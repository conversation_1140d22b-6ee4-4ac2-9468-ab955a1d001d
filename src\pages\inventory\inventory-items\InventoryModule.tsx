import React, { useState } from 'react';
import { InventoryList } from './InventoryList';
import { InventoryForm } from './InventoryForm';
import type { InventoryItem } from '../types/inventory-item.type';
import { Screen } from '@/app-components/layout/screen';

export const InventoryModule: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<InventoryItem | undefined>();

  const handleAdd = () => {
    setEditingItem(undefined);
    setShowForm(true);
  };

  const handleEdit = (item: InventoryItem) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  return (
    <Screen>
      <div className="container mx-auto py-6">
        <InventoryList onEdit={handleEdit} onAdd={handleAdd} />

        {showForm && (
          <InventoryForm
            item={editingItem}
            onClose={handleCloseForm}
            onSuccess={handleFormSuccess}
          />
        )}
      </div>
    </Screen>
  );
};