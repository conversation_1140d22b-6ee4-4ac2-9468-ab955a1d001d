import React, { useState, useEffect, useMemo } from "react";
import { X, Utensils, Loader2, Check } from "lucide-react";
import { useGetMenuGroupsQuery } from "@/redux/slices/menuGroup";
import { useAddMenuSubGroupMutation } from "@/redux/slices/menuSubGroup";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "@/hooks/use-toast";


class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught in CreateMenuSubGroupModal:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-500">
          Something went wrong. Please try again or contact support.
        </div>
      );
    }
    return this.props.children;
  }
}

interface CreateMenuSubGroupModalProps {
  onClose: () => void;
  onSave: (newSubGroup: {
    name: string;
    description: string;
    position: number;
    group: number;
  }) => void;
  selectedMenuGroup?: number | null;
  selectedMainMenu?: number | null;
}

export function CreateMenuSubGroupModal({ 
  onClose, 
  onSave, 
  selectedMenuGroup,
  selectedMainMenu 
}: CreateMenuSubGroupModalProps) {
  const { data: menuGroupsData, isLoading: isLoadingMenuGroups, error: menuGroupsError } = useGetMenuGroupsQuery({});
  const [addMenuSubGroup, { isLoading: isSubmitting }] = useAddMenuSubGroupMutation();

  // Initialize form data
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    position: "1",
    group: selectedMenuGroup?.toString() || "",
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [backendError, setBackendError] = useState<string | null>(null);

  // Log API responses for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("Menu Groups Data:", menuGroupsData);
      if (menuGroupsError) console.error("Menu Groups Error:", menuGroupsError);
    }
  }, [menuGroupsData, menuGroupsError]);

  // Process menu groups - filter by selected main menu if provided
  const filteredMenuGroups = useMemo(() => {
    if (!menuGroupsData?.data?.results) return [];
    
    let groups = menuGroupsData.data.results
      .filter((group: any) => group.id && group.name)
      .map((group: any) => ({
        id: group.id.toString(),
        name: group.name || "Unknown",
        menu: group.menu,
      }));

    // Filter by selected main menu if provided
    if (selectedMainMenu) {
      groups = groups.filter((group: any) => group.menu === selectedMainMenu);
    }

    return groups;
  }, [menuGroupsData, selectedMainMenu]);

  // Log processed data for dropdowns
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("Filtered Menu Groups:", filteredMenuGroups);
      console.log("Selected Main Menu:", selectedMainMenu);
      console.log("Selected Menu Group:", selectedMenuGroup);
    }
  }, [filteredMenuGroups, selectedMainMenu, selectedMenuGroup]);

  // Set default group if selectedMenuGroup changes
  useEffect(() => {
    if (!formData.group && selectedMenuGroup) {
      setFormData((prev) => ({
        ...prev,
        group: selectedMenuGroup.toString(),
      }));
    }
  }, [selectedMenuGroup]);

  const handleInputChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (validationErrors[name]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
    setBackendError(null);

    if (name === "group" && process.env.NODE_ENV === "development") {
      console.log("Selected Menu Group:", value);
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    let isValid = true;

    if (!formData.name.trim()) {
      errors.name = "Name is required";
      isValid = false;
    } else if (formData.name.length > 100) {
      errors.name = "Name cannot exceed 100 characters";
      isValid = false;
    }

    if (!formData.description.trim()) {
      errors.description = "Description is required";
      isValid = false;
    } else if (formData.description.length > 500) {
      errors.description = "Description cannot exceed 500 characters";
      isValid = false;
    }

    if (!formData.position.trim()) {
      errors.position = "Position is required";
      isValid = false;
    } else if (
      isNaN(Number(formData.position)) ||
      Number(formData.position) < -2147483648 ||
      Number(formData.position) > 2147483647
    ) {
      errors.position = "Position must be a valid number between -2147483648 and 2147483647";
      isValid = false;
    }

    if (!formData.group) {
      errors.group = "Menu group is required";
      isValid = false;
    }

    setValidationErrors(errors);
    if (!isValid) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill all required fields correctly",
      });
    }
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      console.warn("Form validation failed:", validationErrors);
      return;
    }

    try {
      const payload = {
        name: formData.name,
        description: formData.description,
        position: parseInt(formData.position),
        group: parseInt(formData.group),
      };

      if (process.env.NODE_ENV === "development") {
        console.log("Submitting Menu SubGroup Payload:", payload);
      }

      const result = await addMenuSubGroup(payload).unwrap();
      console.log("Menu subgroup created successfully:", result);

      onSave({
        name: formData.name,
        description: formData.description,
        position: parseInt(formData.position),
        group: parseInt(formData.group),
      });
      
      toast({
        title: "Success",
        description: "Menu subgroup created successfully!",
      });
      onClose();
    } catch (error: any) {
      console.error("Failed to add menu subgroup:", error);
      const errorMessage =
        error?.data?.detail ||
        error?.data?.message ||
        "Failed to create menu subgroup. Please try again.";
      setBackendError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    }
  };

  // Render loading state if critical data is missing
  if (isLoadingMenuGroups && !menuGroupsData) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl h-[85vh] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
          onClick={onClose}
          aria-label="Close modal"
        />
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-lg h-[85vh] flex flex-col overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
          <div className="relative bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 p-4 text-white flex-shrink-0">
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                    <Utensils className="h-5 w-5" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Create Menu SubGroup</h2>
                    <p className="text-white/90 text-xs">Add a new subgroup to organize menu items within a group</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 rounded-full"
                  disabled={isSubmitting || isLoadingMenuGroups}
                  aria-label="Close modal"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                {backendError && (
                  <div className="p-4 bg-red-50 text-red-500 rounded-md">{backendError}</div>
                )}

                {isSubmitting || isLoadingMenuGroups ? (
                  <div className="flex items-center justify-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : menuGroupsError ? (
                  <div className="text-red-500 text-center">
                    Failed to load menu groups. Please try again.
                  </div>
                ) : (
                  <div className="space-y-8">
                    {/* General Information */}
                    <Card className="border-0 shadow-none">
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold">SubGroup Information</CardTitle>
                      </CardHeader>
                      <CardContent className="p-0 space-y-6">
                        {/* Name */}
                        <div className="space-y-2">
                          <Label htmlFor="name" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-blue-500" />
                            SubGroup Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="name"
                            placeholder="e.g., Appetizers"
                            value={formData.name}
                            onChange={(e) => handleInputChange("name", e.target.value)}
                            className="h-11 text-base"
                            disabled={isSubmitting}
                            aria-required="true"
                          />
                          {validationErrors.name && (
                            <p className="text-sm text-red-500">{validationErrors.name}</p>
                          )}
                        </div>

                        {/* Description */}
                        <div className="space-y-2">
                          <Label htmlFor="description" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-blue-500" />
                            Description <span className="text-red-500">*</span>
                          </Label>
                          <textarea
                            id="description"
                            placeholder="Describe this subgroup..."
                            value={formData.description}
                            onChange={(e) => handleInputChange("description", e.target.value)}
                            className="w-full h-20 px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                            disabled={isSubmitting}
                            aria-required="true"
                          />
                          {validationErrors.description && (
                            <p className="text-sm text-red-500">{validationErrors.description}</p>
                          )}
                        </div>

                        {/* Position */}
                        <div className="space-y-2">
                          <Label htmlFor="position" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-blue-500" />
                            Position <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="position"
                            placeholder="e.g., 1"
                            value={formData.position}
                            onChange={(e) => handleInputChange("position", e.target.value)}
                            className="h-11 text-base"
                            type="number"
                            step="1"
                            min="-2147483648"
                            max="2147483647"
                            disabled={isSubmitting}
                            aria-required="true"
                          />
                          {validationErrors.position && (
                            <p className="text-sm text-red-500">{validationErrors.position}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Group Selection */}
                    <Card className="border-0 shadow-none">
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold">Parent Group</CardTitle>
                      </CardHeader>
                      <CardContent className="p-0 space-y-6">
                        {/* Menu Group */}
                        <div className="space-y-2">
                          <Label htmlFor="group" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-blue-500" />
                            Menu Group <span className="text-red-500">*</span>
                          </Label>
                          {!filteredMenuGroups.length ? (
                            <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                              No menu groups available
                              {selectedMainMenu && " for the selected main menu"}
                            </div>
                          ) : (
                            <Select
                              value={formData.group}
                              onValueChange={(value) => handleInputChange("group", value)}
                              disabled={isSubmitting || filteredMenuGroups.length === 0}
                            >
                              <SelectTrigger className="h-11">
                                <SelectValue placeholder="Select menu group" />
                              </SelectTrigger>
                              <SelectContent>
                                {filteredMenuGroups.map((group) => (
                                  <SelectItem key={group.id} value={group.id}>
                                    {group.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                          {validationErrors.group && (
                            <p className="text-sm text-red-500">{validationErrors.group}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </form>
            </ScrollArea>
          </div>

          <div className="border-t bg-muted/30 p-4 flex-shrink-0">
            <div className="flex justify-between">
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                disabled={isSubmitting}
                aria-label="Cancel and close modal"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting || isLoadingMenuGroups || !filteredMenuGroups.length}
                className="min-w-[100px] bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Create SubGroup
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}