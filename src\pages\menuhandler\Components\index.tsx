import React from "react";
import { MealCard } from "./menuCard";

function App() {
  const sampleMeals = [
    {
      title: "Earl Grey Tea",
      description: "Classic black tea with bergamot oil, served hot with optional lemon and honey",
      imageUrl: "https://images.unsplash.com/photo-1594631661960-487fb4edb78f?w=400&h=300&fit=crop",
      price: "$3.50",
      category: "Beverages",
      rating: 4.8,
      prepTime: "3 mins"
    },
    {
      title: "Scrambled Eggs",
      description: "Fluffy eggs with fresh herbs and butter, served with toast",
      imageUrl: "https://images.unsplash.com/photo-1525351484163-7529414344d8?w=400&h=300&fit=crop",
      price: "$8.50",
      category: "Mains",
      rating: 4.9,
      prepTime: "8 mins"
    },
    {
      title: "Avocado Toast",
      description: "Sourdough bread with smashed avocado, lime, and sea salt",
      imageUrl: "https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400&h=300&fit=crop",
      price: "$12.00",
      category: "Healthy",
      rating: 4.7,
      prepTime: "5 mins"
    }
  ];

  const handleAddToCart = (mealTitle: string) => {
    alert(`Added ${mealTitle} to cart!`);
  };

  const handleViewDetails = (mealTitle: string) => {
    console.log(`Viewing details for ${mealTitle}`);
  };

  return (
    <div className="min-h-screen bg-gray-100 pទ: p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Breakfast Menu</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sampleMeals.map((meal, index) => (
            <MealCard
              key={index}
              title={meal.title}
              description={meal.description}
              imageUrl={meal.imageUrl}
              price={meal.price}
              category={meal.category}
              rating={meal.rating}
              prepTime={meal.prepTime}
              onAddToCart={() => handleAddToCart(meal.title)}
              onViewDetails={() => handleViewDetails(meal.title)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

export default App;