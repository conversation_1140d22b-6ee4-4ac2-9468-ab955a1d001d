import { CheckPermissions, UserRole } from '../Components/types/types';

/**
 * Permission constants for different user roles
 */
export const PERMISSIONS = {
  ADD_ITEMS: 'add_items',
  REMOVE_ITEMS: 'remove_items',
  VOID_ITEMS: 'void_items',
  APPLY_DISCOUNTS: 'apply_discounts',
  TRANSFER_CHECK: 'transfer_check',
  MERGE_CHECKS: 'merge_checks',
  CLOSE_CHECK: 'close_check',
  VIEW_ALL_CHECKS: 'view_all_checks',
  ARCHIVE_CHECKS: 'archive_checks',
  PRINT_CHECKS: 'print_checks',
  MANAGE_TABLES: 'manage_tables',
} as const;

/**
 * Default role permissions
 */
export const DEFAULT_ROLE_PERMISSIONS: Record<string, string[]> = {
  admin: Object.values(PERMISSIONS),
  manager: [
    PERMISSIONS.ADD_ITEMS,
    PERMISSIONS.REMOVE_ITEMS,
    PERMISSIONS.VOID_ITEMS,
    PERMISSIONS.APPLY_DISCOUNTS,
    PERMISSIONS.TRANSFER_CHECK,
    PERMISSIONS.MERGE_CHECKS,
    PERMISSIONS.CLOSE_CHECK,
    PERMISSIONS.VIEW_ALL_CHECKS,
    PERMISSIONS.ARCHIVE_CHECKS,
    PERMISSIONS.PRINT_CHECKS,
  ],
  waiter: [
    PERMISSIONS.ADD_ITEMS,
    PERMISSIONS.REMOVE_ITEMS,
    PERMISSIONS.CLOSE_CHECK,
    PERMISSIONS.PRINT_CHECKS,
  ],
  cashier: [
    PERMISSIONS.VIEW_ALL_CHECKS,
    PERMISSIONS.CLOSE_CHECK,
    PERMISSIONS.PRINT_CHECKS,
    PERMISSIONS.APPLY_DISCOUNTS,
  ],
};

/**
 * Get user permissions based on role
 */
export const getUserPermissions = (userRole: string | UserRole): CheckPermissions => {
  const permissions = typeof userRole === 'string' 
    ? DEFAULT_ROLE_PERMISSIONS[userRole.toLowerCase()] || []
    : userRole.permissions || [];

  return {
    canAddItems: permissions.includes(PERMISSIONS.ADD_ITEMS),
    canRemoveItems: permissions.includes(PERMISSIONS.REMOVE_ITEMS),
    canVoidItems: permissions.includes(PERMISSIONS.VOID_ITEMS),
    canApplyDiscounts: permissions.includes(PERMISSIONS.APPLY_DISCOUNTS),
    canTransferCheck: permissions.includes(PERMISSIONS.TRANSFER_CHECK),
    canMergeChecks: permissions.includes(PERMISSIONS.MERGE_CHECKS),
    canCloseCheck: permissions.includes(PERMISSIONS.CLOSE_CHECK),
    canViewAllChecks: permissions.includes(PERMISSIONS.VIEW_ALL_CHECKS),
    canArchiveChecks: permissions.includes(PERMISSIONS.ARCHIVE_CHECKS),
  };
};

/**
 * Check if user has specific permission
 */
export const hasPermission = (userRole: string | UserRole, permission: string): boolean => {
  const permissions = typeof userRole === 'string' 
    ? DEFAULT_ROLE_PERMISSIONS[userRole.toLowerCase()] || []
    : userRole.permissions || [];
  
  return permissions.includes(permission);
};