import { apiSlice } from "../apiSlice";

export const costCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getGuestChecks: builder.query({
      query: (params) => ({
        url: "/guest-checks",
        method: "GET",
        params,
        
      }),
    }),

    retrieveGuest: builder.query({
      query: (id) => ({
        url: `/guest-checks/${id}`,
        method: "GET",
      }),
    }),

    addGuestCheck: builder.mutation({
      query: (payload) => ({
        url: "/guest-checks", // Removed ID from URL, as POST typically creates a new resource
        method: "POST",
        body: payload,
      }),
    }),

    deleteGuestCheck: builder.mutation({
      query: (id) => ({
        url: `/guest-checks/${id}`,
        method: "DELETE",
      }),
    }),

    patchGuest: builder.mutation({
      query: (payload) => ({
        url: `/guest-checks/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
    useGetGuestChecksQuery,
    useLazyGetGuestChecksQuery,
    useRetrieveGuestQuery,
    useLazyRetrieveGuestQuery,
    useAddGuestCheckMutation,
    useDeleteGuestCheckMutation,
    usePatchGuestMutation,
    
    
} = costCenterApiSlice;
