export interface ProductMainCategoryItem {
    id: number;
    name: string;
    code: string;
    description: string | null;
}

export interface ProductMainCategoryApiResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: ProductMainCategoryItem[];
}

export interface CreateProductMainCategoryItemRequest {
    name: string;
    code: string;
    description: string | null;
}