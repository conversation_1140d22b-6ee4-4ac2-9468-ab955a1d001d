import React from "react";
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Di<PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Eye, 
  BarChart, 
  Award, 
  Users, 
  Package, 
  Calendar,
  User,
  FileText,
  CheckCircle,
  Clock
} from "lucide-react";
import { useGetBidAnalysisQuery } from "@/redux/slices/procurement";

interface ViewBidAnalysisProps {
  open: boolean;
  onClose: () => void;
  bidAnalysis: any;
}

const ViewBidAnalysis: React.FC<ViewBidAnalysisProps> = ({
  open,
  onClose,
  bidAnalysis,
}) => {
  // Fetch detailed bid analysis data
  const { data: bidAnalysisDetail, isLoading } = useGetBidAnalysisQuery(bidAnalysis?.id, {
    skip: !bidAnalysis?.id
  });

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleString();
  };

  const formatCurrency = (amount: number | string) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES'
    }).format(num || 0);
  };

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading bid analysis details...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const analysis = bidAnalysisDetail || bidAnalysis;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Bid Analysis Details - {analysis?.code}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center gap-2">
              <BarChart className="h-5 w-5" />
              Basic Information
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Code</label>
                <p className="text-lg font-semibold text-gray-900">{analysis?.code}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">RFQ</label>
                <p className="text-lg font-semibold text-blue-600">{analysis?.rfq}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Created By</label>
                <p className="text-lg font-semibold text-gray-900 flex items-center gap-1">
                  <User className="h-4 w-4" />
                  {analysis?.created_by}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Award Type</label>
                <Badge variant={analysis?.split_award ? "default" : "secondary"} className="text-sm">
                  {analysis?.split_award ? (
                    <>
                      <Users className="mr-1 h-3 w-3" />
                      Split Award
                    </>
                  ) : (
                    <>
                      <Award className="mr-1 h-3 w-3" />
                      Single Award
                    </>
                  )}
                </Badge>
              </div>
            </div>
          </div>

          {/* Status Information */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Status Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Status</label>
                <div className="mt-1">
                  {analysis?.finalized_at ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Finalized
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-orange-600">
                      <Clock className="mr-1 h-3 w-3" />
                      Draft
                    </Badge>
                  )}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Finalized At</label>
                <p className="text-gray-900">{formatDate(analysis?.finalized_at)}</p>
              </div>
            </div>
          </div>

          {/* Selected Response (Single Award) */}
          {!analysis?.split_award && (
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
                <Award className="h-5 w-5" />
                Selected Response
              </h3>
              <div>
                <label className="text-sm font-medium text-gray-600">Winning Response</label>
                {analysis?.selected_responses ? (
                  <div className="mt-2">
                    <Badge variant="outline" className="text-lg px-3 py-1">
                      <FileText className="mr-2 h-4 w-4" />
                      {analysis.selected_responses}
                    </Badge>
                  </div>
                ) : (
                  <p className="text-gray-500 italic mt-1">No response selected yet</p>
                )}
              </div>
            </div>
          )}

          {/* Bid Lines (Split Award) */}
          {analysis?.split_award && analysis?.bid_lines && (
            <div className="bg-purple-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center gap-2">
                <Users className="h-5 w-5" />
                Bid Lines - Supplier Assignments
              </h3>
              <div className="space-y-4">
                {analysis.bid_lines.map((line: any, index: number) => (
                  <div key={line.id} className="bg-white p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-3">
                      <Package className="h-4 w-4 text-purple-600" />
                      <h4 className="font-medium">Bid Line {index + 1}</h4>
                      {line.supplier && (
                        <Badge variant="outline">{line.supplier}</Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <label className="font-medium text-gray-600">Unit Price</label>
                        <p className="text-gray-900">{formatCurrency(line.unit_price)}</p>
                      </div>
                      <div>
                        <label className="font-medium text-gray-600">Quantity Awarded</label>
                        <p className="text-gray-900">{line.quantity_awarded || 0}</p>
                      </div>
                      <div>
                        <label className="font-medium text-gray-600">Delivery Days</label>
                        <p className="text-gray-900">{line.delivery_time_days || 0} days</p>
                      </div>
                      <div>
                        <label className="font-medium text-gray-600">Total Price</label>
                        <p className="text-gray-900 font-semibold">{formatCurrency(line.total_price)}</p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Total Summary */}
                <div className="bg-purple-100 p-4 rounded-lg border-2 border-purple-200">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-purple-900">Total Award Value:</span>
                    <span className="text-xl font-bold text-purple-900">
                      {formatCurrency(
                        analysis.bid_lines.reduce((sum: number, line: any) => 
                          sum + (parseFloat(line.total_price) || 0), 0
                        )
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Recommendation Notes */}
          <div className="bg-yellow-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-900 mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Recommendation Notes
            </h3>
            <div className="bg-white p-4 rounded border">
              {analysis?.recommendation_notes ? (
                <p className="text-gray-900 whitespace-pre-wrap">{analysis.recommendation_notes}</p>
              ) : (
                <p className="text-gray-500 italic">No recommendation notes provided</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={onClose} variant="outline">
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewBidAnalysis;
