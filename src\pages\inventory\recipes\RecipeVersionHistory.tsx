import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  GitCompare,
  History,
  Eye,
  RotateCcw,
  Clock,
  AlertCircle,
  Users,
  Shield,
  ChevronLeft,
  Notebook
} from 'lucide-react';
import { useGetRecipeVersionQuery, useCreateRecipeVersionMutation } from '@/redux/slices/recipe-version';
import { format } from 'date-fns';
import { useUpdateRecipeMutation } from '@/redux/slices/recipe-data';
import { Recipe } from '../types/recipe-type';

interface RecipeVersionHistoryProps {
  recipe: Recipe;
  onClose: () => void;
  onCompareVersions: () => void;
}

export const RecipeVersionHistory: React.FC<RecipeVersionHistoryProps> = ({
  recipe,
  onClose,
  onCompareVersions
}) => {
  const [selectedVersions, setSelectedVersions] = useState<number[]>([]);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [selectedVersionForAssignment, setSelectedVersionForAssignment] = useState<number | null>(null);
  const [showRevertModal, setShowRevertModal] = useState(false);
  const [selectedVersionForRevert, setSelectedVersionForRevert] = useState<number | null>(null);
  const [revertReason, setRevertReason] = useState('');
  const [assignmentUserId, setAssignmentUserId] = useState('');
  const [assignmentRole, setAssignmentRole] = useState('');

  const { data: versionsData, isLoading } = useGetRecipeVersionQuery({
    params: { recipe: recipe.id }
  });
  const [createRecipeVersion] = useCreateRecipeVersionMutation();
  const [updateRecipe] = useUpdateRecipeMutation();

  const versions = versionsData?.results || [];

  // Check if recipe has been recently modified (within last 24 hours)
  const isRecentlyModified = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    return diffInHours <= 24;
  };

  // Get role badge color
  const getRoleBadgeColor = (role: string) => {
    switch (role?.toLowerCase()) {
      case 'chef': return 'bg-purple-100 text-purple-800';
      case 'kitchen manager': return 'bg-blue-100 text-blue-800';
      case 'sous chef': return 'bg-green-100 text-green-800';
      case 'admin': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleVersionSelect = (versionId: number) => {
    setSelectedVersions(prev => {
      if (prev.includes(versionId)) {
        return prev.filter(id => id !== versionId);
      } else if (prev.length < 2) {
        return [...prev, versionId];
      } else {
        return [prev[1], versionId];
      }
    });
  };

  const handleRevertToVersion = (versionId: number) => {
    setSelectedVersionForRevert(versionId);
    setShowRevertModal(true);
  };

  const confirmRevert = async () => {
    if (!selectedVersionForRevert || !revertReason.trim()) return;

    const version = versions.find(v => v.id === selectedVersionForRevert);
    if (!version) return;

    try {
      // Create a new version based on the selected version's snapshot
      const newVersionNumber = `${versions.length + 1}.0`;
      await createRecipeVersion({
        data_snapshot: version.data_snapshot,
        version_number: newVersionNumber,
        recipe: recipe.id.toString(),
        // change_notes: `Reverted to version ${version.version_number}. Reason: ${revertReason}`,
        // revert_reason: revertReason,
        // previous_version_id: version.id
      }).unwrap();

      // Update the recipe with the reverted data
      await updateRecipe({
        id: recipe.id,
        body: {
          ...version.data_snapshot,
          // last_modified: new Date().toISOString(),
          // modified_by: 'current_user' // This should come from auth context
        }
      }).unwrap();

      alert('Successfully reverted to previous version!');
      setShowRevertModal(false);
      setRevertReason('');
      setSelectedVersionForRevert(null);
    } catch (error) {
      console.error('Failed to revert version:', error);
      alert('Failed to revert to previous version');
    }
  };

  const handleAssignRecipe = () => {
    setShowAssignmentModal(true);
  };

  const confirmAssignment = async () => {
    if (!assignmentUserId.trim() || !assignmentRole.trim()) return;

    try {
      await updateRecipe({
        id: recipe.id,
        body: {
          // assigned_to: assignmentUserId,
          // assigned_role: assignmentRole,
          // last_modified: new Date().toISOString(),
          // modified_by: 'current_user' // This should come from auth context
        }
      }).unwrap();

      alert('Recipe assignment updated successfully!');
      setShowAssignmentModal(false);
      setAssignmentUserId('');
      setAssignmentRole('');
    } catch (error) {
      console.error('Failed to assign recipe:', error);
      alert('Failed to assign recipe');
    }
  };

  const getChangesSummary = (version: any, previousVersion?: any) => {
    const changes: any[] = [];

    if (!version.data_snapshot) return changes;

    const current = version.data_snapshot;
    const previous = previousVersion?.data_snapshot || {};

    // Compare basic fields
    if (current.name !== previous.name) changes.push('Recipe Name');
    if (current.recipe_type !== previous.recipe_type) changes.push('Recipe Type');
    if (current.portion_size !== previous.portion_size) changes.push('Portion Size');
    if (current.preparation_time !== previous.preparation_time) changes.push('Prep Time');
    if (current.cooking_time !== previous.cooking_time) changes.push('Cook Time');
    if (current.instructions !== previous.instructions) changes.push('Instructions');
    if (current.tools_required !== previous.tools_required) changes.push('Tools');
    if (current.labor_cost !== previous.labor_cost) changes.push('Labor Cost');
    if (current.packaging_cost !== previous.packaging_cost) changes.push('Packaging Cost');

    // Compare ingredients
    const currentIngredients = JSON.stringify(current.ingredients || []);
    const previousIngredients = JSON.stringify(previous.ingredients || []);
    if (currentIngredients !== previousIngredients) changes.push('Ingredients');

    // Compare dietary flags
    const currentDietary = JSON.stringify(current.dietary_flags || []);
    const previousDietary = JSON.stringify(previous.dietary_flags || []);
    if (currentDietary !== previousDietary) changes.push('Dietary Info');

    return changes;
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading version history...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <ChevronLeft size={50} className='cursor-pointer' onClick={onClose} />
          <div>
            <h1 className="text-3xl font-bold">Version History</h1>
            <p className="text-muted-foreground">Recipe: {recipe.name}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleAssignRecipe}
            className='cursor-pointer'
          >
            <Users className="h-4 w-4 mr-2" />
            Assign Recipe
          </Button>
          <Button
            variant="outline"
            onClick={onCompareVersions}
            disabled={selectedVersions.length !== 2}
            className='cursor-pointer'
          >
            <GitCompare className="h-4 w-4 mr-2" />
            Compare Selected ({selectedVersions.length}/2)
          </Button>
        </div>
      </div>

      {/* Recipe Assignment Status */}
      {(recipe.assigned_to || recipe.assigned_role) && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                Recipe Assignment:
              </span>
              {recipe.assigned_role && (
                <Badge className={getRoleBadgeColor(recipe.assigned_role)}>
                  {recipe.assigned_role}
                </Badge>
              )}
              {recipe.assigned_to && (
                <span className="text-sm text-blue-700">
                  Assigned to: {recipe.assigned_to}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recently Modified Flag */}
      {recipe.is_recently_modified && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">
                Recently Modified
              </span>
              <span className="text-sm text-orange-700">
                This recipe has been modified within the last 24 hours
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <div className="p-4 border rounded-lg">
        <p className="text-sm text-muted-foreground">
          Select up to 2 versions to compare changes.
        </p>
      </div>

      {/* Version History Table */}
      <div className=' space-y-3'>
        <div className="flex items-center gap-2">
          <History className="h-5 w-5 translate-y-0.5" />
          <p className='font-semibold'>Version History</p>
        </div>
        <div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow className='font-bold'>
                  <TableHead className="w-12 font-bold">Select</TableHead>
                  <TableHead className='font-bold'>Version</TableHead>
                  <TableHead className='font-bold'>Date Created</TableHead>
                  <TableHead className='font-bold'>Changes</TableHead>
                  <TableHead className='font-bold'>Status</TableHead>
                  <TableHead className='font-bold'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {versions.map((version, index) => {
                  const isSelected = selectedVersions.includes(version.id);
                  const changes = getChangesSummary(version);
                  const isCurrentVersion = index === 0; // Assuming first version is current

                  return (
                    <TableRow
                      key={version.id}
                      className={isSelected ? 'bg-muted/20' : ''}
                    >
                      <TableCell className='text-center'>
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleVersionSelect(version.id)}
                          className="rounded align-middle border-gray-300 h-4 w-4"
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        V{version.version_number}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-5 w-5" />
                          {format(new Date(version.created_at), 'MMM dd, yyyy HH:mm')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {changes.length > 0 ? (
                            changes.map((change, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {change}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-muted-foreground text-sm">No changes</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {isCurrentVersion ? (
                          <Badge variant="default" className='px-4 py-1'>Current</Badge>
                        ) : (
                          <Badge variant="secondary">Historical</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {!isCurrentVersion && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRevertToVersion(version.id)}
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {versions.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No version history available</p>
              <p className="text-sm">Versions will appear here when the recipe is modified</p>
            </div>
          )}
        </div>
      </div>

      {/* Recent Changes Summary */}
      {versions.length > 0 && (
        <div className=' space-y-3'>
          <div className="flex items-center gap-2">
            <Notebook className="h-5 w-5 translate-y-0.5" />
            <p className='font-semibold'>Recent Activity</p>
          </div>
          <div>
            <div className="space-y-3">
              {versions.slice(0, 5).map((version, index) => (
                <div key={version.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Version {version.version_number}</p>
                    <p className="text-sm text-muted-foreground">
                      Created {format(new Date(version.created_at), 'MMM dd, yyyy')}
                    </p>
                  </div>
                  <Badge variant={index === 0 ? 'default' : 'secondary'} className='px-4 py-1'>
                    {index === 0 ? 'Current' : 'Historical'}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Assignment Modal */}
      {showAssignmentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Assign Recipe
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">User ID</label>
                <Input
                  value={assignmentUserId}
                  onChange={(e) => setAssignmentUserId(e.target.value)}
                  placeholder="Enter user ID or email"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Role</label>
                <Select value={assignmentRole} onValueChange={setAssignmentRole}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chef">Chef</SelectItem>
                    <SelectItem value="kitchen manager">Kitchen Manager</SelectItem>
                    <SelectItem value="sous chef">Sous Chef</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2 pt-4">
                <Button
                  onClick={confirmAssignment}
                  disabled={!assignmentUserId.trim() || !assignmentRole.trim()}
                  className="flex-1"
                >
                  Assign Recipe
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowAssignmentModal(false);
                    setAssignmentUserId('');
                    setAssignmentRole('');
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Revert Modal */}
      {showRevertModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RotateCcw className="h-5 w-5" />
                Revert to Previous Version
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-4">
                  You are about to revert to version {versions.find(v => v.id === selectedVersionForRevert)?.version_number}.
                  This will create a new version with the previous data.
                </p>
                <label className="block text-sm font-medium mb-2">Reason for revert (required)</label>
                <textarea
                  value={revertReason}
                  onChange={(e) => setRevertReason(e.target.value)}
                  placeholder="Explain why you're reverting to this version..."
                  className="w-full p-2 border rounded-md resize-none"
                  rows={3}
                />
              </div>
              <div className="flex gap-2 pt-4">
                <Button
                  onClick={confirmRevert}
                  disabled={!revertReason.trim()}
                  variant="destructive"
                  className="flex-1"
                >
                  Confirm Revert
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowRevertModal(false);
                    setRevertReason('');
                    setSelectedVersionForRevert(null);
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};