import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Loader, Save } from 'lucide-react';
import { useRetrieveUserQuery, usePatchUserMutation } from '@/redux/slices/user';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetUserRolesQuery } from '@/redux/slices/userRoles';
import { toast } from '@/components/custom/Toast/MyToast';
import { Skeleton } from '@/components/ui/skeleton';
import { useEffect } from 'react';

// Form schema for user editing
const editUserFormSchema = z.object({
  username: z.string().min(3, { message: "Username is required (min 3 chars)." }),
  employee_no: z.string().min(1, { message: "Employee number is required." }),
  first_name: z.string().min(2, { message: "First name is required." }),
  last_name: z.string().min(2, { message: "Last name is required." }),
  email: z.string().email({ message: "Invalid email address." }),
  role: z.string().min(1, { message: "Role selection is required." }),
  branch: z.string().min(1, { message: "Branch selection is required." }),
  phone: z.string().optional(),
});

type EditUserFormData = z.infer<typeof editUserFormSchema>;

export default function EditUser() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // API hooks
  const { data: user, isLoading: loadingUser, error: userError } = useRetrieveUserQuery(Number(id));
  const [updateUser, { isLoading: isUpdating }] = usePatchUserMutation();
  const { data: branches = [], isLoading: loadingBranches } = useGetBranchesQuery({});
  const { data: userRoles = [], isLoading: loadingUserRoles } = useGetUserRolesQuery({});

  const form = useForm<EditUserFormData>({
    resolver: zodResolver(editUserFormSchema),
    defaultValues: {
      username: '',
      employee_no: '',
      first_name: '',
      last_name: '',
      email: '',
      role: '',
      branch: '',
      phone: '',
      pin: '',
    },
  });

  // Update form when user data loads
  useEffect(() => {
    if (user) {
      form.reset({
        username: user.username || '',
        employee_no: user.employee_no || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        role: user.role?.toString() || '',
        branch: user.branch?.toString() || '',
        phone: user.phone || '',
        pin: '', // Don't pre-fill PIN for security
      });
    }
  }, [user, form]);

  const onSubmit = async (data: EditUserFormData) => {
    if (!id) return;

    try {
      const updatePayload = {
        username: data.username,
        employee_no: data.employee_no,
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        role: parseInt(data.role),
        branch: parseInt(data.branch),
        phone: data.phone || null,
        pin: data.pin ? parseInt(data.pin) : null,
      };

      await updateUser({ id: Number(id), data: updatePayload }).unwrap();
      toast.success('User updated successfully!');
      navigate(`/admin/users/${id}`);
    } catch (error: any) {
      const { data } = error || {};
      
      // Handle field-specific errors
      if (data && typeof data === 'object') {
        const fieldErrors = [];
        const fields = ['username', 'employee_no', 'first_name', 'last_name', 'email', 'role', 'branch'];
        
        for (const field of fields) {
          if (data[field] && Array.isArray(data[field])) {
            fieldErrors.push(`${field.replace('_', ' ')}: ${data[field][0]}`);
          }
        }
        
        if (fieldErrors.length > 0) {
          toast.error(fieldErrors[0]);
        } else {
          toast.error('Failed to update user. Please check your information and try again.');
        }
      } else {
        toast.error('Failed to update user. Please try again.');
      }
    }
  };

  if (loadingUser) {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  if (userError || !user) {
    return (
      <div className="container mx-auto py-6">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading User</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              {userError ? 'Failed to load user details.' : 'User not found.'}
            </p>
            <Button 
              variant="outline" 
              onClick={() => navigate('/admin/users')}
              className="mt-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/admin/users/${id}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to User Details
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit User</h1>
            <p className="text-muted-foreground">Update user information</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter username" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="employee_no"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employee Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter employee number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Enter email address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>PIN (Optional - leave blank to keep current)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter new PIN" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Role and Branch Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="branch"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a branch" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loadingBranches ? (
                            <SelectItem value="loading" disabled>Loading branches...</SelectItem>
                          ) : Array.isArray(branches) ? (
                            branches.map((branch) => (
                              <SelectItem key={branch.id} value={branch.id?.toString() || ''}>
                                {branch.name} ({branch.branch_code})
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loadingUserRoles ? (
                            <SelectItem value="loading" disabled>Loading roles...</SelectItem>
                          ) : Array.isArray(userRoles) && userRoles.length > 0 ? (
                            userRoles.map((role) => (
                              <SelectItem key={role.id} value={role.id?.toString() || ''}>
                                {role.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="1">Default Role</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(`/admin/users/${id}`)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? (
                <span className='flex items-center justify-center gap-2'>
                  <Loader className="animate-spin" size={22} />
                  Updating...
                </span>
              ) : (
                <span className='flex items-center justify-center gap-2'>
                  <Save size={22} />
                  Update User
                </span>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
