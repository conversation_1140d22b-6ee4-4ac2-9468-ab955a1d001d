import { CreateProductItemRequest, ProductApiResponse, ProductItem } from "@/pages/inventory/types/product.type";
import { apiSlice } from "../apiSlice";


export const productApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getProduct: builder.query<ProductApiResponse, {params:{}}>({
            query: (params) => ({
                url: `/inventory/products`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<ProductApiResponse>) => response.data,
            providesTags: ["/inventory/products"],
        }),
        updateProduct: builder.mutation<ProductItem, { id: number; body: Partial<CreateProductItemRequest> }>({
            query: ({ id, body }) => ({
                url: `/inventory/products/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/inventory/products"],
        }),
        createProduct: builder.mutation<ProductItem, CreateProductItemRequest>({
            query: (body) => ({
                url: `/inventory/products`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/inventory/products"],
        }),
        getOneProduct: builder.query<ProductItem, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/inventory/products/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/inventory/products"],
        }),
        deleteProduct: builder.mutation<void, number>({
            query: (id) => ({
                url: `/inventory/products/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/inventory/products"],
        }),
    })
})

export const {
    useCreateProductMutation,
    useGetProductQuery,
    useUpdateProductMutation,
    useGetOneProductQuery,
    useDeleteProductMutation,
} = productApiSlice

interface ApiResponse<T> {
  data: T;
  message: string;
}