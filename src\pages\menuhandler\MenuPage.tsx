import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { CreateMenuItemModal } from "./Components/AddFoodModal";
import { CreateMainMenuModal } from "./Components/menuCard";
import { useGetMenuGroupsQuery } from "@/redux/slices/menuGroup";
import { 
  useGetMenusQuery, 
   
  usePatchMenuMutation
} from "@/redux/slices/menuMake";
import { useGetMainMenusQuery } from "@/redux/slices/mainMenu";
import { useGetMenuSubGroupsQuery } from "@/redux/slices/menuSubGroup";
import { CreateMenuGroupModal } from "./Components/GroupModal";
import { ChevronRight, ChevronDown, Edit, Archive, MoreVertical, Move, Eye, EyeOff } from "lucide-react";
import { CreateMenuSubGroupModal } from "./MenuGroup/SubGroup";

// Interfaces (unchanged)
interface MainMenu {
  id: number;
  name: string;
  is_active: boolean;
}

interface MenuGroup {
  id: number;
  name: string;
  menu: number | null;
  sales_category?: string;
  position?: number;
  image_url?: string;
  subgroups?: MenuSubGroup[];
  itemCount?: number;
  mainMenuName?: string;
  mainMenuId?: number | null;
}

interface MenuSubGroup {
  id: number;
  name: string;
  description?: string;
  position?: number;
  group: number;
}

interface MenuItem {
  id: number;
  name: string;
  description?: string;
  price?: number;
  menu_group: number;
  menu_subgroup?: number;
  menu_group_name?: string;
  rating?: number;
  prep_time?: string;
  is_active?: boolean;
  image_url?: string;
  position?: number;
}

// Consolidated image mapping
const getGroupImage = (groupId: number, imageUrl?: string): string => {
  const imageMap = {
    41: "https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?auto=format&fit=crop&w=400&q=80",
    42: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?auto=format&fit=crop&w=400&q=80",
    43: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?auto=format&fit=crop&w=400&q=80",
    44: "https://images.unsplash.com/photo-1622543925917-763c34d1a86e?auto=format&fit=crop&w=400&q=80",
    45: "https://images.unsplash.com/photo-1608270586620-248524c67de9?auto=format&fit=crop&w=400&q=80",
    46: "https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=400&q=80",
  };
  return imageUrl || imageMap[groupId as keyof typeof imageMap] || imageMap[46];
};

// Simplified ErrorBoundary
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean; error: any }> {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error: any) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center py-12">
          <h2 className="text-lg font-medium text-gray-900 mb-2">Something went wrong.</h2>
          <p className="text-red-500">{this.state.error?.message || "Unknown error"}</p>
        </div>
      );
    }
    return this.props.children;
  }
}

// Simplified MenuItemActionsModal
const MenuItemActionsModal: React.FC<{
  item: MenuItem;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (itemId: number, updates: Partial<MenuItem>) => void;
  onArchive: (itemId: number) => void;
  menuGroups: MenuGroup[];
  menuSubGroups: MenuSubGroup[];
}> = ({ item, isOpen, onClose, onUpdate, onArchive, menuGroups, menuSubGroups }) => {
  const [selectedGroup, setSelectedGroup] = useState(item.menu_group);
  const [selectedSubGroup, setSelectedSubGroup] = useState(item.menu_subgroup || '');
  const [position, setPosition] = useState(item.position || 0);
  const [isActive, setIsActive] = useState(item.is_active !== false);

  const availableSubGroups = useMemo(
    () => menuSubGroups.filter((sg) => sg.group === selectedGroup),
    [menuSubGroups, selectedGroup]
  );

  const handleSave = () => {
    onUpdate(item.id, {
      menu_group: selectedGroup,
      menu_subgroup: selectedSubGroup ? Number(selectedSubGroup) : undefined,
      position,
      is_active: isActive,
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold mb-4">Edit {item.name}</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Menu Group</label>
            <select
              value={selectedGroup}
              onChange={(e) => {
                setSelectedGroup(Number(e.target.value));
                setSelectedSubGroup('');
              }}
              className="w-full p-2 border rounded-md focus:ring-orange-500 focus:border-orange-500"
            >
              {menuGroups.map((group) => (
                <option key={group.id} value={group.id}>{group.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Menu SubGroup</label>
            <select
              value={selectedSubGroup}
              onChange={(e) => setSelectedSubGroup(e.target.value)}
              className="w-full p-2 border rounded-md focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="">No SubGroup</option>
              {availableSubGroups.map((subgroup) => (
                <option key={subgroup.id} value={subgroup.id}>{subgroup.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Position</label>
            <input
              type="number"
              value={position}
              onChange={(e) => setPosition(Number(e.target.value))}
              min="0"
              className="w-full p-2 border rounded-md focus:ring-orange-500 focus:border-orange-500"
            />
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isActive"
              checked={isActive}
              onChange={(e) => setIsActive(e.target.checked)}
              className="rounded text-orange-600 focus:ring-orange-500"
            />
            <label htmlFor="isActive" className="text-sm font-medium text-gray-700">Active</label>
          </div>
        </div>
        <div className="flex justify-between mt-6">
          <button
            onClick={() => {
              onArchive(item.id);
              onClose();
            }}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
          >
            <Archive className="h-4 w-4 inline mr-2" /> Archive
          </button>
          <div className="flex space-x-2">
            <button onClick={onClose} className="px-4 py-2 bg-gray-300 rounded-md hover:bg-gray-400">
              Cancel
            </button>
            <button onClick={handleSave} className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600">
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Simplified MealCard
const MealCard: React.FC<{
  title: string;
  description: string;
  imageUrl: string;
  price: string;
  category: string;
  rating: number;
  prepTime: string;
  onEdit: () => void;
  isActive?: boolean;
  position?: number;
}> = ({ title, description, imageUrl, price, category, rating, prepTime, onEdit, isActive = true, position }) => (
  <div className={`bg-white rounded-xl shadow-lg hover:shadow-xl relative ${!isActive ? 'opacity-60' : ''}`}>
    <div className="absolute top-2 left-2 flex space-x-1">
      {!isActive && (
        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
          <EyeOff className="h-3 w-3 mr-1" /> Inactive
        </span>
      )}
      {position !== undefined && (
        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">#{position}</span>
      )}
    </div>
    <button
      onClick={onEdit}
      className="absolute top-2 right-2 p-2 bg-white/80 rounded-full hover:bg-white shadow-sm"
    >
      <Edit className="h-4 w-4 text-gray-600" />
    </button>
    <img src={imageUrl} alt={title} className="w-full h-32 object-cover rounded-t-xl" />
    <div className="p-4">
      <h3 className="text-lg font-bold">{title}</h3>
      <p className="text-gray-600 text-sm">{description}</p>
      <p className="text-gray-600 text-sm">Category: {category}</p>
      <p className="text-orange-600 font-medium">{price}</p>
      <p className="text-gray-500 text-sm">Rating: {rating}/5</p>
      <p className="text-gray-500 text-sm">Prep Time: {prepTime}</p>
    </div>
  </div>
);

// Simplified MenuGroupCard
const MenuGroupCard: React.FC<{
  group: MenuGroup;
  itemCount: number;
  mainMenuName?: string;
  onGroupClick?: (groupId: number) => void;
  onSubGroupClick?: (subGroupId: number) => void;
  onAddSubGroupClick?: (groupId: number) => void;
  expandedGroups: Set<number>;
  onToggleExpand: (groupId: number) => void;
}> = ({ group, itemCount, mainMenuName, onGroupClick, onSubGroupClick, onAddSubGroupClick, expandedGroups, onToggleExpand }) => {
  const isExpanded = expandedGroups.has(group.id);
  const hasSubGroups = group.subgroups && group.subgroups.length > 0;

  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl">
      <div className="relative h-32">
        <img
          src={getGroupImage(group.id, group.image_url)}
          alt={group.name}
          className="w-full h-full object-cover rounded-t-xl"
          onError={(e) => (e.currentTarget.src = getGroupImage(46))}
        />
        <div className="absolute inset-0 bg-black/40 flex items-center justify-center rounded-t-xl">
          <h3 className="text-white text-xl font-bold">{group.name}</h3>
        </div>
      </div>
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-600">{itemCount} items</span>
          <button
            onClick={() => onGroupClick && onGroupClick(group.id)}
            className="text-orange-600 hover:text-orange-700"
          >
            View Items →
          </button>
        </div>
        {mainMenuName && (
          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded mb-3">{mainMenuName}</div>
        )}
        <div className="border-t pt-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {hasSubGroups && (
                <button onClick={() => onToggleExpand(group.id)} className="p-1 hover:bg-gray-100 rounded">
                  {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </button>
              )}
              <span className="text-sm font-medium text-gray-700">SubGroups ({group.subgroups?.length || 0})</span>
            </div>
            <button
              onClick={() => onAddSubGroupClick && onAddSubGroupClick(group.id)}
              className="text-xs text-blue-600 hover:text-blue-700"
            >
              + Add
            </button>
          </div>
          {isExpanded && hasSubGroups && (
            <div className="mt-2 space-y-1">
              {group.subgroups!.map((subgroup) => (
                <div
                  key={subgroup.id}
                  className="flex justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 cursor-pointer"
                  onClick={() => onSubGroupClick && onSubGroupClick(subgroup.id)}
                >
                  <div>
                    <div className="text-sm font-medium text-gray-800">{subgroup.name}</div>
                    {subgroup.description && (
                      <div className="text-xs text-gray-500 truncate">{subgroup.description}</div>
                    )}
                  </div>
                  <ChevronRight className="h-3 w-3 text-gray-400" />
                </div>
              ))}
            </div>
          )}
          {isExpanded && !hasSubGroups && (
            <div className="mt-2 text-xs text-gray-500 text-center py-2">No subgroups yet</div>
          )}
        </div>
      </div>
    </div>
  );
};

// Simplified MainMenuTabs
const MainMenuTabs: React.FC<{
  mainMenus: MainMenu[];
  selectedMainMenu: number | null;
  onSelectMainMenu: (mainMenuId: number | null) => void;
}> = ({ mainMenus, selectedMainMenu, onSelectMainMenu }) => (
  <div className="mb-6">
    <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
      <button
        onClick={() => onSelectMainMenu(null)}
        className={`px-4 py-2 rounded-lg text-sm font-medium ${selectedMainMenu === null ? "bg-white text-gray-900" : "text-gray-600"}`}
      >
        All Menus
      </button>
      {mainMenus.map((menu) => (
        <button
          key={menu.id}
          onClick={() => onSelectMainMenu(menu.id)}
          className={`px-4 py-2 rounded-lg text-sm font-medium ${selectedMainMenu === menu.id ? "bg-white text-gray-900" : "text-gray-600"}`}
        >
          {menu.name}
        </button>
      ))}
    </div>
  </div>
);

// Simplified LoadingSpinner
const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500"></div>
  </div>
);

// Simplified EmptyState
const EmptyState: React.FC<{
  type: "groups" | "items" | "subgroups";
  onAddClick: () => void;
  mainMenuName?: string | null;
}> = ({ type, onAddClick, mainMenuName }) => (
  <div className="text-center py-12">
    <h3 className="text-lg font-medium text-gray-900 mb-2">
      No {type} found{mainMenuName && ` in ${mainMenuName}`}
    </h3>
    <button
      onClick={onAddClick}
      className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600"
    >
      Add {type === "groups" ? "Menu Group" : type === "subgroups" ? "SubGroup" : "Menu Item"}
    </button>
  </div>
);

const FoodMenu: React.FC = () => {
  // API Queries and Mutations
  const { data: mainMenuData, isLoading: isLoadingMainMenu, refetch: refetchMainMenu } = useGetMainMenusQuery({});
  const { data: menuGroupsData, isLoading: isLoadingGroups, refetch: refetchGroups } = useGetMenuGroupsQuery({});
  const { data: menuSubGroupsData, isLoading: isLoadingSubGroups, refetch: refetchSubGroups } = useGetMenuSubGroupsQuery({});
  const { data: menuItemsData, isLoading: isLoadingItems, refetch: refetchItems } = useGetMenusQuery({});
  const [updateMenuItem] = usePatchMenuMutation();

  // State
  const [activeTab, setActiveTab] = useState<"groups" | "items">("groups");
  const [selectedGroup, setSelectedGroup] = useState<number | null>(null);
  const [selectedSubGroup, setSelectedSubGroup] = useState<number | null>(null);
  const [selectedMainMenu, setSelectedMainMenu] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<"item" | "group" | "subgroup" | "main_menu">("item");
  const [expandedGroups, setExpandedGroups] = useState<Set<number>>(new Set());
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [isActionsModalOpen, setIsActionsModalOpen] = useState(false);

  // Process data
  const mainMenus = useMemo(() => mainMenuData?.data?.results?.filter((m: MainMenu) => m.is_active) ?? [], [mainMenuData]);
  const menuSubGroups = useMemo(() => menuSubGroupsData?.data?.results ?? [], [menuSubGroupsData]);
  const menuGroups = useMemo(() => {
    const groups = menuGroupsData?.data?.results ?? [];
    return groups
      .filter((g: MenuGroup) => !selectedMainMenu || g.menu === selectedMainMenu)
      .map((g: MenuGroup) => ({
        ...g,
        name: g.name || `Group ${g.id}`,
        subgroups: menuSubGroups.filter((sg: MenuSubGroup) => sg.group === g.id),
        mainMenuName: g.menu ? mainMenus.find((m: MainMenu) => m.id === g.menu)?.name : null,
      }));
  }, [menuGroupsData, menuSubGroups, selectedMainMenu, mainMenus]);

  const menuItems = useMemo(() => {
    let items = menuItemsData?.data?.results?.filter((i: MenuItem) => i.id && i.menu_group) ?? [];
    if (selectedMainMenu) {
      const groupIds = menuGroups.map((g: MenuGroup) => g.id);
      items = items.filter((i: MenuItem) => groupIds.includes(i.menu_group));
    }
    if (selectedSubGroup) items = items.filter((i: MenuItem) => i.menu_subgroup === selectedSubGroup);
    return items;
  }, [menuItemsData, selectedMainMenu, selectedSubGroup, menuGroups]);

  const groupedItems = useMemo(() => {
    return menuItems.reduce((acc: Record<string, MenuItem[]>, item: MenuItem) => {
      if (item.menu_group) {
        acc[item.menu_group] = acc[item.menu_group] || [];
        acc[item.menu_group].push(item);
      }
      return acc;
    }, {});
  }, [menuItems]);

  const updatedMenuGroups = useMemo(() => {
    return menuGroups.map((g: MenuGroup) => ({
      ...g,
      itemCount: groupedItems[g.id]?.length || 0,
    }));
  }, [menuGroups, groupedItems]);

  const selectedMainMenuName = useMemo(
    () => selectedMainMenu ? mainMenus.find((m) => m.id === selectedMainMenu)?.name : null,
    [selectedMainMenu, mainMenus]
  );

  // Auto-select first main menu
  useEffect(() => {
    if (mainMenus.length === 1 && !selectedMainMenu) setSelectedMainMenu(mainMenus[0].id);
  }, [mainMenus, selectedMainMenu]);

  // Handlers
  const handleAddMenuItem = useCallback(
    async (newItem: { title: string; description: string; imageUrl: string; price: string; category: string; rating: number; prepTime: string }) => {
      // Modal handles the API call, we just need to refresh data and close modal
      setIsModalOpen(false);
      // Use setTimeout to prevent race conditions
      setTimeout(() => {
        refetchItems();
        refetchGroups();
      }, 100);
    },
    [refetchItems, refetchGroups]
  );

  const handleUpdateMenuItem = useCallback(
    async (itemId: number, updates: Partial<MenuItem>) => {
      await updateMenuItem({ id: itemId, ...updates }).unwrap();
      refetchItems();
      refetchGroups();
    },
    [updateMenuItem, refetchItems, refetchGroups]
  );

  const handleArchiveMenuItem = useCallback(
    async (itemId: number) => {
      await updateMenuItem({ id: itemId, is_active: false }).unwrap();
      refetchItems();
      refetchGroups();
    },
    [updateMenuItem, refetchItems, refetchGroups]
  );

  const handleAddMenuGroup = useCallback(
    async (newGroup: { name: string; sales_category: string; position: number; menu: number }) => {
      // Modal handles the API call, we just need to refresh data and close modal
      setIsModalOpen(false);
      // Use setTimeout to prevent race conditions
      setTimeout(() => {
        refetchGroups();
      }, 100);
    },
    [refetchGroups]
  );

  const handleAddMenuSubGroup = useCallback(
    async (newSubGroup: { name: string; description: string; position: number; group: number }) => {
      // Modal handles the API call, we just need to refresh data and close modal
      setIsModalOpen(false);
      refetchSubGroups();
      refetchGroups();
    },
    [refetchSubGroups, refetchGroups]
  );

  const handleGroupClick = useCallback((groupId: number) => {
    setSelectedGroup(groupId);
    setSelectedSubGroup(null);
    setActiveTab("items");
  }, []);

  const handleSubGroupClick = useCallback((subGroupId: number) => {
    setSelectedSubGroup(subGroupId);
    setActiveTab("items");
  }, []);

  const handleItemEdit = useCallback((item: MenuItem) => {
    setSelectedItem(item);
    setIsActionsModalOpen(true);
  }, []);

  // Fixed handleToggleGroupExpansion function
  const handleToggleGroupExpansion = useCallback((groupId: number) => {
    setExpandedGroups((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  }, []);

  const createMealCardProps = useCallback(
    (item: MenuItem) => ({
      title: item.name || "Untitled",
      description: item.description || "",
      imageUrl: item.image_url || getGroupImage(item.menu_group || 46),
      price: item.price && !isNaN(Number(item.price)) && Number(item.price) > 0
  ? `KES ${Number(item.price).toFixed(2)}`
  : "N/A",
      category: updatedMenuGroups.find((g) => g.id === item.menu_group)?.name || "Uncategorized",
      rating: item.rating || 0,
      prepTime: item.prep_time || "",
      onEdit: () => handleItemEdit(item),
      isActive: item.is_active !== false,
      position: item.position,
    }),
    [updatedMenuGroups, handleItemEdit]
  );

  // Loading state
  if (isLoadingGroups || isLoadingItems || isLoadingMainMenu || isLoadingSubGroups) return <Screen><LoadingSpinner /></Screen>;

  // Render
  return (
    <ErrorBoundary>
      <Screen>
        <header className="mb-6">
          <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl p-8">
            <h1 className="text-3xl font-bold text-white">Food Menu</h1>
            <p className="text-white/80">Manage your restaurant menu {selectedMainMenuName && `- ${selectedMainMenuName}`}</p>
          </div>
        </header>

        <MainMenuTabs
          mainMenus={mainMenus}
          selectedMainMenu={selectedMainMenu}
          onSelectMainMenu={(id) => {
            setSelectedMainMenu(id);
            setSelectedGroup(null);
            setSelectedSubGroup(null);
          }}
        />

        <div className="mb-6">
          <div className="flex space-x-1 bg-white p-1 rounded-lg shadow">
            <button
              onClick={() => {
                setActiveTab("groups");
                setSelectedGroup(null);
                setSelectedSubGroup(null);
              }}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === "groups" ? "bg-orange-500 text-white" : "text-gray-600 hover:text-gray-800"}`}
            >
              Menu Groups ({updatedMenuGroups.length})
            </button>
            <button
              onClick={() => setActiveTab("items")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === "items" ? "bg-orange-500 text-white" : "text-gray-600 hover:text-gray-800"}`}
            >
              Menu Items ({menuItems.length})
            </button>
          </div>
        </div>

        <div className="flex justify-end mb-6 space-x-3">
          <PrimaryButton
            variant="secondary"
            onClick={() => {
              setModalType(activeTab === "groups" ? "group" : "item");
              setIsModalOpen(true);
            }}
          >
            {activeTab === "groups" ? "Add Menu Group" : "Add Menu Item"}
          </PrimaryButton>
          <PrimaryButton
            variant="secondary"
            onClick={() => {
              setModalType("main_menu");
              setIsModalOpen(true);
            }}
          >
            Add Main Menu
          </PrimaryButton>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {activeTab === "groups" ? (
            updatedMenuGroups.length > 0 ? (
              updatedMenuGroups.map((group) => (
                <MenuGroupCard
                  key={group.id}
                  group={group}
                  itemCount={group.itemCount || 0}
                  mainMenuName={group.mainMenuName}
                  onGroupClick={handleGroupClick}
                  onSubGroupClick={handleSubGroupClick}
                  onAddSubGroupClick={() => {
                    setSelectedGroup(group.id);
                    setModalType("subgroup");
                    setIsModalOpen(true);
                  }}
                  expandedGroups={expandedGroups}
                  onToggleExpand={handleToggleGroupExpansion}
                />
              ))
            ) : (
              <div className="col-span-full">
                <EmptyState
                  type="groups"
                  onAddClick={() => {
                    setModalType("group");
                    setIsModalOpen(true);
                  }}
                  mainMenuName={selectedMainMenuName}
                />
              </div>
            )
          ) : (
            (selectedGroup ? groupedItems[selectedGroup] || [] : menuItems).length > 0 ? (
              (selectedGroup ? groupedItems[selectedGroup] || [] : menuItems).map((item) => (
                <MealCard key={item.id} {...createMealCardProps(item)} />
              ))
            ) : (
              <div className="col-span-full">
                <EmptyState
                  type="items"
                  onAddClick={() => {
                    setModalType("item");
                    setIsModalOpen(true);
                  }}
                  mainMenuName={selectedMainMenuName}
                />
              </div>
            )
          )}
        </div>

        {selectedItem && (
          <MenuItemActionsModal
            item={selectedItem}
            isOpen={isActionsModalOpen}
            onClose={() => {
              setIsActionsModalOpen(false);
              setSelectedItem(null);
            }}
            onUpdate={handleUpdateMenuItem}
            onArchive={handleArchiveMenuItem}
            menuGroups={menuGroups}
            menuSubGroups={menuSubGroups}
          />
        )}

        {isModalOpen && (
          <>
            {modalType === "main_menu" ? (
              <CreateMainMenuModal
                onClose={() => setIsModalOpen(false)}
                onSave={() => {
                  setIsModalOpen(false);
                  refetchMainMenu();
                }}
              />
            ) : modalType === "group" ? (
              <CreateMenuGroupModal
                onClose={() => setIsModalOpen(false)}
                onSave={handleAddMenuGroup}
                selectedMainMenu={selectedMainMenu}
              />
            ) : modalType === "subgroup" ? (
              <CreateMenuSubGroupModal
                onClose={() => setIsModalOpen(false)}
                onSave={handleAddMenuSubGroup}
                selectedMenuGroup={selectedGroup}
                selectedMainMenu={selectedMainMenu}
              />
            ) : (
              <CreateMenuItemModal
                onClose={() => setIsModalOpen(false)}
                onSave={handleAddMenuItem}
                selectedMainMenu={selectedMainMenu}
                selectedMenuGroup={selectedGroup} // Pass selected group
                selectedMenuSubGroup={selectedSubGroup} // Pass selected subgroup
              />
            )}
          </>
        )}
      </Screen>
    </ErrorBoundary>
  );
};

export default FoodMenu;