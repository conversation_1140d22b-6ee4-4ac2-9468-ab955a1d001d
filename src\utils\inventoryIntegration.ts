/**
 * Inventory Integration Utilities for GRN
 * 
 * This module provides functions to integrate GRN (Goods Received Note) 
 * functionality with the inventory management system.
 * 
 * Note: These functions assume the existence of inventory API endpoints.
 * Update the API calls when the inventory endpoints are available.
 */

import { GRNItem } from "@/types/procurement";

// Types for inventory integration
export interface InventoryUpdateRequest {
  product_id: number;
  store_id: number;
  quantity_received: number;
  unit_price: number;
  grn_id: number;
  grn_item_id: number;
  received_date: string;
  expiry_date?: string;
  batch_number?: string;
  notes?: string;
}

export interface InventoryUpdateResponse {
  success: boolean;
  message: string;
  new_stock_level?: number;
  transaction_id?: number;
}

/**
 * Updates inventory stock levels when GRN items are received
 * 
 * @param grnItems - Array of GRN items that were received
 * @param storeId - ID of the store where goods were received
 * @param grnId - ID of the GRN
 * @param receivedDate - Date when goods were received
 * @returns Promise with update results
 */
export const updateInventoryFromGRN = async (
  grnItems: GRNItem[],
  storeId: number,
  grnId: number,
  receivedDate: string
): Promise<InventoryUpdateResponse[]> => {
  const results: InventoryUpdateResponse[] = [];

  for (const item of grnItems) {
    try {
      // Only update inventory for items with received quantity > 0
      const receivedQty = parseFloat(item.quantity_received.toString());
      if (receivedQty <= 0) {
        continue;
      }

      const updateRequest: InventoryUpdateRequest = {
        product_id: item.product,
        store_id: storeId,
        quantity_received: receivedQty,
        unit_price: parseFloat(item.unit_price.toString()),
        grn_id: grnId,
        grn_item_id: item.id!,
        received_date: receivedDate,
        expiry_date: item.expiry_date,
        notes: item.notes,
      };

      // TODO: Replace with actual inventory API call when available
      const response = await updateInventoryStock(updateRequest);
      results.push(response);

    } catch (error) {
      console.error(`Failed to update inventory for product ${item.product}:`, error);
      results.push({
        success: false,
        message: `Failed to update inventory for product ${item.product}: ${error}`,
      });
    }
  }

  return results;
};

/**
 * Reverses inventory updates when a GRN is cancelled or rejected
 * 
 * @param grnItems - Array of GRN items to reverse
 * @param storeId - ID of the store
 * @param grnId - ID of the GRN
 * @returns Promise with reversal results
 */
export const reverseInventoryFromGRN = async (
  grnItems: GRNItem[],
  storeId: number,
  grnId: number
): Promise<InventoryUpdateResponse[]> => {
  const results: InventoryUpdateResponse[] = [];

  for (const item of grnItems) {
    try {
      const receivedQty = parseFloat(item.quantity_received.toString());
      if (receivedQty <= 0) {
        continue;
      }

      // TODO: Replace with actual inventory reversal API call when available
      const response = await reverseInventoryStock({
        product_id: item.product,
        store_id: storeId,
        quantity_to_reverse: receivedQty,
        grn_id: grnId,
        grn_item_id: item.id!,
      });
      
      results.push(response);

    } catch (error) {
      console.error(`Failed to reverse inventory for product ${item.product}:`, error);
      results.push({
        success: false,
        message: `Failed to reverse inventory for product ${item.product}: ${error}`,
      });
    }
  }

  return results;
};

/**
 * Checks current stock levels for products in a GRN
 * 
 * @param productIds - Array of product IDs to check
 * @param storeId - ID of the store
 * @returns Promise with current stock levels
 */
export const checkCurrentStockLevels = async (
  productIds: number[],
  storeId: number
): Promise<{ [productId: number]: number }> => {
  try {
    // TODO: Replace with actual inventory API call when available
    const stockLevels = await getCurrentStockLevels(productIds, storeId);
    return stockLevels;
  } catch (error) {
    console.error("Failed to check current stock levels:", error);
    return {};
  }
};

/**
 * Validates if received quantities are reasonable based on current stock
 * 
 * @param grnItems - Array of GRN items to validate
 * @param storeId - ID of the store
 * @returns Validation results with warnings/errors
 */
export const validateReceivedQuantities = async (
  grnItems: GRNItem[],
  storeId: number
): Promise<{ isValid: boolean; warnings: string[]; errors: string[] }> => {
  const warnings: string[] = [];
  const errors: string[] = [];

  try {
    const productIds = grnItems.map(item => item.product);
    const currentStock = await checkCurrentStockLevels(productIds, storeId);

    for (const item of grnItems) {
      const receivedQty = parseFloat(item.quantity_received.toString());
      const currentQty = currentStock[item.product] || 0;

      // Add validation logic here
      if (receivedQty < 0) {
        errors.push(`Product ${item.product_name || item.product}: Received quantity cannot be negative`);
      }

      if (receivedQty > 10000) {
        warnings.push(`Product ${item.product_name || item.product}: Large quantity received (${receivedQty})`);
      }

      // Check for expiry date if applicable
      if (item.expiry_date) {
        const expiryDate = new Date(item.expiry_date);
        const today = new Date();
        if (expiryDate <= today) {
          warnings.push(`Product ${item.product_name || item.product}: Expiry date is in the past`);
        }
      }
    }
  } catch (error) {
    console.error("Failed to validate received quantities:", error);
    errors.push("Failed to validate quantities due to system error");
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  };
};

// TODO: Implement these functions when inventory API endpoints are available

/**
 * Updates inventory stock for a single item
 * This is a placeholder function - implement with actual API call
 */
async function updateInventoryStock(request: InventoryUpdateRequest): Promise<InventoryUpdateResponse> {
  // Placeholder implementation
  console.log("TODO: Implement inventory stock update API call", request);
  
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: "Stock updated successfully (simulated)",
        new_stock_level: Math.floor(Math.random() * 1000),
        transaction_id: Math.floor(Math.random() * 10000),
      });
    }, 100);
  });
}

/**
 * Reverses inventory stock for a single item
 * This is a placeholder function - implement with actual API call
 */
async function reverseInventoryStock(request: {
  product_id: number;
  store_id: number;
  quantity_to_reverse: number;
  grn_id: number;
  grn_item_id: number;
}): Promise<InventoryUpdateResponse> {
  // Placeholder implementation
  console.log("TODO: Implement inventory stock reversal API call", request);
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: "Stock reversal completed successfully (simulated)",
        new_stock_level: Math.floor(Math.random() * 1000),
      });
    }, 100);
  });
}

/**
 * Gets current stock levels for products
 * This is a placeholder function - implement with actual API call
 */
async function getCurrentStockLevels(
  productIds: number[],
  storeId: number
): Promise<{ [productId: number]: number }> {
  // Placeholder implementation
  console.log("TODO: Implement get current stock levels API call", { productIds, storeId });
  
  return new Promise((resolve) => {
    setTimeout(() => {
      const stockLevels: { [productId: number]: number } = {};
      productIds.forEach(id => {
        stockLevels[id] = Math.floor(Math.random() * 1000);
      });
      resolve(stockLevels);
    }, 100);
  });
}

/**
 * Integration hooks for GRN operations
 * These can be called from GRN components when inventory integration is needed
 */
export const inventoryIntegrationHooks = {
  /**
   * Call this when a GRN is created and items are received
   */
  onGRNCreated: async (grnItems: GRNItem[], storeId: number, grnId: number, receivedDate: string) => {
    console.log("GRN Created - Updating inventory...");
    const results = await updateInventoryFromGRN(grnItems, storeId, grnId, receivedDate);
    console.log("Inventory update results:", results);
    return results;
  },

  /**
   * Call this when a GRN is updated (quantities changed)
   */
  onGRNUpdated: async (
    oldItems: GRNItem[],
    newItems: GRNItem[],
    storeId: number,
    grnId: number,
    receivedDate: string
  ) => {
    console.log("GRN Updated - Adjusting inventory...");
    // First reverse the old quantities
    await reverseInventoryFromGRN(oldItems, storeId, grnId);
    // Then apply the new quantities
    const results = await updateInventoryFromGRN(newItems, storeId, grnId, receivedDate);
    console.log("Inventory adjustment results:", results);
    return results;
  },

  /**
   * Call this when a GRN is cancelled or rejected
   */
  onGRNCancelled: async (grnItems: GRNItem[], storeId: number, grnId: number) => {
    console.log("GRN Cancelled - Reversing inventory...");
    const results = await reverseInventoryFromGRN(grnItems, storeId, grnId);
    console.log("Inventory reversal results:", results);
    return results;
  },
};
