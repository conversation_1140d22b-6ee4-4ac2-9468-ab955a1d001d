export interface ProductSubCategoryItem {
    id: number;
    name: string;
    code: string;
    description: string | null;
    main_category: string;
}

export interface ProductSubCategoryApiResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: ProductSubCategoryItem[];
}

export interface CreateProductSubCategoryItemRequest {
    name: string;
    code: string;
    description: string | null;
    main_category: string;
}