import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useSelector } from 'react-redux';
import { selectCurrentUserDetails } from '@/redux/authSlice';
import { AlertCircle, ShoppingCart, Clock, Users, CheckCircle } from 'lucide-react';
import { useAddGuestCheckMutation } from '@/redux/slices/guestCheck';
import { useLazyGetOrdersQuery } from '@/redux/slices/order';

interface CheckInForm {
  guestCount: string;
  specialRequests: string;
  order: string;
}

interface Order {
  id: number;
  order_number: string;
  order_type: string;
  status: string;
  order_date: string;
  total_amount: string;
  payment_status: boolean;
  guest_count?: number;
  created_at: string;
  modified_at: string;
  tax_amount?: string;
  service_charge?: string;
  catering_levy?: string;
  revenue_center?: number;
  workstation?: number;
  table_number?: number;
  created_by: string;
}

interface GuestCheckPayload {
  guest_count: number;
  opened_at: string;
  closed_at?: string;
  status: string;
  payment_status: string;
  sub_total: string;
  tax_total: string;
  service_charge_total: string;
  discount_total: string;
  grand_total: string;
  order?: number;
  table_number: number;
  employee: string;
  linked_checks: number[];
}

interface CheckInDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  checkInForm: CheckInForm;
  setCheckInForm: React.Dispatch<React.SetStateAction<CheckInForm>>;
  onCheckIn: () => void;
}

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error: any }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: any) {
    return { hasError: true, error };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('ErrorBoundary caught in CheckInDialog:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-300 rounded-lg">
          <div className="flex items-center text-red-600">
            <AlertCircle className="h-5 w-5 mr-2" />
            <div>
              <h3 className="font-semibold">Something went wrong</h3>
              <p className="text-sm">{this.state.error?.message || 'Please try again or contact support.'}</p>
            </div>
          </div>
        </div>
      );
    }
    return this.props.children;
  }
}

// Utility functions
const parseAmount = (amount: string | number | undefined): number => {
  if (!amount) return 0;
  const parsed = parseFloat(String(amount));
  return isNaN(parsed) ? 0 : parsed;
};

const formatKES = (value: number): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 2,
  }).format(isNaN(value) ? 0 : value);
};

const validateGuestCount = (count: string): boolean => {
  const num = parseInt(count);
  return !isNaN(num) && num > 0 && num <= 50;
};

export const CheckInDialog: React.FC<CheckInDialogProps> = ({
  open,
  onOpenChange,
  checkInForm,
  setCheckInForm,
  onCheckIn,
}) => {
  const { toast } = useToast();
  const currentUser = useSelector(selectCurrentUserDetails);
  const [getOrders, { data: ordersApiResponse, isLoading: isLoadingOrders, error: ordersError }] =
    useLazyGetOrdersQuery();
  const [addGuestCheck, { isLoading: isSubmitting }] = useAddGuestCheckMutation();
  const [validationErrors, setValidationErrors] = React.useState<{ [key: string]: string }>({});

  React.useEffect(() => {
    if (open) {
      try {
        getOrders({});
        setValidationErrors({});
      } catch (error) {
        console.error('Error fetching orders:', error);
      }
    }
  }, [open, getOrders]);

  const apiOrders = React.useMemo(() => {
    if (!ordersApiResponse) return [];
    if (ordersApiResponse.data?.results) return ordersApiResponse.data.results;
    if (ordersApiResponse.results) return ordersApiResponse.results;
    if (Array.isArray(ordersApiResponse.data)) return ordersApiResponse.data;
    if (Array.isArray(ordersApiResponse)) return ordersApiResponse;
    return [];
  }, [ordersApiResponse]);

  const getSelectedOrder = React.useCallback((): Order | null => {
    if (!checkInForm.order) return null;
    return apiOrders.find((order) => String(order.id) === checkInForm.order) || null;
  }, [checkInForm.order, apiOrders]);

  const selectedOrder = getSelectedOrder();

  const getOrderDisplayInfo = React.useCallback(
    (order: Order) => {
      const orderNumber = order.order_number || `ORD-${order.id}`;
      const totalAmount = order.total_amount ? formatKES(parseAmount(order.total_amount)) : '';
      const display = `${orderNumber} ${totalAmount ? `• ${totalAmount}` : ''} (${
        order.order_type
      }, ${order.status})`;
      return {
        value: String(order.id),
        display,
        disabled: order.status !== 'Open',
        order,
      };
    },
    []
  );

  const orders = React.useMemo(() => {
    if (isLoadingOrders) return [];
    if (ordersError) {
      console.error('Orders API error:', ordersError);
      return [];
    }
    return apiOrders.filter((order) => order.status === 'Open').map(getOrderDisplayInfo);
  }, [apiOrders, isLoadingOrders, ordersError, getOrderDisplayInfo]);

  const hasSelectedOrder = React.useMemo(
    () => checkInForm.order && checkInForm.order !== 'loading-orders' && checkInForm.order !== 'no-orders',
    [checkInForm.order]
  );

  const validateForm = React.useCallback(() => {
    const errors: { [key: string]: string } = {};
    if (!checkInForm.guestCount) {
      errors.guestCount = 'Guest count is required';
    } else if (!validateGuestCount(checkInForm.guestCount)) {
      errors.guestCount = 'Please enter a valid guest count (1-50)';
    }
    if (!currentUser) {
      errors.user = 'User authentication required';
    } else {
      const hasEmployeeId =
        currentUser.employee_no ||
        currentUser.employee_id ||
        currentUser.empNo ||
        currentUser.emp_no ||
        currentUser.id ||
        currentUser.userId;
      if (!hasEmployeeId) {
        errors.user = 'Employee information is missing';
      }
    }
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [checkInForm.guestCount, currentUser]);

  const isFormValid = React.useMemo(
    () => {
      const guestCountValid = validateGuestCount(checkInForm.guestCount);
      const userValid = currentUser && (
        currentUser.employee_no ||
        currentUser.employee_id ||
        currentUser.empNo ||
        currentUser.emp_no ||
        currentUser.id ||
        currentUser.userId
      );
      return guestCountValid && userValid && !isLoadingOrders;
    },
    [checkInForm.guestCount, currentUser, isLoadingOrders]
  );

  const createPayload = React.useCallback((): GuestCheckPayload => {
    const now = new Date().toISOString();
    const totalAmount = parseAmount(selectedOrder?.total_amount);
    const taxAmount = parseAmount(selectedOrder?.tax_amount);
    const serviceCharge = parseAmount(selectedOrder?.service_charge);
    const subTotal = Math.max(0, totalAmount - taxAmount - serviceCharge);
    let employeeId = 'EMP001';
    if (currentUser) {
      employeeId = String(
        currentUser.employee_no ||
        currentUser.employee_id ||
        currentUser.empNo ||
        currentUser.emp_no ||
        currentUser.id ||
        currentUser.userId ||
        'EMP001'
      );
    }
    const guestCount = Math.max(1, Math.min(50, parseInt(checkInForm.guestCount) || 1));
    const payload: GuestCheckPayload = {
      guest_count: guestCount,
      opened_at: now,
      status: 'Open',
      payment_status: 'Unpaid',
      sub_total: subTotal.toFixed(2),
      tax_total: taxAmount.toFixed(2),
      service_charge_total: serviceCharge.toFixed(2),
      discount_total: '0.00',
      grand_total: totalAmount.toFixed(2),
      table_number: selectedOrder?.table_number || 1,
      employee: employeeId,
      linked_checks: [],
    };
    if (checkInForm.order && hasSelectedOrder && selectedOrder) {
      payload.order = selectedOrder.id;
    }
    return payload;
  }, [checkInForm.guestCount, checkInForm.order, selectedOrder, hasSelectedOrder, currentUser]);

  const handleCheckIn = React.useCallback(async () => {
    if (!validateForm()) {
      const firstError = Object.values(validationErrors)[0];
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: firstError || 'Please check your input and try again.',
      });
      return;
    }
    try {
      const payload = createPayload();
      await addGuestCheck(payload).unwrap();
      toast({
        title: 'Success',
        description: `Guest check created successfully for ${checkInForm.guestCount} guest${
          parseInt(checkInForm.guestCount) > 1 ? 's' : ''
        }!`,
        className: 'bg-green-100 border-green-300 text-green-800',
      });
      onCheckIn();
      getOrders({});
      onOpenChange(false);
      setCheckInForm({ guestCount: '', specialRequests: '', order: '' });
      setValidationErrors({});
    } catch (error: any) {
      let errorMessage = 'Failed to create guest check. Please try again.';
      if (error?.data) {
        if (error.data.employee?.[0]) {
          errorMessage = `Employee error: ${error.data.employee[0]}`;
        } else if (error.data.order?.[0]) {
          errorMessage = `Order error: ${error.data.order[0]}`;
        } else if (error.data.guest_count?.[0]) {
          errorMessage = `Guest count error: ${error.data.guest_count[0]}`;
        } else if (error.data.message) {
          errorMessage = error.data.message;
        } else if (typeof error.data === 'string') {
          errorMessage = error.data;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }
      toast({
        variant: 'destructive',
        title: 'Error',
        description: errorMessage,
      });
    }
  }, [validateForm, validationErrors, createPayload, addGuestCheck, toast, onCheckIn, getOrders, onOpenChange, setCheckInForm, checkInForm.guestCount]);

  const handleSubmit = React.useCallback((e: React.FormEvent) => {
    e.preventDefault();
    handleCheckIn();
  }, [handleCheckIn]);

  const handleOrderChange = React.useCallback(
    (value: string) => {
      if (value !== 'loading-orders' && value !== 'no-orders') {
        const selectedOrder = apiOrders.find((order) => String(order.id) === value);
        if (selectedOrder) {
          const updates: Partial<CheckInForm> = { order: value };
          if (!checkInForm.guestCount && selectedOrder.guest_count && selectedOrder.guest_count > 0) {
            updates.guestCount = String(selectedOrder.guest_count);
          }
          setCheckInForm((prev) => ({ ...prev, ...updates }));
        } else {
          setCheckInForm((prev) => ({ ...prev, order: value }));
        }
      }
      setValidationErrors((prev) => ({ ...prev, order: '' }));
    },
    [apiOrders, setCheckInForm, checkInForm.guestCount]
  );

  const handleUnlinkOrder = React.useCallback(() => {
    setCheckInForm((prev) => ({ ...prev, order: '' }));
  }, [setCheckInForm]);

  const handleGuestCountChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setCheckInForm((prev) => ({ ...prev, guestCount: value }));
      if (validationErrors.guestCount) {
        setValidationErrors((prev) => ({ ...prev, guestCount: '' }));
      }
    },
    [setCheckInForm, validationErrors.guestCount]
  );

  React.useEffect(() => {
    if (ordersError) {
      toast({
        variant: 'destructive',
        title: 'Error Loading Orders',
        description: 'Failed to load orders. You can still create a guest check without linking to an order.',
      });
    }
  }, [ordersError, toast]);

  return (
    <ErrorBoundary>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-lg bg-white border border-gray-300 rounded-lg">
          <DialogHeader>
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-5 w-5 text-gray-600" />
              <DialogTitle className="text-xl font-semibold text-gray-800">
                New Guest Check-in
              </DialogTitle>
            </div>
            <DialogDescription className="text-gray-600">
              Create a new guest check for your table.
              {currentUser && (
                <span className="block text-sm text-gray-500 mt-1">
                  Attendant: {currentUser.fullnames || currentUser.name || 'Current User'}
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-4 py-4">
              {/* Order Selection */}
              <div className="space-y-2">
                <Label htmlFor="order" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <ShoppingCart className="h-4 w-4" />
                  Link to Existing Order
                  <span className="text-gray-400 font-normal">(Optional)</span>
                </Label>
                <Select value={checkInForm.order || ''} onValueChange={handleOrderChange} disabled={isLoadingOrders}>
                  <SelectTrigger className="w-full h-10 border-gray-300">
                    <SelectValue placeholder={isLoadingOrders ? 'Loading orders...' : 'Select existing order (optional)'} />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-300">
                    {isLoadingOrders ? (
                      <SelectItem value="loading-orders" disabled>
                        <div className="flex items-center py-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-600 border-t-transparent mr-2" />
                          <span>Loading orders...</span>
                        </div>
                      </SelectItem>
                    ) : orders.length > 0 ? (
                      orders.map((order) => (
                        <SelectItem key={order.value} value={order.value} disabled={order.disabled} className="py-2">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                              <ShoppingCart className="h-4 w-4 mr-2 text-gray-600" />
                              <div className="flex flex-col items-start">
                                <span className={`font-medium ${order.disabled ? 'text-gray-400' : 'text-gray-800'}`}>
                                  {order.order.order_number}
                                </span>
                                <div className="flex items-center gap-2 text-xs text-gray-500">
                                  {order.order.total_amount && (
                                    <span className="flex items-center gap-1">
                                      <span>{formatKES(parseAmount(order.order.total_amount))}</span>
                                    </span>
                                  )}
                                  {order.order.table_number && <span>Table {order.order.table_number}</span>}
                                  <span className="capitalize">{order.order.order_type}</span>
                                </div>
                              </div>
                            </div>
                            {order.disabled && (
                              <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">Closed</span>
                            )}
                          </div>
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no-orders" disabled>
                        <div className="flex items-center text-gray-500 py-2">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          No open orders available
                        </div>
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {hasSelectedOrder && selectedOrder && (
                  <div className="border border-gray-300 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-semibold text-gray-800">Order Linked</span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleUnlinkOrder}
                        className="h-8 px-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100"
                      >
                        Unlink
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center gap-2">
                        <span className="text-gray-600">Total:</span>
                        <span className="font-semibold text-gray-800">
                          {formatKES(parseAmount(selectedOrder.total_amount))}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-600">Created:</span>
                        <span className="font-medium">
                          {new Date(selectedOrder.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    {selectedOrder.guest_count && (
                      <div className="mt-2 text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
                        Order suggests {selectedOrder.guest_count} guest{selectedOrder.guest_count > 1 ? 's' : ''}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Guest Count */}
              <div className="space-y-2">
                <Label htmlFor="guestCount" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Number of Guests *
                </Label>
                <Input
                  id="guestCount"
                  type="number"
                  min="1"
                  max="50"
                  placeholder="Enter guest count"
                  value={checkInForm.guestCount}
                  onChange={handleGuestCountChange}
                  className={`w-full h-10 border-gray-300 ${
                    validationErrors.guestCount ? 'border-red-300' : ''
                  }`}
                  required
                />
                {validationErrors.guestCount && (
                  <div className="flex items-center text-sm text-red-600">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    {validationErrors.guestCount}
                  </div>
                )}
              </div>

              {/* Special Requests */}
              <div className="space-y-2">
                <Label htmlFor="specialRequests" className="text-sm font-semibold text-gray-700">
                  Special Requests
                  <span className="text-gray-400 font-normal ml-1">(Optional)</span>
                </Label>
                <Input
                  id="specialRequests"
                  placeholder="Dietary restrictions, preferences, or special notes..."
                  value={checkInForm.specialRequests}
                  onChange={(e) => setCheckInForm((prev) => ({ ...prev, specialRequests: e.target.value }))}
                  className="w-full h-10 border-gray-300"
                />
              </div>

              {/* User validation error */}
              {validationErrors.user && (
                <div className="flex items-center text-sm text-red-600 bg-red-100 border border-red-300 p-2 rounded">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {validationErrors.user}
                </div>
              )}
            </div>
          </ScrollArea>

          <DialogFooter className="flex gap-2 pt-4 border-t border-gray-300">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1 h-10 border-gray-300 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleCheckIn}
              className="flex-1 h-10 bg-blue-600 hover:bg-blue-700 text-white"
              disabled={!isFormValid || isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  Creating Check...
                </div>
              ) : isLoadingOrders ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  Loading...
                </div>
              ) : !currentUser ? (
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  User Not Found
                </div>
              ) : (
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Check In Guest{checkInForm.guestCount && parseInt(checkInForm.guestCount) > 1 ? 's' : ''}
                </div>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </ErrorBoundary>
  );
};