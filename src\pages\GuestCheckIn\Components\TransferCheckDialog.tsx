import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserCheck, ArrowRight } from 'lucide-react';
import { GuestCheck } from './types/types';

interface User {
  id: string | number;
  username?: string;
  name?: string;
}

interface TransferCheckDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  check: GuestCheck | null;
  availableUsers: User[];
  onTransferCheck: (checkId: string, toUserId: string, reason?: string) => void;
}

export const TransferCheckDialog: React.FC<TransferCheckDialogProps> = ({
  open,
  onOpenChange,
  check,
  availableUsers,
  onTransferCheck,
}) => {
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [transferReason, setTransferReason] = useState('');

  const handleTransfer = () => {
    if (!check || !selectedUserId) return;
    
    onTransferCheck(check.id, selectedUserId, transferReason.trim() || undefined);
    setSelectedUserId('');
    setTransferReason('');
    onOpenChange(false);
  };

  const formatKES = (value: number): string => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Filter out current waiter from available users
  const availableTransferUsers = availableUsers.filter(user => 
    (user.username || user.name) !== check?.waiterName
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Transfer Check
          </DialogTitle>
        </DialogHeader>

        {check && (
          <div className="space-y-4">
            {/* Check Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium mb-3">Check Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Check #:</span>
                  <span className="font-medium">{check.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Table:</span>
                  <span className="font-medium">{check.tableNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Guests:</span>
                  <span className="font-medium">{check.guest_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Current Server:</span>
                  <span className="font-medium">{check.waiterName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Time:</span>
                  <span className="font-medium">{formatTime(check.orderTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total:</span>
                  <span className="font-bold">{formatKES(check.total)}</span>
                </div>
              </div>
            </div>

            {/* Transfer Details */}
            <div className="space-y-4">
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <span>From: <strong>{check.waiterName}</strong></span>
                <ArrowRight className="h-4 w-4" />
                <span>To: <strong>{selectedUserId ? availableTransferUsers.find(u => u.id.toString() === selectedUserId)?.username || availableTransferUsers.find(u => u.id.toString() === selectedUserId)?.name || 'Unknown' : 'Select Server'}</strong></span>
              </div>

              <div className="space-y-2">
                <Label htmlFor="transferTo" className="text-sm font-medium">
                  Transfer to Server <span className="text-red-500">*</span>
                </Label>
                <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a server..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTransferUsers.length === 0 ? (
                      <SelectItem value="none" disabled>
                        No other servers available
                      </SelectItem>
                    ) : (
                      availableTransferUsers.map(user => (
                        <SelectItem key={user.id} value={user.id.toString()}>
                          {user.username || user.name || `User ${user.id}`}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="transferReason" className="text-sm font-medium">
                  Reason for Transfer (Optional)
                </Label>
                <Textarea
                  id="transferReason"
                  value={transferReason}
                  onChange={(e) => setTransferReason(e.target.value)}
                  placeholder="Enter reason for transfer (e.g., shift change, section reassignment)..."
                  rows={3}
                  className="resize-none"
                />
              </div>
            </div>

            {/* Transfer History */}
            {check.transfers && check.transfers.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Transfer History</Label>
                <div className="max-h-32 overflow-y-auto border rounded-lg p-3 bg-gray-50">
                  {check.transfers.map((transfer, index) => (
                    <div key={transfer.id} className="text-xs text-gray-600 mb-2 last:mb-0">
                      <div className="flex justify-between items-start">
                        <span>
                          {transfer.fromEmployee} → {transfer.toEmployee}
                        </span>
                        <span>
                          {new Date(transfer.transferredAt).toLocaleString()}
                        </span>
                      </div>
                      {transfer.reason && (
                        <div className="text-gray-500 italic mt-1">
                          Reason: {transfer.reason}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Warning */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="text-sm text-yellow-800">
                <strong>Note:</strong> Transferring this check will change the responsible server. 
                The new server will be able to modify the check and handle payment.
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => {
              setSelectedUserId('');
              setTransferReason('');
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleTransfer}
            disabled={!selectedUserId || availableTransferUsers.length === 0}
          >
            Transfer Check
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};