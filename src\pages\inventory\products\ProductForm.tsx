import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  useCreateProductMutation,
  useUpdateProductMutation
} from '@/redux/slices/product';
import { useGetProductSubCategoryQuery } from '@/redux/slices/product-sub-category';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Save, AlertCircle } from 'lucide-react';
import { ProductItem } from '../types/product.type';
import { useGetInventoryUtilityStoresQuery, useGetInventoryUtilitySuppliersQuery, useGetInventoryUtilityTaxClassQuery } from '@/redux/slices/inventory-utilities';

const productSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  code: z.string().min(1, 'Code is required').max(20, 'Code must be less than 20 characters'),
  description: z.string().nullable().optional(),
  unit_price: z.string().min(1, 'Unit price is required').regex(/^\d+(\.\d{1,2})?$/, 'Invalid price format'),
  perishable: z.boolean().nullable().optional(),
  shelf_life_days: z.number().nullable().optional(),
  is_active: z.boolean().nullable().optional(),
  sub_category: z.string().min(1, 'Sub category is required'),
  tax_class: z.number().nullable().optional(),
  default_supplier: z.string().nullable().optional(),
  unit_of_measure: z.number().nullable().optional(),
  default_store: z.string().nullable().optional(),
});

type ProductFormData = z.infer<typeof productSchema>;

interface ProductFormProps {
  item?: ProductItem;
  onClose: () => void;
  onSuccess: () => void;
}

export const ProductForm: React.FC<ProductFormProps> = ({
  item,
  onClose,
  onSuccess
}) => {
  const [createItem, { isLoading: isCreating, error: createError }] = useCreateProductMutation();
  const [updateItem, { isLoading: isUpdating, error: updateError }] = useUpdateProductMutation();
  const { data: subCategories } = useGetProductSubCategoryQuery({ params: {} });
  const { data: stores } = useGetInventoryUtilityStoresQuery({ params: {} });
  const { data: taxClasses } = useGetInventoryUtilityTaxClassQuery({ params: {} });
  const { data: suppliers } = useGetInventoryUtilitySuppliersQuery({ params: {} });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid }
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: item?.name || '',
      code: item?.code || '',
      description: item?.description || '',
      unit_price: item?.unit_price || '',
      perishable: item?.perishable ?? false,
      shelf_life_days: item?.shelf_life_days || null,
      is_active: item?.is_active ?? true,
      sub_category: item?.sub_category || '',
      tax_class: item?.tax_class || null,
      default_supplier: item?.default_supplier || '',
      unit_of_measure: item?.unit_of_measure || null,
      default_store: item?.default_store || '',
    },
    mode: 'onChange'
  });

  const isLoading = isCreating || isUpdating;
  const error = createError || updateError;
  const selectedSubCategory = watch('sub_category');
  const isPerishable = watch('perishable');

  const onSubmit = async (data: ProductFormData) => {
    try {
      const submitData = {
        ...data,
        description: data.description || null,
        perishable: data.perishable ?? null,
        is_active: data.is_active ?? null,
        shelf_life_days: data.perishable ? (data.shelf_life_days ?? null) : null,
        tax_class: data.tax_class ?? null,
        default_supplier: data.default_supplier || null,
        unit_of_measure: data.unit_of_measure ?? null,
        default_store: data.default_store || null,
      };

      if (item) {
        await updateItem({
          id: item.id,
          body: submitData
        }).unwrap();
      } else {
        await createItem(submitData).unwrap();
      }
      onSuccess();
    } catch (err) {
      console.error('Failed to save product:', err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold">
            {item ? 'Edit Product' : 'Add Product'}
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">An unexpected error occurred. Please try again later.</span>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium text-gray-700">
                  Name *
                </label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter product name"
                  className={`focus-visible:ring-0 ${errors.name ? 'border-red-500' : ''}`}
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="code" className="text-sm font-medium text-gray-700">
                  Code *
                </label>
                <Input
                  id="code"
                  {...register('code')}
                  placeholder="Enter product code"
                  className={`focus-visible:ring-0 ${errors.code ? 'border-red-500' : ''}`}
                />
                {errors.code && (
                  <p className="text-sm text-red-600">{errors.code.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                {...register('description')}
                placeholder="Enter description (optional)"
                rows={3}
                className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="unit_price" className="text-sm font-medium text-gray-700">
                  Unit Price *
                </label>
                <Input
                  id="unit_price"
                  {...register('unit_price')}
                  placeholder="0.00"
                  type="number"
                  step="0.01"
                  className={`focus-visible:ring-0 ${errors.unit_price ? 'border-red-500' : ''}`}
                />
                {errors.unit_price && (
                  <p className="text-sm text-red-600">{errors.unit_price.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="sub_category" className="text-sm font-medium text-gray-700">
                  Sub Category *
                </label>
                <Select
                  value={selectedSubCategory}
                  onValueChange={(value) => setValue('sub_category', value, { shouldValidate: true })}
                >
                  <SelectTrigger className={`focus:ring-0 ${errors.sub_category ? 'border-red-500' : ''}`}>
                    <SelectValue placeholder="Select sub category" />
                  </SelectTrigger>
                  <SelectContent>
                    {subCategories?.results?.map((category) => (
                      <SelectItem key={category.id} value={category.code.toString()}>
                        {category.name} ({category.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.sub_category && (
                  <p className="text-sm text-red-600">{errors.sub_category.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Status
                </label>
                <Select
                  value={watch('is_active') ? 'true' : 'false'}
                  onValueChange={(value) => setValue('is_active', value === 'true', { shouldValidate: true })}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Active</SelectItem>
                    <SelectItem value="false">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label htmlFor="default_supplier" className="text-sm font-medium text-gray-700">
                  Default Supplier
                </label>
                <Select
                  value={watch('default_supplier') || ''}
                  onValueChange={(value) => setValue('default_supplier', value || null, { shouldValidate: true })}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers?.results?.length ? (
                      suppliers.results.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.code.toString()}>
                          {supplier.name} ({supplier.code})
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem disabled value="Not Available">
                        Suppliers not available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="tax_class" className="text-sm font-medium text-gray-700">
                  Tax Class
                </label>
                <Select
                  value={watch('tax_class')?.toString() || ''}
                  onValueChange={(value) => setValue('tax_class', value ? parseInt(value) : null, { shouldValidate: true })}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue placeholder="Select tax class" />
                  </SelectTrigger>
                  <SelectContent>
                    {taxClasses?.results?.length ? (
                      taxClasses.results.map((taxClass) => (
                        <SelectItem key={taxClass.id} value={taxClass.id.toString()}>
                          {taxClass.name} ({taxClass.rate}%)
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem disabled value="Not Available">
                        Tax Classes not available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label htmlFor="default_store" className="text-sm font-medium text-gray-700">
                  Default Store
                </label>
                <Select
                  value={watch('default_store') || ''}
                  onValueChange={(value) => setValue('default_store', value || null, { shouldValidate: true })}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue placeholder="Select store" />
                  </SelectTrigger>
                  <SelectContent>
                    {stores?.results?.length ? (
                      stores.results.map((store) => (
                        <SelectItem key={store.id} value={store.code.toString()}>
                          {store.name} 
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem disabled value="Not Available">
                        Stores not available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="unit_of_measure" className="text-sm font-medium text-gray-700">
                Unit of Measure
              </label>
              <Input
                id="unit_of_measure"
                {...register('unit_of_measure', { valueAsNumber: true })}
                placeholder="Enter unit of measure ID"
                type="number"
                min="1"
                className='focus-visible:ring-0'
              />
            </div>



            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Perishable
                </label>
                <Select
                  value={isPerishable ? 'true' : 'false'}
                  onValueChange={(value) => setValue('perishable', value === 'true', { shouldValidate: true })}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="false">No</SelectItem>
                    <SelectItem value="true">Yes</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {isPerishable && (
                <div className="space-y-2">
                  <label htmlFor="shelf_life_days" className="text-sm font-medium text-gray-700">
                    Shelf Life (Days)
                  </label>
                  <Input
                    id="shelf_life_days"
                    {...register('shelf_life_days', { valueAsNumber: true })}
                    placeholder="Enter shelf life in days"
                    type="number"
                    min="1"
                    className='focus-visible:ring-0 '
                  />
                </div>
              )}
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="flex-1 cursor-pointer"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1 cursor-pointer"
                disabled={!isValid || isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Saving...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    {item ? 'Update' : 'Create'}
                  </div>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};