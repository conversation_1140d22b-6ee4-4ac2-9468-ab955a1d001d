import React, { useState } from 'react';
import { RecipeGroupList } from './RecipeGroupList';
import { RecipeGroupForm } from './RecipeGroupForm';
import { Plus } from 'lucide-react';
import { RecipeGroup } from '../types/recipe-group.type';
import { Button } from '@/components/ui/button';
import { Screen } from '@/app-components/layout/screen';

export const RecipeGroupModule: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<RecipeGroup | undefined>();

  const handleAdd = () => {
    setEditingItem(undefined);
    setShowForm(true);
  };

  const handleEdit = (item: RecipeGroup) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  return (
    <Screen>
      <div className="container mx-auto py-6 space-y-5">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Recipe Groups</h1>
            <p className="text-muted-foreground">
              Manage recipe groups and categories for organizing your recipes
            </p>
          </div>
          <Button onClick={handleAdd} className="flex items-center gap-2 cursor-pointer">
            <Plus className="h-4 w-4" />
            Add Recipe Group
          </Button>
        </div>

        <RecipeGroupList onEdit={handleEdit} onAdd={handleAdd} />

        {showForm && (
          <RecipeGroupForm
            item={editingItem}
            onClose={handleCloseForm}
            onSuccess={handleFormSuccess}
          />
        )}
      </div>
    </Screen>
  );
};