/**
 * Utility functions for generating check numbers and QR codes
 */

export const generateCheckNumber = (): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `CHK-${timestamp}-${random}`;
};

export const generateShortCheckNumber = (): string => {
  const date = new Date();
  const dateStr = date.toISOString().slice(2, 10).replace(/-/g, ''); // YYMMDD
  const timeStr = date.toTimeString().slice(0, 8).replace(/:/g, ''); // HHMMSS
  const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');
  return `${dateStr}${timeStr}${random}`;
};

export const generateQRCodeData = (checkId: string, tableNumber: string): string => {
  const baseUrl = window.location.origin;
  return `${baseUrl}/guest-check/${checkId}?table=${tableNumber}`;
};

export const formatCheckNumber = (checkNumber: string): string => {
  if (checkNumber.includes('-')) {
    return checkNumber;
  }
  // Format as CHK-XXXX-XXXX for display
  if (checkNumber.length >= 8) {
    return `CHK-${checkNumber.slice(0, 4)}-${checkNumber.slice(4)}`;
  }
  return `CHK-${checkNumber}`;
};