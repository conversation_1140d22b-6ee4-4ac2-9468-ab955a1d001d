import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Pencil,
  Eye,
  History,
  GitCompare,
  Plus,
  Search,
  ChefHat,
  Clock,
  DollarSign,
  Link,
  UserX,
  AlertCircle,
  User,
  Shield,
  Calendar,
  CircleCheckBig,
  CircleX,
  Filter
} from 'lucide-react';
import { useGetRecipeGroupQuery } from '@/redux/slices/recipe-group';
import { useGetRecipeIngredientQuery } from '@/redux/slices/recipe-ingredient';
import { Recipe } from '../types/recipe-type';
import { useGetRecipeQuery, useUpdateRecipeMutation } from '@/redux/slices/recipe-data';

interface RecipeListProps {
  onEdit: (item: Recipe) => void;
  onAdd: () => void;
  onView: (item: Recipe) => void;
  onViewHistory: (item: Recipe) => void;
  onCompareVersions: (item: Recipe) => void;
}

type SortField = 'name' | 'recipe_type' | 'portion_size' | 'preparation_time' | 'cooking_time' | 'final_cost';
type SortDirection = 'asc' | 'desc';

export const RecipeList: React.FC<RecipeListProps> = ({
  onEdit,
  onAdd,
  onView,
  onViewHistory,
  onCompareVersions
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [groupFilter, setGroupFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const { data: recipesData, isLoading, error } = useGetRecipeQuery({ params: {} });
  const { data: groupsData } = useGetRecipeGroupQuery({ params: {} });
  const { data: ingredientsData } = useGetRecipeIngredientQuery({ params: {} });
  const [updateRecipe] = useUpdateRecipeMutation();

  const recipes = recipesData?.results || [];
  const groups = groupsData?.results || [];
  const ingredients = ingredientsData?.results || [];

  const filteredAndSortedRecipes = useMemo(() => {
    let filtered = recipes.filter(recipe => {
      const matchesSearch = recipe.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === 'all' || recipe.recipe_type === typeFilter;
      const matchesGroup = groupFilter === 'all' || recipe.recipe_group.toString() === groupFilter;
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'active' && recipe.is_active) ||
        (statusFilter === 'inactive' && !recipe.is_active);

      return matchesSearch && matchesType && matchesGroup && matchesStatus;
    });

    // Sort recipes
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      if (sortField === 'final_cost') {
        aValue = calculateFinalCost(a.id);
        bValue = calculateFinalCost(b.id);
      } else {
        aValue = a[sortField];
        bValue = b[sortField];
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [recipes, searchTerm, typeFilter, groupFilter, statusFilter, sortField, sortDirection]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleDeactivate = async (recipe: Recipe) => {
    const action = recipe.is_active ? 'deactivate' : 'activate';
    if (window.confirm(`Are you sure you want to ${action} this recipe?`)) {
      try {
        await updateRecipe({
          id: recipe.id,
          body: { is_active: !recipe.is_active }
        }).unwrap();
      } catch (error) {
        console.error(`Failed to ${action} recipe:`, error);
      }
    }
  };

  const getGroupName = (groupId: number) => {
    const group = groups.find(g => g.id === groupId);
    return group?.name || 'Unknown Group';
  };

  const calculateFinalCost = (recipeId: number): number => {
    const recipeIngredients = ingredients.filter(ing => ing.recipe === recipeId);
    return recipeIngredients.reduce((total, ingredient) => {
      const cost = parseFloat(ingredient.total_cost || '0');
      return total + cost;
    }, 0);
  };

  const getMenuItemName = (menuItemId: number | null): string => {
    // This would typically come from a menu items API call
    // For now, return a placeholder or the ID
    return menuItemId ? `Menu Item #${menuItemId}` : '';
  };

  const isRecentlyModified = (recipe: Recipe): boolean => {
    if (!recipe.updated_at && !recipe.last_modified) return false;

    const lastModified = new Date(recipe.updated_at || recipe.last_modified!);
    const now = new Date();
    const diffInHours = (now.getTime() - lastModified.getTime()) / (1000 * 60 * 60);
    return diffInHours <= 24;
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role?.toLowerCase()) {
      case 'chef': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'kitchen manager': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'sous chef': return 'bg-green-100 text-green-800 border-green-200';
      case 'admin': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-muted-foreground">Loading recipes...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Error loading recipes. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div>
      <div className='space-y-5'>

        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search recipes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

       <div className="flex flex-col  gap-2 items-start p-4 rounded-lg border">
          <div className="flex items-center gap-2 ml-1">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filters</span>
          </div>

          <div className="flex flex-col md:flex-row gap-4 w-full">
            <div className="flex-1">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className='focus:ring-0'>
                  <SelectValue placeholder="Recipe Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Prep">Prep Recipe</SelectItem>
                  <SelectItem value="Serving">Final Recipe</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1 ">
              <Select value={groupFilter} onValueChange={setGroupFilter}>
                <SelectTrigger className='focus:ring-0'>
                  <SelectValue placeholder="Group" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Groups</SelectItem>
                  {groups.map(group => (
                    <SelectItem key={group.id} value={group.id.toString()}>
                      {group.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1 ">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='focus:ring-0'>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className='!font-bold'>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50 font-bold"
                  onClick={() => handleSort('name')}
                >
                  Recipe Name
                  {sortField === 'name' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50 font-bold"
                  onClick={() => handleSort('recipe_type')}
                >
                  Recipe Type
                  {sortField === 'recipe_type' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead className='font-bold'>Group</TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50 font-bold"
                  onClick={() => handleSort('preparation_time')}
                >
                  Timing
                  {sortField === 'preparation_time' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead className='font-bold'>Menu Item</TableHead>
                <TableHead className='text-center font-bold'>Status & Size</TableHead>
                <TableHead className='font-bold'>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedRecipes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    <div className="flex flex-col items-center gap-2">
                      <ChefHat className="h-8 w-8 text-muted-foreground" />
                      <div className="text-muted-foreground">No recipes found</div>
                      <Button onClick={onAdd} size="sm" variant="outline">
                        <Plus className="h-4 w-4 mr-2" />
                        Add your first recipe
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedRecipes.map((recipe) => {
                  const finalCost = calculateFinalCost(recipe.id);
                  const menuItemName = getMenuItemName(recipe.menu_item);

                  return (
                    <TableRow key={recipe.id} className={isRecentlyModified(recipe) ? 'bg-orange-50 border-l-4 border-l-orange-400' : ''}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {recipe.name}
                          <p></p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={recipe.recipe_type === 'Prep' ? 'secondary' : 'default'}>
                          {recipe.recipe_type === 'Serving' ? 'Final' : recipe.recipe_type}
                        </Badge>
                      </TableCell>
                      <TableCell>{getGroupName(recipe.recipe_group)}</TableCell>
                      <TableCell className=' space-y-0.5'>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <p className='whitespace-nowrap'>{recipe.preparation_time}m prep time</p>
                        </div>
                        <div className="flex items-center flex-nowrap gap-1">
                          <Clock className="h-4 w-4" />
                          <p className='whitespace-nowrap'>{recipe.cooking_time}m cook time</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {recipe.menu_item ? (
                          <div className="flex items-center gap-1">
                            <span className="text-sm bg-muted px-4 py-0.5 rounded">{recipe.menu_item}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">Not linked</span>
                        )}
                      </TableCell>
                      <TableCell className='flex flex-col items-center gap-1 '>
                        {recipe.is_active
                          ? <div className="flex gap-1">
                            <CircleCheckBig size={18} className='text-green-500 translate-y-0.5' /> <p>Active</p>
                          </div>
                          : <div className="flex gap-1">
                            <CircleX size={20} className='text-red-500 translate-y-0.5' /> <p>Inactive</p>
                          </div>
                        }
                        {
                          recipe.portion_size
                          && <Badge className='w-fit'>{recipe.portion_size}</Badge>
                        }
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {/* <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onView(recipe)}
                            title="View Recipe"
                          >
                            <Eye className="h-4 w-4" />
                          </Button> */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEdit(recipe)}
                            title="Edit Recipe"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewHistory(recipe)}
                            title="View History"
                          >
                            <History className="h-4 w-4" />
                          </Button>
                          {/* <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onCompareVersions(recipe)}
                            title="Compare Versions"
                          >
                            <GitCompare className="h-4 w-4" />
                          </Button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};