import React, { useState } from 'react';
import { ProductMainCategoryList } from './ProductMainCategoryList';
import { ProductMainCategoryForm } from './ProductMainCategoryForm';
import type { ProductMainCategoryItem } from '../types/product-main-categories.type';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Screen } from '@/app-components/layout/screen';

export const ProductMainCategoryModule: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<ProductMainCategoryItem | undefined>();

  const handleAdd = () => {
    setEditingItem(undefined);
    setShowForm(true);
  };

  const handleEdit = (item: ProductMainCategoryItem) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  return (
    <Screen>
      <div className="container mx-auto py-6 space-y-5">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Product Main Categories</h1>
            <p className="text-muted-foreground">
              Manage your product main categories
            </p>
          </div>
          <Button onClick={handleAdd} className="flex items-center gap-2 cursor-pointer">
            <Plus className="h-4 w-4" />
            Add Category
          </Button>
        </div>

        <ProductMainCategoryList onEdit={handleEdit} onAdd={handleAdd} />

        {showForm && (
          <ProductMainCategoryForm
            item={editingItem}
            onClose={handleCloseForm}
            onSuccess={handleFormSuccess}
          />
        )}
      </div>
    </Screen>
  );
};