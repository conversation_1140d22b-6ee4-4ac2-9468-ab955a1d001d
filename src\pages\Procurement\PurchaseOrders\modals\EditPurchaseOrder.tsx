import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Save, X } from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { useUpdatePurchaseOrderMutation } from "@/redux/slices/procurement";
import { PurchaseOrder } from "@/types/procurement";

interface EditPurchaseOrderProps {
  open: boolean;
  onClose: () => void;
  purchaseOrder: PurchaseOrder | null;
  onSuccess?: () => void;
}

interface EditFormData {
  delivery_location: string;
  payment_terms: string;
  delivery_date: string;
}

const EditPurchaseOrder: React.FC<EditPurchaseOrderProps> = ({
  open,
  onClose,
  purchaseOrder,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<EditFormData>({
    delivery_location: "",
    payment_terms: "",
    delivery_date: "",
  });

  const [updatePurchaseOrder, { isLoading }] = useUpdatePurchaseOrderMutation();

  // Initialize form data when purchase order changes
  useEffect(() => {
    if (purchaseOrder && open) {
      setFormData({
        delivery_location: purchaseOrder.delivery_location || "",
        payment_terms: purchaseOrder.payment_terms || "",
        delivery_date: purchaseOrder.delivery_date || "",
      });
    }
  }, [purchaseOrder, open]);

  const handleInputChange = (field: keyof EditFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      delivery_location: "",
      payment_terms: "",
      delivery_date: "",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!purchaseOrder?.id) {
      toast.error("Purchase Order ID is required");
      return;
    }

    try {
      const payload = {
        id: purchaseOrder.id,
        delivery_location: formData.delivery_location || null,
        payment_terms: formData.payment_terms || null,
        delivery_date: formData.delivery_date || null,
      };

      await updatePurchaseOrder(payload).unwrap();

      toast.success("Purchase Order updated successfully");
      resetForm();
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error updating purchase order:", error);
      toast.error(error?.data?.message || "Failed to update purchase order");
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      resetForm();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            Edit Purchase Order - {purchaseOrder?.po_number}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Purchase Order Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-600">PO Number:</span>
                <span className="ml-2">{purchaseOrder?.po_number}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">Supplier:</span>
                <span className="ml-2">{purchaseOrder?.supplier}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">Status:</span>
                <span className="ml-2">{purchaseOrder?.status}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">Created By:</span>
                <span className="ml-2">{purchaseOrder?.created_by}</span>
              </div>
            </div>
          </div>

          {/* Editable Fields */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="delivery_location">Delivery Location</Label>
              <Input
                id="delivery_location"
                value={formData.delivery_location}
                onChange={(e) => handleInputChange("delivery_location", e.target.value)}
                placeholder="Enter delivery location"
                maxLength={255}
              />
            </div>

            <div>
              <Label htmlFor="payment_terms">Payment Terms</Label>
              <Textarea
                id="payment_terms"
                value={formData.payment_terms}
                onChange={(e) => handleInputChange("payment_terms", e.target.value)}
                placeholder="Enter payment terms (e.g., Net 30 days, Cash on delivery, etc.)"
                rows={3}
                maxLength={255}
              />
            </div>

            <div>
              <Label htmlFor="delivery_date">Delivery Date</Label>
              <Input
                id="delivery_date"
                type="date"
                value={formData.delivery_date}
                onChange={(e) => handleInputChange("delivery_date", e.target.value)}
              />
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Update Purchase Order
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditPurchaseOrder;
