import { CreateRecipeGroupRequest, RecipeGroup, RecipeGroupApiResponse } from "@/pages/inventory/types/recipe-group.type";
import { apiSlice } from "../apiSlice";

export const recipeGroupApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getRecipeGroup: builder.query<RecipeGroupApiResponse, {params:{}}>({
            query: (params) => ({
                url: `/menu/recipe-groups`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<RecipeGroupApiResponse>) => response.data,
            providesTags: ["/menu/recipe-groups"],
        }),
        updateRecipeGroup: builder.mutation<RecipeGroup, { id: number; body: Partial<CreateRecipeGroupRequest> }>({
            query: ({ id, body }) => ({
                url: `/menu/recipe-groups/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/menu/recipe-groups"],
        }),
        createRecipeGroup: builder.mutation<RecipeGroup, CreateRecipeGroupRequest>({
            query: (body) => ({
                url: `/menu/recipe-groups`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/menu/recipe-groups"],
        }),
        getOneRecipeGroup: builder.query<RecipeGroup, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/menu/recipe-groups/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/menu/recipe-groups"],
        }),
        deleteRecipeGroup: builder.mutation<void, number>({
            query: (id) => ({
                url: `/menu/recipe-groups/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/menu/recipe-groups"],
        }),
    })
})

export const {
    useCreateRecipeGroupMutation,
    useGetRecipeGroupQuery,
    useUpdateRecipeGroupMutation,
    useGetOneRecipeGroupQuery,
    useDeleteRecipeGroupMutation,
} = recipeGroupApiSlice

interface ApiResponse<T> {
  data: T;
  message: string;
}