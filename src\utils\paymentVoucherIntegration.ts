/**
 * Payment Voucher Integration Utilities
 * 
 * This module provides functions to integrate invoice functionality with the payment voucher system.
 * It handles the conversion of approved invoices to payment vouchers and manages the workflow.
 * 
 * Note: These functions assume the existence of payment voucher API endpoints.
 * Update the API calls when the payment voucher endpoints are available.
 */

import { Invoice } from "@/types/procurement";

// Types for payment voucher integration
export interface PaymentVoucherRequest {
  invoice_id: number;
  amount: string;
  currency: string;
  supplier_id: number;
  gl_account_id: number;
  payment_date?: string;
  payment_method?: string;
  notes?: string;
}

export interface PaymentVoucherResponse {
  voucher_id: number;
  voucher_number: string;
  amount: string;
  status: string;
  created_at: string;
  payment_date?: string;
  payment_method?: string;
}

/**
 * Creates a payment voucher from an approved invoice
 * 
 * @param invoice - The approved invoice to convert to a payment voucher
 * @returns Promise with the created payment voucher details
 */
export const createPaymentVoucherFromInvoice = async (
  invoice: Invoice
): Promise<PaymentVoucherResponse> => {
  // Validate invoice status
  if (invoice.status !== "Approved") {
    throw new Error("Only approved invoices can be converted to payment vouchers");
  }

  // Check if invoice already has a payment voucher
  if (invoice.payment_voucher) {
    throw new Error(`Invoice already has payment voucher: ${invoice.payment_voucher_number || invoice.payment_voucher}`);
  }

  try {
    // Create payment voucher request
    const voucherRequest: PaymentVoucherRequest = {
      invoice_id: invoice.id!,
      amount: invoice.total_amount,
      currency: invoice.currency,
      supplier_id: invoice.supplier,
      gl_account_id: invoice.gl_account,
      payment_date: new Date().toISOString().split('T')[0], // Default to today
      notes: `Payment for invoice ${invoice.invoice_number}`,
    };

    // TODO: Replace with actual payment voucher API call when available
    const response = await createPaymentVoucher(voucherRequest);
    return response;

  } catch (error) {
    console.error(`Failed to create payment voucher for invoice ${invoice.id}:`, error);
    throw error;
  }
};

/**
 * Gets payment voucher details by ID
 * 
 * @param voucherId - ID of the payment voucher
 * @returns Promise with payment voucher details
 */
export const getPaymentVoucherDetails = async (
  voucherId: number
): Promise<PaymentVoucherResponse> => {
  try {
    // TODO: Replace with actual payment voucher API call when available
    const response = await fetchPaymentVoucher(voucherId);
    return response;
  } catch (error) {
    console.error(`Failed to get payment voucher details for ID ${voucherId}:`, error);
    throw error;
  }
};

/**
 * Validates if an invoice can be converted to a payment voucher
 * 
 * @param invoice - The invoice to validate
 * @returns Validation result with any errors
 */
export const validateInvoiceForPaymentVoucher = (
  invoice: Invoice
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!invoice.id) {
    errors.push("Invoice ID is missing");
  }

  if (invoice.status !== "Approved") {
    errors.push(`Invoice status must be 'Approved', current status: ${invoice.status}`);
  }

  if (invoice.payment_voucher) {
    errors.push(`Invoice already has payment voucher: ${invoice.payment_voucher_number || invoice.payment_voucher}`);
  }

  if (!invoice.supplier) {
    errors.push("Supplier information is missing");
  }

  if (!invoice.gl_account) {
    errors.push("GL account information is missing");
  }

  if (!invoice.total_amount || parseFloat(invoice.total_amount) <= 0) {
    errors.push("Invoice amount must be greater than zero");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// TODO: Implement these functions when payment voucher API endpoints are available

/**
 * Creates a payment voucher
 * This is a placeholder function - implement with actual API call
 */
async function createPaymentVoucher(request: PaymentVoucherRequest): Promise<PaymentVoucherResponse> {
  // Placeholder implementation
  console.log("TODO: Implement payment voucher creation API call", request);
  
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        voucher_id: Math.floor(Math.random() * 10000),
        voucher_number: `PV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
        amount: request.amount,
        status: "Created",
        created_at: new Date().toISOString(),
        payment_date: request.payment_date,
        payment_method: request.payment_method,
      });
    }, 500);
  });
}

/**
 * Fetches payment voucher details
 * This is a placeholder function - implement with actual API call
 */
async function fetchPaymentVoucher(voucherId: number): Promise<PaymentVoucherResponse> {
  // Placeholder implementation
  console.log("TODO: Implement fetch payment voucher API call", voucherId);
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        voucher_id: voucherId,
        voucher_number: `PV-${voucherId}`,
        amount: (Math.random() * 10000).toFixed(2),
        status: "Created",
        created_at: new Date().toISOString(),
      });
    }, 300);
  });
}

/**
 * Integration hooks for payment voucher operations
 * These can be called from invoice components when payment voucher integration is needed
 */
export const paymentVoucherIntegrationHooks = {
  /**
   * Call this when creating a payment voucher from an invoice
   */
  onCreatePaymentVoucher: async (invoice: Invoice) => {
    console.log("Creating payment voucher for invoice:", invoice.invoice_number);
    const validation = validateInvoiceForPaymentVoucher(invoice);
    
    if (!validation.isValid) {
      throw new Error(`Cannot create payment voucher: ${validation.errors.join(", ")}`);
    }
    
    const result = await createPaymentVoucherFromInvoice(invoice);
    console.log("Payment voucher created:", result);
    return result;
  },

  /**
   * Call this to get payment voucher details
   */
  onGetPaymentVoucherDetails: async (voucherId: number) => {
    console.log("Getting payment voucher details for ID:", voucherId);
    const result = await getPaymentVoucherDetails(voucherId);
    console.log("Payment voucher details:", result);
    return result;
  },
};
