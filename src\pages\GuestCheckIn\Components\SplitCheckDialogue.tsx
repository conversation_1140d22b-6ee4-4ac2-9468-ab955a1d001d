import React from 'react';
import { GuestChe<PERSON> } from './types/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle } from 'lucide-react';


interface LinkCheckDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  checkToLink: GuestCheck | null;
  availableChecks: GuestCheck[];
  selectedCheckToLink: string;
  setSelectedCheckToLink: (checkId: string) => void;
  onConfirm: () => void;
}

const LinkCheckDialog: React.FC<LinkCheckDialogProps> = ({
  open,
  onOpenChange,
  checkToLink,
  availableChecks,
  selectedCheckToLink,
  setSelectedCheckToLink,
  onConfirm,
}) => {
  

  if (!open || !checkToLink) return null;

  const eligibleChecks = availableChecks.filter(
    (check) =>
      check.id !== checkToLink.id &&
      check.status === 'active' &&
      !checkToLink.linkedChecks?.some((linked) => linked.check_number === check.id)
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md bg-gradient-to-br from-white via-orange-50/30 to-red-50/30 backdrop-blur-xl border-0 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-gray-800 via-orange-600 to-red-600 bg-clip-text text-transparent">
            Link Check
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Link check <span className="font-semibold text-orange-600">{checkToLink.id}</span> to another active check
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Check to Link To:
            </label>
            <Select value={selectedCheckToLink} onValueChange={setSelectedCheckToLink}>
              <SelectTrigger className="w-full h-12 bg-white/80 backdrop-blur-sm border-gray-200 hover:border-orange-300 transition-all duration-200">
                <SelectValue placeholder="Choose a check..." />
              </SelectTrigger>
              <SelectContent className="bg-white/95 backdrop-blur-xl border-gray-200">
                {eligibleChecks.length > 0 ? (
                  eligibleChecks.map((check) => (
                    <SelectItem key={check.id} value={check.id}>
                      {check.id} - Table {check.tableNumber} ({check.waiterName})
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-checks" disabled>
                    <div className="flex items-center text-gray-500 py-2">
                      <AlertCircle className="h-4 w-4 mr-3" />
                      No eligible checks available
                    </div>
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {eligibleChecks.length === 0 && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-xl">
              <p className="text-amber-700 text-sm">
                No eligible checks available for linking. Only active checks that aren't already linked can be selected.
              </p>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-3 pt-4 border-t border-gray-100">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 h-12 border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={!selectedCheckToLink}
            className="flex-1 h-12 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Link Checks
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LinkCheckDialog;