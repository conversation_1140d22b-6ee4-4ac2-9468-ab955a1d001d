import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Plus,
  Calendar,
  Play,
  Edit,
  Trash2,
  Eye,
  MapPin,
  AlertCircle,
  CheckCircle,
  UserCheck,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Screen } from "@/app-components/layout/screen";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { useAuthHook } from "@/utils/useAuthHook";

// API imports
import {
  useGetShiftsQuery,
  useCreateShiftMutation,
  useUpdateShiftMutation,
  useDeleteShiftMutation,
  useGetShiftEntriesQuery,
  useCreateShiftEntryMutation,
  Shift,
  ShiftEntry,
  CreateShiftRequest
} from "@/redux/slices/shifts";
import { useGetUsersQuery } from "@/redux/slices/user";
import { useGetBranchesQuery } from "@/redux/slices/branches";
import { useGetRevenueCentersQuery } from "@/redux/slices/revenueCenters";
import { useGetWorkstationsQuery } from "@/redux/slices/workstations";

// Helper function to format datetime for display
const formatTime = (dateTimeString: string) => {
  return new Date(dateTimeString).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const formatDate = (dateTimeString: string) => {
  return new Date(dateTimeString).toLocaleDateString();
};

// Helper function to calculate duration
const calculateDuration = (startTime: string, endTime?: string | null) => {
  if (!endTime) return "0h 0m";

  const start = new Date(startTime);
  const end = new Date(endTime);
  const diffMs = end.getTime() - start.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  return `${diffHours}h ${diffMinutes}m`;
};

// Helper function to determine shift status
const getShiftStatus = (shift: Shift) => {
  const now = new Date();
  const startTime = new Date(shift.start_time);
  const endTime = shift.end_time ? new Date(shift.end_time) : null;

  if (endTime && now > endTime) {
    return "Completed";
  } else if (now >= startTime && (!endTime || now <= endTime)) {
    return "Active";
  } else {
    return "Scheduled";
  }
};

// Helper function to analyze shift entry timing
const analyzeShiftEntryTiming = (shiftEntry: ShiftEntry) => {
  const shiftStartTime = new Date(shiftEntry.shift_start_time);
  const actualStartTime = new Date(shiftEntry.start_time);
  const shiftEndTime = new Date(shiftEntry.shift_end_time);
  const actualEndTime = shiftEntry.end_time ? new Date(shiftEntry.end_time) : null;

  const startDiffMinutes = (actualStartTime.getTime() - shiftStartTime.getTime()) / (1000 * 60);
  const endDiffMinutes = actualEndTime ? (actualEndTime.getTime() - shiftEndTime.getTime()) / (1000 * 60) : 0;

  return {
    isLate: startDiffMinutes > 5, // Late if more than 5 minutes after scheduled start
    isEarly: startDiffMinutes < -5, // Early if more than 5 minutes before scheduled start
    isEarlyExit: actualEndTime && endDiffMinutes < -5, // Early exit if more than 5 minutes before scheduled end
    isOvertime: actualEndTime && endDiffMinutes > 5, // Overtime if more than 5 minutes after scheduled end
    startDiffMinutes: Math.round(startDiffMinutes),
    endDiffMinutes: Math.round(endDiffMinutes),
  };
};

// Helper function to get shift entry for a shift
const getShiftEntryForShift = (shiftCode: string, shiftEntries: ShiftEntry[]) => {
  return shiftEntries.find(entry => entry.shift === shiftCode);
};

export default function ShiftManagement() {
  const [activeTab, setActiveTab] = useState("calendar");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [editingShift, setEditingShift] = useState<Shift | null>(null);
  const [deletingShift, setDeletingShift] = useState<Shift | null>(null);
  const [filterByUserBranch, setFilterByUserBranch] = useState(false);
  const [newShift, setNewShift] = useState({
    employee: "",
    start_time: new Date().toTimeString().slice(0, 5), // Default to current time (HH:MM)
    end_time: "",
    branch: "",
    revenue_center: "",
    workstation: "",
    auto_ended: false,
  });

  // Helper function to format time for input (HH:MM)
  const formatTimeForInput = (dateTimeString: string) => {
    if (!dateTimeString) return "";
    const date = new Date(dateTimeString);
    return date.toTimeString().slice(0, 5); // Format: HH:MM
  };

  // Helper function to get current time for input default
  const getCurrentTime = () => {
    const now = new Date();
    return now.toTimeString().slice(0, 5); // Format: HH:MM
  };

  // Helper function to combine date and time into ISO string
  const combineDateTime = (timeString: string) => {
    if (!timeString) return "";
    const today = new Date();
    const [hours, minutes] = timeString.split(':');
    today.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    return today.toISOString();
  };

  const navigate = useNavigate();
  const { toast } = useToast();
  const { user_details } = useAuthHook();

  // Build query parameters for shifts
  const shiftsQueryParams = filterByUserBranch && user_details?.branch
    ? { branch: user_details.branch.toString() }
    : {};

  // API hooks with polling for real-time updates
  const { data: shiftsData, isLoading: shiftsLoading, error: shiftsError, refetch: refetchShifts } = useGetShiftsQuery(shiftsQueryParams, {
    pollingInterval: 30000, // Poll every 30 seconds for new shifts
    refetchOnFocus: true,   // Refetch when window regains focus
    refetchOnReconnect: true, // Refetch when network reconnects
  });
  const { data: shiftEntriesData, isLoading: shiftEntriesLoading, error: shiftEntriesError, refetch: refetchShiftEntries } = useGetShiftEntriesQuery({
    page_size: 100, // Get more entries
    ordering: '-start_time', // Order by most recent first
  }, {
    pollingInterval: 30000, // Poll every 30 seconds for new shift entries
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });
  const { data: usersData, isLoading: usersLoading } = useGetUsersQuery({});
  const { data: branches = [], isLoading: branchesLoading } = useGetBranchesQuery({});
  const { data: revenueCenters = [], isLoading: revenueCentersLoading } = useGetRevenueCentersQuery({});
  const { data: workstations = [], isLoading: workstationsLoading } = useGetWorkstationsQuery({});

  // Mutations
  const [createShift, { isLoading: createLoading }] = useCreateShiftMutation();
  const [updateShift, { isLoading: updateLoading }] = useUpdateShiftMutation();
  const [deleteShift, { isLoading: deleteLoading }] = useDeleteShiftMutation();
  const [createShiftEntry] = useCreateShiftEntryMutation();

  // Extract data from API responses
  const shifts = shiftsData?.results || [];
  const shiftEntries = shiftEntriesData?.results || [];
  const employees = usersData?.results || [];

  // Debug logging
  console.log("Shifts data:", shiftsData);
  console.log("Shift entries data:", shiftEntriesData);
  console.log("Shift entries error:", shiftEntriesError);

  const validateShiftForm = () => {
    if (!newShift.employee) {
      toast({
        title: "Validation Error",
        description: "Please select an employee",
        variant: "destructive",
      });
      return false;
    }

    if (!newShift.start_time) {
      toast({
        title: "Validation Error",
        description: "Please select a start time",
        variant: "destructive",
      });
      return false;
    }

    if (!newShift.branch) {
      toast({
        title: "Validation Error",
        description: "Please select a branch",
        variant: "destructive",
      });
      return false;
    }

    if (newShift.end_time && new Date(newShift.end_time) <= new Date(newShift.start_time)) {
      toast({
        title: "Validation Error",
        description: "End time must be after start time",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleCreateShift = async () => {
    if (!validateShiftForm()) return;

    try {
      // Convert time format to ISO datetime string
      const startDateTime = combineDateTime(newShift.start_time);
      const endDateTime = newShift.end_time ? combineDateTime(newShift.end_time) : null;

      // Create shift data object with empty code for backend auto-generation
      const shiftData: CreateShiftRequest = {
        code: "", // Empty string - backend will auto-generate
        employee: newShift.employee.trim(),
        start_time: startDateTime,
        end_time: endDateTime,
        branch: parseInt(newShift.branch),
        auto_ended: newShift.auto_ended || false,
      };

      // Only add optional fields if they have values
      if (newShift.revenue_center && newShift.revenue_center.trim()) {
        shiftData.revenue_center = parseInt(newShift.revenue_center);
      }

      if (newShift.workstation && newShift.workstation.trim()) {
        shiftData.workstation = parseInt(newShift.workstation);
      }

      console.log("Creating shift with data:", JSON.stringify(shiftData, null, 2)); // Debug log
      console.log("Shift data keys:", Object.keys(shiftData)); // Debug log
      console.log("Shift data values:", Object.values(shiftData)); // Debug log

      const result = await createShift(shiftData).unwrap();
      console.log("Create shift result:", result); // Debug log

      toast({
        title: "Success",
        description: "Shift created successfully",
      });

      // Refresh the shifts data
      refetchShifts();

      setIsCreateModalOpen(false);
      resetNewShiftForm();
    } catch (error: any) {
      console.error("Create shift error:", error);
      console.error("Error data:", JSON.stringify(error?.data, null, 2));

      let errorMessage = "Failed to create shift";

      if (error?.data) {
        if (typeof error.data === 'string') {
          errorMessage = error.data;
        } else if (error.data.message) {
          errorMessage = error.data.message;
        } else if (error.data.detail) {
          errorMessage = error.data.detail;
        } else if (error.data.code) {
          errorMessage = `Code field error: ${Array.isArray(error.data.code) ? error.data.code.join(', ') : error.data.code}`;
        } else {
          errorMessage = JSON.stringify(error.data);
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error Creating Shift",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleEditShift = async () => {
    if (!editingShift || !validateShiftForm()) return;

    try {
      const startDateTime = combineDateTime(newShift.start_time);
      const endDateTime = newShift.end_time ? combineDateTime(newShift.end_time) : null;

      const shiftData = {
        employee: newShift.employee,
        start_time: startDateTime,
        end_time: endDateTime,
        branch: parseInt(newShift.branch),
        revenue_center: newShift.revenue_center ? parseInt(newShift.revenue_center) : null,
        workstation: newShift.workstation ? parseInt(newShift.workstation) : null,
        auto_ended: newShift.auto_ended,
      };

      console.log("Updating shift with data:", shiftData); // Debug log
      await updateShift({ code: editingShift.code, data: shiftData }).unwrap();

      toast({
        title: "Success",
        description: "Shift updated successfully",
      });

      setIsEditModalOpen(false);
      setEditingShift(null);
      resetNewShiftForm();
    } catch (error: any) {
      console.error("Update shift error:", error);
      const errorMessage = error?.data?.message ||
                          error?.data?.detail ||
                          error?.message ||
                          "Failed to update shift";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const openDeleteModal = (shift: Shift) => {
    setDeletingShift(shift);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteShift = async () => {
    if (!deletingShift) return;

    try {
      await deleteShift(deletingShift.code).unwrap();

      toast({
        title: "Success",
        description: "Shift deleted successfully",
      });

      setIsDeleteModalOpen(false);
      setDeletingShift(null);
    } catch (error: any) {
      console.error("Delete shift error:", error);
      const errorMessage = error?.data?.message ||
                          error?.data?.detail ||
                          error?.message ||
                          "Failed to delete shift";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleStartShift = async (shiftCode: string) => {
    try {
      const shift = shifts.find(s => s.code === shiftCode);
      if (!shift) return;

      const shiftEntryData = {
        shift: shiftCode,
        shift_start_time: shift.start_time,
        shift_end_time: shift.end_time || new Date().toISOString(),
        start_time: new Date().toISOString(),
        workstation: shift.workstation,
      };

      await createShiftEntry(shiftEntryData).unwrap();

      toast({
        title: "Success",
        description: "Shift started successfully",
      });
    } catch (error: any) {
      console.error("Start shift error:", error);
      const errorMessage = error?.data?.message ||
                          error?.data?.detail ||
                          error?.message ||
                          "Failed to start shift";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const resetNewShiftForm = () => {
    setNewShift({
      employee: "",
      start_time: getCurrentTime(),
      end_time: "",
      branch: "",
      revenue_center: "",
      workstation: "",
      auto_ended: false,
    });
  };

  const openEditModal = (shift: Shift) => {
    setEditingShift(shift);
    setNewShift({
      employee: shift.employee,
      start_time: formatTimeForInput(shift.start_time),
      end_time: shift.end_time ? formatTimeForInput(shift.end_time) : "",
      branch: shift.branch.toString(),
      revenue_center: shift.revenue_center?.toString() || "",
      workstation: shift.workstation?.toString() || "",
      auto_ended: shift.auto_ended || false,
    });
    setIsEditModalOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Completed":
        return "bg-blue-100 text-blue-800";
      case "Scheduled":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Calculate statistics from real data
  const shiftsWithStatus = shifts.map(shift => ({
    ...shift,
    status: getShiftStatus(shift)
  }));

  const activeShifts = shiftsWithStatus.filter(shift => shift.status === "Active").length;
  const completedShifts = shiftsWithStatus.filter(shift => shift.status === "Completed").length;
  const scheduledShifts = shiftsWithStatus.filter(shift => shift.status === "Scheduled").length;
  const lateEntries = shiftEntries.filter(entry => {
    const analysis = analyzeShiftEntryTiming(entry);
    return analysis.isLate;
  }).length;

  // Helper function to get employee name by ID
  const getEmployeeName = (employeeId: string) => {
    const employee = employees.find(emp => emp.employee_no === employeeId || emp.id?.toString() === employeeId);
    return employee ? `${employee.first_name} ${employee.last_name}` : employeeId;
  };

  // Helper function to get branch name by ID
  const getBranchName = (branchId: number) => {
    const branch = branches.find(b => b.id === branchId);
    return branch ? branch.name : `Branch ${branchId}`;
  };

  // Helper function to get revenue center name by ID
  const getRevenueCenterName = (revenueCenterId?: number | null) => {
    if (!revenueCenterId) return "N/A";
    const revenueCenter = revenueCenters.find(rc => rc.id === revenueCenterId);
    return revenueCenter ? revenueCenter.name : `Revenue Center ${revenueCenterId}`;
  };

  // Helper function to get workstation name by ID
  const getWorkstationName = (workstationId?: number | null) => {
    if (!workstationId) return "N/A";
    const workstation = workstations.find(ws => ws.id === workstationId);
    return workstation ? workstation.name : `Workstation ${workstationId}`;
  };

  // Show loading state
  if (shiftsLoading || usersLoading || branchesLoading) {
    return (
      <Screen>
        <div className="p-6 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading shift data...</span>
          </div>
        </div>
      </Screen>
    );
  }

  // Show error state
  if (shiftsError) {
    return (
      <Screen>
        <div className="p-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error Loading Shifts</h2>
            <p className="text-muted-foreground">
              There was an error loading the shift data. Please try again.
            </p>
          </div>
        </div>
      </Screen>
    );
  }



  // Show error state
  if (shiftsError) {
    return (
      <Screen>
        <div className="p-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error Loading Shifts</h2>
            <p className="text-muted-foreground">
              There was an error loading the shift data. Please try again.
            </p>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
          <Link to="/admin/users">
            <Button variant="ghost" size="sm" className="w-fit">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Shift Management</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage employee shifts, workstations, and time tracking
            </p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          {/* Branch Filter Toggle */}
          {user_details?.branch && (
            <div className="flex items-center space-x-2">
              <Switch
                id="branch-filter"
                checked={filterByUserBranch}
                onCheckedChange={setFilterByUserBranch}
              />
              <Label htmlFor="branch-filter" className="text-sm">
                My Branch Only
              </Label>
            </div>
          )}
          <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              console.log("Manual refresh triggered");
              refetchShifts();
              refetchShiftEntries();
              toast({
                title: "Data Refreshed",
                description: "Shift data has been updated from the server",
              });
            }}
            disabled={shiftsLoading || shiftEntriesLoading}
            className="w-full sm:w-auto"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${(shiftsLoading || shiftEntriesLoading) ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">Refresh</span>
            <span className="sm:hidden">Refresh</span>
          </Button>
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Create Shift</span>
                <span className="sm:hidden">Create</span>
              </Button>
            </DialogTrigger>
          <DialogContent className="w-[95vw] max-w-md sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Create New Shift</DialogTitle>
              <DialogDescription>
                Schedule a new shift for an employee. The shift code will be generated automatically.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="employee">Employee *</Label>
                <Select
                  value={newShift.employee}
                  onValueChange={(value) => setNewShift(prev => ({ ...prev, employee: value }))}
                  disabled={usersLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={usersLoading ? "Loading employees..." : "Select employee"} />
                  </SelectTrigger>
                  <SelectContent>
                    {usersLoading ? (
                      <SelectItem value="" disabled>
                        <div className="flex items-center space-x-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Loading employees...</span>
                        </div>
                      </SelectItem>
                    ) : employees.length === 0 ? (
                      <SelectItem value="" disabled>
                        No employees found
                      </SelectItem>
                    ) : (
                      employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.employee_no || employee.id?.toString() || ""}>
                          {employee.first_name} {employee.last_name} ({employee.employee_no})
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startTime">Start Time *</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={newShift.start_time}
                    onChange={(e) => setNewShift(prev => ({ ...prev, start_time: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endTime">End Time</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={newShift.end_time}
                    onChange={(e) => setNewShift(prev => ({ ...prev, end_time: e.target.value }))}
                    min={newShift.start_time}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="branch">Branch *</Label>
                  <Select
                    value={newShift.branch}
                    onValueChange={(value) => setNewShift(prev => ({ ...prev, branch: value }))}
                    disabled={branchesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={branchesLoading ? "Loading branches..." : "Select branch"} />
                    </SelectTrigger>
                    <SelectContent>
                      {branchesLoading ? (
                        <SelectItem value="" disabled>
                          <div className="flex items-center space-x-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Loading branches...</span>
                          </div>
                        </SelectItem>
                      ) : branches.length === 0 ? (
                        <SelectItem value="" disabled>
                          No branches found
                        </SelectItem>
                      ) : (
                        branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id?.toString() || ""}>
                            {branch.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="revenueCenter">Revenue Center</Label>
                  <Select
                    value={newShift.revenue_center}
                    onValueChange={(value) => setNewShift(prev => ({ ...prev, revenue_center: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select revenue center" />
                    </SelectTrigger>
                    <SelectContent>
                      {revenueCenters.map((center) => (
                        <SelectItem key={center.id} value={center.id?.toString() || ""}>
                          {center.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="workstation">Workstation</Label>
                <Select
                  value={newShift.workstation}
                  onValueChange={(value) => setNewShift(prev => ({ ...prev, workstation: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select workstation" />
                  </SelectTrigger>
                  <SelectContent>
                    {workstations.map((station) => (
                      <SelectItem key={station.id} value={station.id?.toString() || ""}>
                        {station.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoEnded"
                  checked={newShift.auto_ended}
                  onCheckedChange={(checked) => setNewShift(prev => ({ ...prev, auto_ended: checked as boolean }))}
                />
                <Label htmlFor="autoEnded">Auto End Shift</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateShift} disabled={createLoading}>
                {createLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Shift
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
          </div>
        </div>

        {/* Edit Shift Modal */}
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="w-[95vw] max-w-md sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Edit Shift</DialogTitle>
              <DialogDescription>
                Update the shift details for the selected employee.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="editEmployee">Employee *</Label>
                <Select
                  value={newShift.employee}
                  onValueChange={(value) => setNewShift(prev => ({ ...prev, employee: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select employee" />
                  </SelectTrigger>
                  <SelectContent>
                    {employees.map((employee) => (
                      <SelectItem key={employee.id} value={employee.employee_no || employee.id?.toString() || ""}>
                        {employee.first_name} {employee.last_name} ({employee.employee_no})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="editStartTime">Start Time *</Label>
                  <Input
                    id="editStartTime"
                    type="time"
                    value={newShift.start_time}
                    onChange={(e) => setNewShift(prev => ({ ...prev, start_time: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="editEndTime">End Time</Label>
                  <Input
                    id="editEndTime"
                    type="time"
                    value={newShift.end_time}
                    onChange={(e) => setNewShift(prev => ({ ...prev, end_time: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="editBranch">Branch *</Label>
                  <Select
                    value={newShift.branch}
                    onValueChange={(value) => setNewShift(prev => ({ ...prev, branch: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id?.toString() || ""}>
                          {branch.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="editRevenueCenter">Revenue Center</Label>
                  <Select
                    value={newShift.revenue_center}
                    onValueChange={(value) => setNewShift(prev => ({ ...prev, revenue_center: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select revenue center" />
                    </SelectTrigger>
                    <SelectContent>
                      {revenueCenters.map((center) => (
                        <SelectItem key={center.id} value={center.id?.toString() || ""}>
                          {center.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="editWorkstation">Workstation</Label>
                <Select
                  value={newShift.workstation}
                  onValueChange={(value) => setNewShift(prev => ({ ...prev, workstation: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select workstation" />
                  </SelectTrigger>
                  <SelectContent>
                    {workstations.map((station) => (
                      <SelectItem key={station.id} value={station.id?.toString() || ""}>
                        {station.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="editAutoEnded"
                  checked={newShift.auto_ended}
                  onCheckedChange={(checked) => setNewShift(prev => ({ ...prev, auto_ended: checked as boolean }))}
                />
                <Label htmlFor="editAutoEnded">Auto End Shift</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleEditShift} disabled={updateLoading}>
                {updateLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Update Shift
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Modal */}
        <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
          <DialogContent className="w-[95vw] max-w-md">
            <DialogHeader>
              <DialogTitle>Delete Shift</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this shift? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            {deletingShift && (
              <div className="py-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="font-medium">
                    {getEmployeeName(deletingShift.employee)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formatTime(deletingShift.start_time)} - {deletingShift.end_time ? formatTime(deletingShift.end_time) : "Open"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {getBranchName(deletingShift.branch)} - {getWorkstationName(deletingShift.workstation)}
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeleteShift} disabled={deleteLoading}>
                {deleteLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Delete Shift
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Shifts</CardTitle>
            <Play className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeShifts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clocked In</CardTitle>
            <UserCheck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{shiftEntries.length}</div>
            <p className="text-xs text-muted-foreground">
              Employees clocked in today
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{scheduledShifts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Late Entries</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{lateEntries}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5">
          <TabsTrigger value="calendar" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Shifts</span>
            <span className="sm:hidden">Shifts</span>
          </TabsTrigger>
          <TabsTrigger value="entries" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Clock Entries</span>
            <span className="sm:hidden">Entries</span>
          </TabsTrigger>
          <TabsTrigger value="employees" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Employees</span>
            <span className="sm:hidden">Employees</span>
          </TabsTrigger>
          <TabsTrigger value="branches" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Branches</span>
            <span className="sm:hidden">Branches</span>
          </TabsTrigger>
          <TabsTrigger value="logs" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Shift Logs</span>
            <span className="sm:hidden">Logs</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="calendar" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">All Shifts - {new Date().toLocaleDateString()}</CardTitle>
            </CardHeader>
            <CardContent className="p-0 sm:p-6">
              <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead className="hidden sm:table-cell">Workstation</TableHead>
                    <TableHead>Schedule</TableHead>
                    <TableHead className="hidden md:table-cell">Clock In/Out</TableHead>
                    <TableHead className="hidden lg:table-cell">Duration</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {shiftsWithStatus.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex flex-col items-center space-y-2">
                          <Calendar className="h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground">No shifts found</p>
                          <p className="text-sm text-muted-foreground">
                            Create a new shift to get started
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    shiftsWithStatus.map((shift) => {
                    const employeeName = getEmployeeName(shift.employee);
                    const branchName = getBranchName(shift.branch);
                    const revenueCenterName = getRevenueCenterName(shift.revenue_center);
                    const workstationName = getWorkstationName(shift.workstation);
                    const shiftEntry = getShiftEntryForShift(shift.code, shiftEntries);
                    const timingAnalysis = shiftEntry ? analyzeShiftEntryTiming(shiftEntry) : null;

                    return (
                      <TableRow key={shift.code}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar>
                              <AvatarFallback>
                                {employeeName.split(" ").map(n => n[0]).join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{employeeName}</div>
                              <div className="text-sm text-muted-foreground">
                                {shift.employee}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <div>
                            <div className="font-medium">{workstationName}</div>
                            <div className="text-sm text-muted-foreground">
                              {branchName} - {revenueCenterName}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{formatTime(shift.start_time)} - {shift.end_time ? formatTime(shift.end_time) : "Open"}</div>
                            <div className="sm:hidden text-xs text-muted-foreground mt-1">
                              {workstationName}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="text-sm">
                            <div className="flex items-center space-x-1">
                              <span>In: {shiftEntry ? formatTime(shiftEntry.start_time) : "Not clocked"}</span>
                              {timingAnalysis?.isLate && (
                                <AlertCircle className="h-3 w-3 text-red-500" title={`Late by ${timingAnalysis.startDiffMinutes} minutes`} />
                              )}
                              {timingAnalysis?.isEarly && (
                                <CheckCircle className="h-3 w-3 text-green-500" title={`Early by ${Math.abs(timingAnalysis.startDiffMinutes)} minutes`} />
                              )}
                            </div>
                            <div className="flex items-center space-x-1">
                              <span>Out: {shiftEntry?.end_time ? formatTime(shiftEntry.end_time) : "Not clocked"}</span>
                              {timingAnalysis?.isEarlyExit && (
                                <AlertCircle className="h-3 w-3 text-orange-500" title={`Early exit by ${Math.abs(timingAnalysis.endDiffMinutes)} minutes`} />
                              )}
                              {timingAnalysis?.isOvertime && (
                                <CheckCircle className="h-3 w-3 text-blue-500" title={`Overtime by ${timingAnalysis.endDiffMinutes} minutes`} />
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <div className="text-sm font-medium">
                            {calculateDuration(shift.start_time, shift.end_time)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(shift.status)}>
                            {shift.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            {shift.status === "Scheduled" && (
                              <Button
                                size="sm"
                                onClick={() => handleStartShift(shift.code)}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <Play className="h-3 w-3 sm:mr-1" />
                                <span className="hidden sm:inline">Start</span>
                              </Button>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem onClick={() => openEditModal(shift)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Shift
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Override Clock
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => openDeleteModal(shift)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Shift
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                  )}
                </TableBody>
              </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="entries" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Clock Entries - {new Date().toLocaleDateString()}</CardTitle>
            </CardHeader>
            <CardContent className="p-0 sm:p-6">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead className="hidden sm:table-cell">Shift</TableHead>
                      <TableHead>Clock In</TableHead>
                      <TableHead className="hidden md:table-cell">Clock Out</TableHead>
                      <TableHead className="hidden lg:table-cell">Duration</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {shiftEntriesLoading ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="flex items-center justify-center space-x-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Loading clock entries...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : shiftEntries.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="flex flex-col items-center space-y-2">
                            <UserCheck className="h-8 w-8 text-muted-foreground" />
                            <p className="text-muted-foreground">No clock entries found</p>
                            <p className="text-sm text-muted-foreground">
                              Employees will appear here when they clock in
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      shiftEntries.map((entry) => {
                        const employeeName = getEmployeeName(entry.shift);
                        const timingAnalysis = analyzeShiftEntryTiming(entry);
                        const shift = shifts.find(s => s.code === entry.shift);

                        return (
                          <TableRow key={entry.code}>
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <Avatar>
                                  <AvatarFallback>
                                    {employeeName.split(" ").map(n => n[0]).join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{employeeName}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {entry.shift}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="hidden sm:table-cell">
                              <div className="text-sm">
                                <div>{formatTime(entry.shift_start_time)} - {formatTime(entry.shift_end_time)}</div>
                                <div className="text-muted-foreground">
                                  {shift ? getWorkstationName(shift.workstation) : "Unknown"}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div className="flex items-center space-x-1">
                                  <span>{formatTime(entry.start_time)}</span>
                                  {timingAnalysis.isLate && (
                                    <AlertCircle className="h-3 w-3 text-red-500" title={`Late by ${timingAnalysis.startDiffMinutes} minutes`} />
                                  )}
                                  {timingAnalysis.isEarly && (
                                    <CheckCircle className="h-3 w-3 text-green-500" title={`Early by ${Math.abs(timingAnalysis.startDiffMinutes)} minutes`} />
                                  )}
                                </div>
                                <div className="sm:hidden text-xs text-muted-foreground mt-1">
                                  Scheduled: {formatTime(entry.shift_start_time)}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="hidden md:table-cell">
                              <div className="text-sm">
                                {entry.end_time ? (
                                  <div className="flex items-center space-x-1">
                                    <span>{formatTime(entry.end_time)}</span>
                                    {timingAnalysis.isEarlyExit && (
                                      <AlertCircle className="h-3 w-3 text-orange-500" title={`Early exit by ${Math.abs(timingAnalysis.endDiffMinutes)} minutes`} />
                                    )}
                                    {timingAnalysis.isOvertime && (
                                      <CheckCircle className="h-3 w-3 text-blue-500" title={`Overtime by ${timingAnalysis.endDiffMinutes} minutes`} />
                                    )}
                                  </div>
                                ) : (
                                  <span className="text-muted-foreground">Still working</span>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="hidden lg:table-cell">
                              <div className="text-sm font-medium">
                                {entry.end_time ? calculateDuration(entry.start_time, entry.end_time) : "In progress"}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col space-y-1">
                                {timingAnalysis.isLate && (
                                  <Badge variant="destructive" className="text-xs">
                                    Late ({timingAnalysis.startDiffMinutes}m)
                                  </Badge>
                                )}
                                {timingAnalysis.isEarly && (
                                  <Badge variant="secondary" className="text-xs">
                                    Early ({Math.abs(timingAnalysis.startDiffMinutes)}m)
                                  </Badge>
                                )}
                                {timingAnalysis.isEarlyExit && (
                                  <Badge variant="outline" className="text-xs">
                                    Early Exit
                                  </Badge>
                                )}
                                {timingAnalysis.isOvertime && (
                                  <Badge variant="outline" className="text-xs">
                                    Overtime
                                  </Badge>
                                )}
                                {!timingAnalysis.isLate && !timingAnalysis.isEarly && !timingAnalysis.isEarlyExit && !timingAnalysis.isOvertime && (
                                  <Badge variant="secondary" className="text-xs">
                                    On Time
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employees" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Shifts by Employee</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {employees.map((employee) => {
                  const employeeShifts = shifts.filter(shift =>
                    shift.employee === employee.employee_no || shift.employee === employee.id?.toString()
                  );
                  const employeeName = `${employee.first_name} ${employee.last_name}`;

                  return (
                    <div key={employee.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback>
                              {employeeName.split(" ").map(n => n[0]).join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{employeeName}</div>
                            <div className="text-sm text-muted-foreground">
                              {employee.employee_no}
                            </div>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {employeeShifts.length} shifts
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                        {employeeShifts.map((shift) => {
                          const status = getShiftStatus(shift);
                          return (
                            <div key={shift.code} className="text-sm p-3 bg-gray-50 rounded">
                              <div className="font-medium">
                                {formatTime(shift.start_time)} - {shift.end_time ? formatTime(shift.end_time) : "Open"}
                              </div>
                              <div className="text-muted-foreground">
                                {getWorkstationName(shift.workstation)}
                              </div>
                              <Badge className={getStatusColor(status)}>
                                {status}
                              </Badge>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branches" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Shifts by Branch</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {branches.map((branch) => {
                  const branchShifts = shifts.filter(shift => shift.branch === branch.id);
                  const activeShiftsCount = branchShifts.filter(shift => getShiftStatus(shift) === "Active").length;

                  return (
                    <div key={branch.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <MapPin className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{branch.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {branchShifts.length} shifts scheduled
                            </div>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {activeShiftsCount} active
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {branchShifts.map((shift) => {
                          const status = getShiftStatus(shift);
                          return (
                            <div key={shift.code} className="text-sm p-3 bg-gray-50 rounded">
                              <div className="font-medium">{getEmployeeName(shift.employee)}</div>
                              <div className="text-muted-foreground">
                                {getWorkstationName(shift.workstation)} ({formatTime(shift.start_time)} - {shift.end_time ? formatTime(shift.end_time) : "Open"})
                              </div>
                              <Badge className={getStatusColor(status)}>
                                {status}
                              </Badge>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Clock In/Out Logs</CardTitle>
            </CardHeader>
            <CardContent className="p-0 sm:p-6">
              <div className="overflow-x-auto">
                <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead className="hidden sm:table-cell">Date</TableHead>
                    <TableHead>Scheduled</TableHead>
                    <TableHead className="hidden md:table-cell">Actual</TableHead>
                    <TableHead className="hidden lg:table-cell">Duration</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {shifts.map((shift) => (
                    <TableRow key={shift.code}>
                      <TableCell>
                        <div className="font-medium">{getEmployeeName(shift.employee)}</div>
                        <div className="text-sm text-muted-foreground">
                          {shift.employee}
                        </div>
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">{formatDate(shift.start_time)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{formatTime(shift.start_time)} - {shift.end_time ? formatTime(shift.end_time) : "Open"}</div>
                          <div className="sm:hidden text-xs text-muted-foreground mt-1">
                            {formatDate(shift.start_time)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <div className="text-sm">
                          Not clocked - Not clocked
                        </div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        <div className="font-medium">
                          {calculateDuration(shift.start_time, shift.end_time)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Badge variant="secondary" className="text-xs">
                            No Clock Data
                          </Badge>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
    <Toaster />
    </Screen>
  );
}