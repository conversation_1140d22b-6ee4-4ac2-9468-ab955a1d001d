// API Response Types
export interface ApiResponse<T> {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: T[];
  };
}

export interface ApiSingleResponse<T> {
  message: string;
  data: T;
}

// Store Requisition Types (Exact API Match)
export interface StoreRequisition {
  // API Fields (exact match from your API response)
  id?: number;
  code?: string; // Auto-generated code like "STR-000001"
  requested_by: number | null; // Can be null in API response
  cost_center: number | null; // Can be null in API response
  store: number | null; // Can be null in API response
  status: "Draft" | "Submitted" | "Approved" | "Rejected" | "Issued" | "Converted";
  purpose: string;
  required_by: string; // Date string
  created_at?: string;
  updated_at?: string;
  items?: StoreRequisitionItem[];

  // Workflow tracking fields (added by our PATCH operations)
  submitted_at?: string;
  approved_at?: string;
  rejected_at?: string;
  converted_at?: string;
  rejection_reason?: string;

  // Additional fields for display (populated by joins)
  requested_by_name?: string;
  cost_center_name?: string;
  store_name?: string;
}

export interface StoreRequisitionItem {
  // API Fields (exact match from your API response)
  id?: number;
  quantity: string; // Decimal as string like "10.00"
  remarks?: string; // Optional remarks
  requisition: string; // Requisition code like "STR-000001"
  product: number | null; // Can be null in API response
  unit_of_measure: number; // Required

  // Additional fields for display (populated by joins)
  product_name?: string;
  unit_of_measure_name?: string;
  unit_of_measure_symbol?: string;
}

// Purchase Requisition Types (Exact API Match)
export interface PurchaseRequisition {
  // API Fields (exact match)
  id?: number; // readOnly
  code?: string; // Purchase Requisition code (e.g., "PR-000001")
  status?: "Draft" | "Submitted" | "Approved" | "Rejected";
  created_at?: string; // readOnly
  store_requisition?: string; // Store Requisition code (e.g., "STR-000003")
  created_by?: string; // Employee number (e.g., "OL/HR/634")

  // Workflow tracking fields (added by our PATCH operations)
  submitted_at?: string;
  approved_at?: string;
  rejected_at?: string;
  rejection_reason?: string;

  // Additional fields from API response (for display)
  store_requisition_number?: string;
  created_by_name?: string;
  purchase_items?: PurchaseRequisitionItem[];
}

export interface PurchaseRequisitionItem {
  // API Fields (exact match)
  id?: number; // readOnly
  quantity: string; // required, decimal as string
  requisition: number; // required
  product: number; // required
  unit_of_measure: number; // required

  // Additional fields from API response (for display)
  product_name?: string;
  product_code?: string;
  unit_of_measure_name?: string;
}

// RFQ Types (Exact API Match)
export interface RFQ {
  // API Fields (exact match)
  id?: number; // readOnly
  rfq_number: string; // required, maxLength: 100, minLength: 1
  response_deadline: string; // required, datetime field
  status?: "Open" | "Closed" | "Cancelled";
  created_at?: string; // readOnly
  requisition: number; // required - Purchase Requisition ID
  created_by: number; // required - User ID
  supplier_category: number; // required - Supplier Category ID

  // Workflow tracking fields (added by our PATCH operations)
  closed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;

  // Additional fields from API response (for display)
  requisition_number?: string;
  created_by_name?: string;
  supplier_category_name?: string;
  items?: RFQItem[];
  responses?: RFQResponse[];
}

export interface RFQItem {
  // API Fields (exact match)
  id?: number; // readOnly
  quantity: string; // required, decimal as string
  rfq: number; // required
  product: number; // required
  unit_of_measure: number; // required

  // Additional fields from API response (for display)
  product_name?: string;
  product_code?: string;
  unit_of_measure_name?: string;
}

export interface RFQResponse {
  // API Fields (exact match)
  id?: number; // readOnly
  submitted_at: string; // required, datetime field
  submitted_by: string; // required, maxLength: 100, minLength: 1
  notes?: string; // nullable
  rfq: number; // required
  supplier: number; // required

  // Additional fields from API response (for display)
  supplier_name?: string;
  items?: RFQResponseItem[];
}

export interface RFQResponseItem {
  // API Fields (exact match)
  id?: number; // readOnly
  unit_price: string; // required, decimal as string
  quantity: string; // required, decimal as string
  delivery_time_days?: number; // nullable
  currency: string; // required, maxLength: 10, minLength: 1
  total_price?: string; // nullable, decimal as string
  response: number; // required
  product: number; // required
  unit_of_measure: number; // required
  tax_rate?: number; // nullable

  // Additional fields from API response (for display)
  product_name?: string;
  unit_of_measure_name?: string;
}

// Bid Analysis Types (Exact API Match)
export interface BidAnalysis {
  // API Fields (exact match)
  id?: number; // readOnly
  split_award: boolean; // required - Allow awarding different items to different suppliers
  recommendation_notes: string; // required, minLength: 1
  finalized_at: string; // required, datetime field
  rfq: number; // required - RFQ ID
  created_by: number; // required - User ID
  selected_responses: number[]; // required, uniqueItems: true - Array of RFQ Response IDs

  // Additional fields from API response (for display)
  rfq_number?: string;
  created_by_name?: string;
  created_at?: string;
  lines?: BidAnalysisLine[];
}

export interface BidAnalysisLine {
  // API Fields (exact match)
  id?: number; // readOnly
  unit_price: string; // required, decimal as string
  quantity_awarded: string; // required, decimal as string
  delivery_time_days?: number; // nullable
  currency: string; // required, maxLength: 10, minLength: 1
  bid_analysis: number; // required
  rfq_item: number; // required
  supplier: number; // required

  // Additional fields from API response (for display)
  supplier_name?: string;
  rfq_item_product_name?: string;
  total_price?: string;
}

// Purchase Order Types (Exact API Match)
export interface PurchaseOrder {
  // API Fields (exact match from API response)
  id?: number; // readOnly
  po_number: string; // required, maxLength: 100, minLength: 1
  supplier: string; // required - supplier code
  status?: "Draft" | "Submitted" | "Approved" | "Cancelled" | "Closed" | "Partially Received";
  delivery_location?: string | null; // nullable, maxLength: 255
  payment_terms?: string | null; // nullable, maxLength: 255
  delivery_date?: string | null; // nullable, date field
  created_by: string; // required
  created_at?: string; // readOnly

  // Nested items from API response
  purchase_order_items?: PurchaseOrderItem[];

  // Workflow tracking fields (added by our PATCH operations)
  submitted_at?: string;
  approved_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;

  // Additional fields from API response (for display)
  supplier_name?: string;
  delivery_location_name?: string;
}

export interface PurchaseOrderItem {
  // API Fields (exact match from API response)
  id?: number; // readOnly
  quantity: string; // required, decimal as string
  unit_price: string; // required, decimal as string
  total_price: string; // required, decimal as string
  tax_amount: string; // required, decimal as string
  quantity_received: string; // required, decimal as string
  quantity_pending: string; // required, decimal as string
  purchase_order: string; // required - PO number
  product: string; // required - product code
  unit_of_measure: number; // required
  tax_rate?: number | null; // nullable

  // Additional fields from API response (for display)
  product_name?: string;
  unit_of_measure_name?: string;
}

// GRN Types (Exact API Match)
export interface GRN {
  // API Fields (exact match)
  id?: number; // readOnly
  grn_number: string; // required, maxLength: 100, minLength: 1
  status: "Partial" | "Full" | "Rejected"; // required
  received_date: string; // required, date-time
  purchase_order: number; // required
  received_by: number; // required
  store: number; // required

  // Additional fields from API response (for display)
  received_by_name?: string;
  store_name?: string;
  purchase_order_number?: string;
  supplier_name?: string;
  items?: GRNItem[];
  notes?: string;
  delivery_note?: string;
  invoice_attachment?: string;
}

export interface GRNItem {
  // API Fields (exact match)
  id?: number; // readOnly
  quantity_received: string; // required, decimal as string
  unit_price: string; // required, decimal as string
  total_price?: string; // nullable, decimal as string
  grn: number; // required
  product: number; // required
  unit_of_measure: number; // required
  tax_rate?: number; // nullable

  // Additional fields from API response (for display)
  product_name?: string;
  product_code?: string;
  unit_of_measure_name?: string;
  ordered_quantity?: number;
  remaining_quantity?: number;
  status?: "Received" | "Partial" | "Pending" | "Damaged";
  notes?: string;
  expiry_date?: string;
}

// Form Data Types for GRN
export interface GRNFormData {
  grn_number: string;
  status: "Partial" | "Full" | "Rejected";
  received_date: string;
  purchase_order: number;
  received_by: number;
  store: number;
  notes?: string;
  delivery_note?: string;
  items: GRNItemFormData[];
}

export interface GRNItemFormData {
  id?: number;
  quantity_received: string;
  unit_price: string;
  total_price?: string;
  grn?: number;
  product: number;
  unit_of_measure: number;
  tax_rate?: number;
  notes?: string;
  expiry_date?: string;
  status?: "Received" | "Partial" | "Pending" | "Damaged";
}

// Purchase Invoice Types (Exact API Match)
export interface Invoice {
  // API Fields (exact match)
  id?: number; // readOnly
  invoice_number: string; // required, unique
  invoice_date: string; // required, date
  supplier: number; // required
  grn: number; // required
  status: "Draft" | "Reviewed" | "Approved"; // required
  total_amount: string; // required, decimal as string
  currency: string; // required
  gl_account: number; // required
  invoice_file?: string; // nullable, file path
  created_at?: string; // readOnly
  created_by?: number; // readOnly
  reviewed_by?: number; // nullable
  reviewed_at?: string; // nullable
  approved_by?: number; // nullable
  approved_at?: string; // nullable
  payment_voucher?: number; // nullable
  notes?: string; // nullable

  // Additional fields from API response (for display)
  supplier_name?: string;
  grn_number?: string;
  gl_account_name?: string;
  created_by_name?: string;
  reviewed_by_name?: string;
  approved_by_name?: string;
  payment_voucher_number?: string;
  items?: InvoiceItem[];
}

export interface InvoiceItem {
  // API Fields (exact match)
  id?: number; // readOnly
  invoice: number; // required
  grn_item: number; // required
  product: number; // required
  quantity_invoiced: string; // required, decimal as string
  unit_price: string; // required, decimal as string
  total_price: string; // required, decimal as string
  variance_quantity: string; // required, decimal as string
  variance_amount: string; // required, decimal as string
  notes?: string; // nullable

  // Additional fields from API response (for display)
  product_name?: string;
  product_code?: string;
  grn_quantity?: string;
  grn_unit_price?: string;
  unit_of_measure_name?: string;
}

// Form Data Types for Invoice
export interface InvoiceFormData {
  invoice_number: string;
  invoice_date: string;
  supplier: number;
  grn: number;
  status: "Draft" | "Reviewed" | "Approved";
  total_amount: string;
  currency: string;
  gl_account: number;
  invoice_file?: string;
  notes?: string;
  items: InvoiceItemFormData[];
}

export interface InvoiceItemFormData {
  id?: number;
  invoice?: number;
  grn_item: number;
  product: number;
  quantity_invoiced: string;
  unit_price: string;
  total_price?: string;
  variance_quantity?: string;
  variance_amount?: string;
  notes?: string;
}

// Workflow Types
export interface InvoiceWorkflowAction {
  action: "submit" | "review" | "approve" | "reject";
  notes?: string;
}

export interface InvoiceAuditLog {
  id: number;
  action: string;
  user: string;
  timestamp: string;
  changes: Record<string, any>;
  notes?: string;
}

// GL Account Types
export interface GLAccount {
  id: number;
  account_code: string;
  account_name: string;
  account_type: string;
  is_active: boolean;
}

// Payment Voucher Types
export interface PaymentVoucher {
  id: number;
  voucher_number: string;
  amount: string;
  status: string;
  created_at: string;
}

// Supporting Types
export interface Product {
  id?: number;
  name?: string;
  code?: string;
  description?: string;
  category?: string;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
}

export interface CostCenter {
  id?: number;
  name?: string;
  code?: string;
  department?: string;
}

export interface Store {
  id?: number;
  name?: string;
  location?: string;
  manager?: string;
}

export interface UnitOfMeasure {
  id?: number;
  name?: string;
  abbreviation?: string;
}

// API Response Types
export interface ProcurementApiResponse<T> {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: T[];
  };
}

// Form Types for UI (matching API requirements)
export interface StoreRequisitionFormData {
  // Required fields (matching API)
  requested_by: number | ""; // required - user who is requesting
  cost_center: number | ""; // required
  store: number | ""; // required
  purpose: string; // required, maxLength: 255, minLength: 1
  required_by: string; // required, date field

  // Optional fields
  status?: "Draft" | "Submitted" | "Approved" | "Rejected" | "Converted"; // defaults to Draft

  // Items array
  items: {
    product: number | ""; // required
    quantity: string; // required, decimal as string
    unit_of_measure: number | ""; // required
    remarks: string; // optional/nullable
  }[];
}

// Form Types for UI (matching API requirements)
export interface PurchaseRequisitionFormData {
  // Required fields (matching API)
  store_requisition: number | ""; // required - links to Store Requisition
  created_by: number | ""; // required - user creating the PR

  // Optional fields
  status?: "Draft" | "Submitted" | "Approved" | "Rejected"; // defaults to Draft

  // Items array
  items: {
    product: number | ""; // required
    quantity: string; // required, decimal as string
    unit_of_measure: number | ""; // required
    requisition?: number; // will be set after PR creation
  }[];
}

// Form Types for UI (matching API requirements)
export interface PurchaseOrderFormData {
  // Required fields (matching API)
  po_number: string; // required, maxLength: 100, minLength: 1
  payment_terms: string; // required, maxLength: 255, minLength: 1
  delivery_date: string; // required, date field
  total_value: string; // required, decimal as string
  supplier: number | ""; // required
  delivery_location: number | ""; // required

  // Optional fields
  status?: "Draft" | "Submitted" | "Approved" | "Cancelled"; // defaults to Draft

  // Items array
  items: {
    product: number | ""; // required
    quantity: string; // required, decimal as string
    unit_price: string; // required, decimal as string
    unit_of_measure: number | ""; // required
    tax_rate?: number; // optional
    total_price?: string; // calculated field
    purchase_order?: number; // will be set after PO creation
  }[];
}

// Filter Types
export interface StoreRequisitionFilters {
  status?: string;
  cost_center?: number;
  store?: number;
  requested_by?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface PurchaseRequisitionFilters {
  status?: string;
  department?: string;
  supplier_category?: string;
  assigned_to?: number;
  priority?: string;
  cost_center?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface PurchaseOrderFilters {
  status?: string;
  supplier?: number;
  delivery_location?: number;
  currency?: string;
  requires_director_approval?: boolean;
  created_by?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface RFQFormData {
  // Required API fields
  rfq_number: string;
  response_deadline: string;
  requisition: number | string;
  created_by: number | string;
  supplier_category: number | string;
  status?: "Open" | "Closed" | "Cancelled";

  // Items to be created separately
  items: {
    quantity: string;
    product: number | "";
    unit_of_measure: number | "";
  }[];
}

export interface RFQFilters {
  status?: string;
  supplier?: number;
  delivery_location?: number;
  category?: string;
  created_by?: number;
  date_from?: string;
  date_to?: string;
  response_deadline_from?: string;
  response_deadline_to?: string;
  search?: string;
}

export interface RFQResponseFormData {
  // Required API fields
  submitted_at: string;
  submitted_by: string;
  rfq: number;
  supplier: number;
  notes?: string;

  // Items to be created separately
  items: {
    unit_price: string;
    quantity: string;
    delivery_time_days?: number;
    currency: string;
    total_price?: string;
    product: number;
    unit_of_measure: number;
    tax_rate?: number;
  }[];
}

// RFQ Response Creation Form Data (matches the specific API structure)
export interface RFQResponseCreateFormData {
  supplier_id: number;
  notes?: string;
  items: {
    unit_price: string;
    delivery_time_days?: number;
    currency: string;
    tax_rate?: number;
  }[];
}

// Tax Rate Type
export interface TaxRate {
  id: number;
  name: string;
  percentage: string;
  is_active: boolean;
  branch: string;
}

// Supporting Types for Purchase Requisitions
export interface ProcurementOfficer {
  id?: number;
  name?: string;
  email?: string;
  department?: string;
  employee_no?: string;
}

export interface Department {
  id?: number;
  name?: string;
  code?: string;
  manager?: string;
}

export interface SupplierCategory {
  id?: number;
  name?: string;
  description?: string;
}

export interface Supplier {
  id?: number;
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  contact_person?: string;
  tax_number?: string;
  registration_number?: string;
  category?: string;
  status?: "Active" | "Inactive" | "Suspended";
  payment_terms?: string;
  credit_limit?: number;
  currency?: string;
}

export interface PaymentTerm {
  id?: number;
  name?: string;
  description?: string;
  days?: number;
}

export interface Currency {
  id?: number;
  code?: string;
  name?: string;
  symbol?: string;
  exchange_rate?: number;
}





// Bid Analysis Form Data (Exact API Match)
export interface BidAnalysisFormData {
  // Required API fields
  split_award: boolean;
  recommendation_notes: string;
  finalized_at: string;
  rfq: number | string;
  created_by: number | string;
  selected_responses: number[];

  // Lines to be created separately
  lines: {
    unit_price: string;
    quantity_awarded: string;
    delivery_time_days?: number;
    currency: string;
    rfq_item: number | "";
    supplier: number | "";
  }[];
}


