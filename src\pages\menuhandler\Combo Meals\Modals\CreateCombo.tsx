import React, { useState, useEffect } from "react";
import { X, Utensils, Clock, Plus, Trash2, ShoppingCart, Star, Zap } from "lucide-react";
import { useGetMenusQuery } from "@/redux/slices/menuMake";
import { useGetMenuGroupsQuery } from "@/redux/slices/menuGroup";
import { useAddComboMenuMutation } from "@/redux/slices/comboMenu";
import { useGetMenuSubGroupsQuery } from "@/redux/slices/menuSubGroup";

interface ComboMealItem {
  id: string;
  name: string;
  category: string;
  subCategory?: string;
  price: number;
  quantity: number;
  menuItemId: number;
}

interface CreateComboMealModalProps {
  onClose: () => void;
  onSave: (newCombo: {
    name: string;
    code: string;
    price: string;
    description: string;
    menu_group: number;
    items: ComboMealItem[];
  }) => void;
}

interface MenuGroup {
  id: number;
  name: string;
  sales_category: string;
}

interface MenuSubGroup {
  id: number;
  name: string;
  description: string;
  position: number;
  group: number;
}

interface MenuItem {
  id: number;
  name: string;
  price: string;
  description: string;
  button_color: string;
  availability_type: string;
  available_days: object;
  available_times: object;
  ordering_channels: object;
  stock_status: boolean;
  is_active: boolean;
  pos_name: string;
  menu_group: number;
  menu_subgroup?: number;
  product: any;
  store: number;
  printer: any;
}

export function CreateComboMealModal({ onClose, onSave }: CreateComboMealModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    menu_group: 0,
    items: [] as ComboMealItem[],
    discount: 0,
  });

  // Data fetching hooks
  const { data: menuData, isLoading: menusLoading, error: menusError } = useGetMenusQuery({});
  const { data: menuGroups, isLoading: groupsLoading, error: groupsError } = useGetMenuGroupsQuery({});
  const { data: menuSubGroups, isLoading: subGroupsLoading, error: subGroupsError } = useGetMenuSubGroupsQuery({});
  const [addCombo, { isLoading: isCreating, error: createError }] = useAddComboMenuMutation();

  // Form state for adding items
  const [selectedMenuSubGroup, setSelectedMenuSubGroup] = useState("");
  const [selectedMenuItem, setSelectedMenuItem] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [filteredMenuItems, setFilteredMenuItems] = useState<MenuItem[]>([]);
  const [filteredSubGroups, setFilteredSubGroups] = useState<MenuSubGroup[]>([]);

  // Memoized data processing
  const menuItems: MenuItem[] = menuData?.data?.results || [];
  const allMenuGroups = menuGroups?.data?.results || [];
  const allSubGroups: MenuSubGroup[] = menuSubGroups?.data?.results || [];

  // Calculate prices
  const subtotal = formData.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const total = Math.max(0, subtotal - formData.discount);

  // Effect 1: Handle menu group changes - reset selections and filter subgroups
  useEffect(() => {
    if (formData.menu_group > 0) {
      const groupId = formData.menu_group;
      
      console.log('Selected group ID:', groupId, 'Type:', typeof groupId);
      console.log('All subgroups:', allSubGroups);
      
      // Filter sub-groups for selected menu group - ensure type matching
      const subGroupsForGroup = allSubGroups.filter(
        subGroup => {
          const matches = Number(subGroup.group) === Number(groupId);
          console.log(`SubGroup ${subGroup.name}: group=${subGroup.group} (${typeof subGroup.group}), matches=${matches}`);
          return matches;
        }
      );
      
      console.log('Filtered subgroups:', subGroupsForGroup);
      setFilteredSubGroups(subGroupsForGroup);
    } else {
      setFilteredSubGroups([]);
    }
    
    // Reset selections when group changes
    setSelectedMenuSubGroup("");
    setSelectedMenuItem("");
  }, [formData.menu_group, allSubGroups]);

  // Effect 2: Handle filtering menu items when group or subgroup changes
  useEffect(() => {
    if (formData.menu_group > 0) {
      const groupId = formData.menu_group;
      
      console.log('Filtering menu items for group:', groupId);
      console.log('All menu items:', menuItems);
      console.log('Selected subgroup:', selectedMenuSubGroup);
      
      // Filter menu items based on group and optionally sub-group - ensure type matching
      let filtered = menuItems.filter(item => {
        const groupMatches = Number(item.menu_group) === Number(groupId);
        const isActive = item.is_active;
        console.log(`Item ${item.name}: menu_group=${item.menu_group}, groupMatches=${groupMatches}, isActive=${isActive}`);
        return groupMatches && isActive;
      });
      
      console.log('Items after group filter:', filtered);
      
      if (selectedMenuSubGroup) {
        const subGroupId = parseInt(selectedMenuSubGroup);
        filtered = filtered.filter(item => {
          const subGroupMatches = Number(item.menu_subgroup) === subGroupId;
          console.log(`Item ${item.name}: menu_subgroup=${item.menu_subgroup}, subGroupMatches=${subGroupMatches}`);
          return subGroupMatches;
        });
        console.log('Items after subgroup filter:', filtered);
      }
      
      setFilteredMenuItems(filtered);
    } else {
      setFilteredMenuItems([]);
    }
    
    // Reset menu item selection when subgroup changes
    if (selectedMenuSubGroup) {
      setSelectedMenuItem("");
    }
  }, [formData.menu_group, selectedMenuSubGroup, menuItems]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === "menu_group" ? parseInt(value) || 0 : 
              name === "discount" ? parseFloat(value) || 0 : value,
    }));
  };

  const showAlert = (message: string) => alert(message);

  const validateForm = () => {
    if (!formData.name.trim()) return "Please enter a combo name.";
    if (!formData.code.trim()) return "Please enter a combo code.";
    if (formData.menu_group === 0) return "Please select a menu group.";
    if (formData.items.length === 0) return "Please add at least one item to the combo.";
    if (!formData.description.trim()) return "Please enter a description.";
    return null;
  };

  const handleAddItem = () => {
    if (!selectedMenuItem || quantity < 1) return;

    const menuItem = filteredMenuItems.find(item => item.id.toString() === selectedMenuItem);
    const menuGroup = allMenuGroups.find(group => group.id === formData.menu_group);
    const menuSubGroup = selectedMenuSubGroup 
      ? filteredSubGroups.find(sg => sg.id.toString() === selectedMenuSubGroup)
      : null;

    if (!menuItem || !menuGroup) return;

    // Check for duplicates
    const existingItem = formData.items.find(item => item.menuItemId === menuItem.id);
    if (existingItem) {
      showAlert(`${menuItem.name} is already added to this combo.`);
      return;
    }

    const newItem: ComboMealItem = {
      id: `${menuItem.id}-${Date.now()}`,
      name: menuItem.name,
      category: menuGroup.name,
      subCategory: menuSubGroup?.name,
      price: parseFloat(menuItem.price),
      quantity,
      menuItemId: menuItem.id,
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
    
    // Reset selection
    setSelectedMenuItem("");
    setQuantity(1);
  };

  const handleRemoveItem = (id: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id),
    }));
  };

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      ),
    }));
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      showAlert(validationError);
      return;
    }

    try {
      const comboData = {
        name: formData.name.trim(),
        code: formData.code.trim(),
        price: total.toFixed(2),
        description: formData.description.trim(),
        menu_group: formData.menu_group,
        items: formData.items.map(item => ({
          menu_item_id: item.menuItemId,
          quantity: item.quantity,
          price: item.price
        }))
      };

      console.log('Creating combo with data:', comboData);
      const result = await addCombo(comboData).unwrap();
      console.log('Combo created successfully:', result);

      onSave({ ...formData, price: total.toFixed(2) });
      showAlert('Combo meal created successfully!');
      onClose();
      
    } catch (error: any) {
      console.error("Failed to create combo meal:", error);
      
      let errorMessage = "Please check your inputs and try again.";
      
      if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.data?.errors) {
        errorMessage = Object.values(error.data.errors).flat().join(', ');
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      showAlert(`Failed to create combo meal. ${errorMessage}`);
    }
  };

  const inputClassName = "w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-300 bg-gradient-to-r from-gray-50 to-white";
  const selectClassName = "w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-200";

  // Loading state
  if (menusLoading || groupsLoading || subGroupsLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-gradient-to-br from-orange-900/50 via-red-900/50 to-yellow-900/50 backdrop-blur-md" />
        <div className="relative bg-white rounded-3xl shadow-2xl p-8">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-lg font-medium text-gray-700">Loading menu data...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (menusError || groupsError || subGroupsError) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-gradient-to-br from-orange-900/50 via-red-900/50 to-yellow-900/50 backdrop-blur-md" onClick={onClose} />
        <div className="relative bg-white rounded-3xl shadow-2xl p-8 max-w-md">
          <div className="text-center">
            <X className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Data</h3>
            <p className="text-gray-600 mb-4">
              Unable to load menu data. Please try again later.
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div
        className="absolute inset-0 bg-gradient-to-br from-orange-900/50 via-red-900/50 to-yellow-900/50 backdrop-blur-md animate-in fade-in duration-500"
        onClick={onClose}
      />

      <div className="relative bg-white rounded-3xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-500">
        {/* Header */}
        <div className="relative bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 p-8 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-white/10 rounded-full animate-pulse"></div>
            <div className="absolute top-8 right-8 w-16 h-16 bg-white/10 rounded-full animate-bounce delay-300"></div>
            <div className="absolute bottom-4 left-1/3 w-12 h-12 bg-white/10 rounded-full animate-ping delay-700"></div>
          </div>

          <div className="relative flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm border border-white/30">
                <Utensils className="h-8 w-8 text-white" />
              </div>
              <div>
                <h2 className="text-3xl font-bold text-white">Create Combo Meal</h2>
                <p className="text-white/90 text-sm flex items-center mt-1">
                  <Star className="h-4 w-4 mr-1" />
                  Build your ultimate food combination
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-110"
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>
        </div>

        {/* Form Content */}
        <div className="overflow-y-auto max-h-[calc(95vh-200px)]">
          <div className="p-8 space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="group">
                <label className="block text-sm font-bold text-gray-800 mb-2 flex items-center">
                  <Zap className="h-4 w-4 mr-2 text-purple-600" />
                  Combo Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  placeholder="Enter your awesome combo name..."
                  className={inputClassName}
                />
              </div>
              <div className="group">
                <label className="block text-sm font-bold text-gray-800 mb-2 flex items-center">
                  <Zap className="h-4 w-4 mr-2 text-purple-600" />
                  Combo Code
                </label>
                <input
                  type="text"
                  name="code"
                  value={formData.code}
                  onChange={handleChange}
                  required
                  placeholder="Enter unique combo code..."
                  className={inputClassName}
                />
              </div>
            </div>

            {/* Menu Group & Discount */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">Menu Group</label>
                <select
                  name="menu_group"
                  value={formData.menu_group}
                  onChange={handleChange}
                  className={selectClassName}
                  required
                >
                  <option value="0">Select Menu Group</option>
                  {allMenuGroups.map((group: MenuGroup) => (
                    <option key={group.id} value={group.id}>
                      {group.name} ({group.sales_category})
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">Discount (Optional)</label>
                <input
                  type="number"
                  name="discount"
                  value={formData.discount}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  className={selectClassName}
                />
              </div>
            </div>

            {/* Add Items Section */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                <Plus className="h-5 w-5 mr-2 text-blue-600" />
                Add Items to Combo
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Sub Group (Optional)</label>
                  <select
                    value={selectedMenuSubGroup}
                    onChange={(e) => setSelectedMenuSubGroup(e.target.value)}
                    disabled={formData.menu_group === 0 || filteredSubGroups.length === 0}
                    className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 transition-all duration-200 disabled:bg-gray-100"
                  >
                    <option value="">
                      {formData.menu_group === 0 ? "Select menu group first" : 
                       filteredSubGroups.length === 0 ? "No sub-groups available" : "All sub-groups"}
                    </option>
                    {filteredSubGroups.map((subGroup: MenuSubGroup) => (
                      <option key={subGroup.id} value={subGroup.id}>
                        {subGroup.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Menu Item</label>
                  <select
                    value={selectedMenuItem}
                    onChange={(e) => setSelectedMenuItem(e.target.value)}
                    disabled={formData.menu_group === 0}
                    className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 transition-all duration-200 disabled:bg-gray-100"
                  >
                    <option value="">
                      {formData.menu_group === 0 ? "Select menu group first" : "Select menu item"}
                    </option>
                    {filteredMenuItems.map((item: MenuItem) => (
                      <option key={item.id} value={item.id}>
                        {item.name} (${parseFloat(item.price).toFixed(2)})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                  <input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                    min="1"
                    className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 transition-all duration-200"
                  />
                </div>
              </div>

              <div className="flex justify-end mt-4">
                <button
                  type="button"
                  onClick={handleAddItem}
                  disabled={!selectedMenuItem}
                  className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:from-gray-300 disabled:to-gray-400 transition-all duration-200 hover:shadow-lg transform hover:scale-105 disabled:transform-none flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Item</span>
                </button>
              </div>
            </div>

            {/* Selected Items */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-800 flex items-center">
                  <ShoppingCart className="h-5 w-5 mr-2 text-green-600" />
                  Selected Items ({formData.items.length})
                </h3>
                <div className="text-right">
                  <div className="text-sm text-gray-600">Subtotal: ${subtotal.toFixed(2)}</div>
                  {formData.discount > 0 && (
                    <div className="text-sm text-red-600">Discount: -${formData.discount.toFixed(2)}</div>
                  )}
                  <div className="text-2xl font-bold text-green-600">
                    Total: ${total.toFixed(2)}
                  </div>
                </div>
              </div>

              {formData.items.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                  <p className="text-gray-500">No items added yet. Start building your combo!</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {formData.items.map(item => (
                    <div key={item.id} className="bg-white rounded-xl p-4 shadow-sm border border-green-100 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-800">{item.name}</h4>
                          <div className="text-sm text-gray-600">
                            <span>{item.category}</span>
                            {item.subCategory && <span> → {item.subCategory}</span>}
                          </div>
                          <p className="text-lg font-bold text-green-600">${(item.price * item.quantity).toFixed(2)}</p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            <label className="text-sm font-medium text-gray-700">Qty:</label>
                            <input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value) || 1)}
                              min="1"
                              className="w-16 px-2 py-1 rounded border border-gray-300 focus:border-green-500 focus:ring-0 text-center text-sm"
                            />
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveItem(item.id)}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200 hover:scale-110"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-bold text-gray-800 mb-2">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={4}
                placeholder="Describe the combo..."
                className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-300 resize-none"
              />
            </div>

            {/* Error Display */}
            {createError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm font-medium text-red-700">
                  Error: {typeof createError === 'string' ? createError : JSON.stringify(createError)}
                </p>
              </div>
            )}

            {/* Submit Button */}
            <div className="pt-6">
              <button
                type="button"
                onClick={handleSubmit}
                disabled={isCreating || formData.items.length === 0}
                className="w-full px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02] disabled:transform-none disabled:from-gray-400 disabled:to-gray-500 flex items-center justify-center space-x-2"
              >
                {isCreating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Star className="h-5 w-5" />
                    <span className="text-lg">Create Combo Meal</span>
                  </>
                )}
              </button>
              
              <div className="mt-4 text-center text-sm text-gray-600">
                <p>Total Items: {formData.items.length} | Final Price: <span className="font-semibold text-green-600">${total.toFixed(2)}</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}