import React, { useState, useMemo } from 'react';
import { 
  useGetInventoryItemQuery, 
  useDeleteInventoryItemMutation 
} from '@/redux/slices/inventory';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { InventoryFilters, type FilterOptions } from './InventoryFilters';
import { InventoryTableView } from './InventoryTableView';
import { Plus, Search, AlertTriangle } from 'lucide-react';
import type { InventoryItem } from '../types/inventory-item.type';

interface InventoryListProps {
  onEdit: (item: InventoryItem) => void;
  onAdd: () => void;
}

export const InventoryList: React.FC<InventoryListProps> = ({ onEdit, onAdd }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    stockStatus: 'all',
    expiryStatus: 'all',
    sortBy: 'name',
    sortOrder: 'asc',
  });
  
  const { data, error, isLoading, refetch } = useGetInventoryItemQuery({params:{}});
  const [deleteItem, { isLoading: isDeleting }] = useDeleteInventoryItemMutation();

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this inventory item?')) {
      try {
        await deleteItem(id).unwrap();
      } catch (error) {
        console.error('Failed to delete item:', error);
      }
    }
  };

  const handleSort = (column: FilterOptions['sortBy']) => {
    if (filters.sortBy === column) {
      setFilters(prev => ({
        ...prev,
        sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc'
      }));
    } else {
      setFilters(prev => ({
        ...prev,
        sortBy: column,
        sortOrder: 'asc'
      }));
    }
  };


  const isExpiringSoon = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  const isExpired = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  };

  const filteredAndSortedItems = useMemo(() => {
    let items = data?.results || [];
    
    // Apply search filter
    if (searchTerm) {
      items = items.filter(item =>
        item.product.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.store.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.branch.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply stock status filter
    if (filters.stockStatus !== 'all') {
      items = items.filter(item => {
        switch (filters.stockStatus) {
          case 'in-stock':
            return item.quantity_available > item.reorder_level;
          case 'low-stock':
            return item.quantity_available <= item.reorder_level && item.quantity_available > 0;
          case 'out-of-stock':
            return item.quantity_available <= 0;
          default:
            return true;
        }
      });
    }
    
    // Apply expiry status filter
    if (filters.expiryStatus !== 'all') {
      items = items.filter(item => {
        switch (filters.expiryStatus) {
          case 'expiring-soon':
            return isExpiringSoon(item.expiry_date);
          case 'expired':
            return isExpired(item.expiry_date);
          case 'no-expiry':
            return !item.expiry_date;
          default:
            return true;
        }
      });
    }
    
    // Apply sorting
    items = [...items].sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filters.sortBy) {
        case 'name':
          aValue = a.product?.toLowerCase();
          bValue = b.product?.toLowerCase();
          break;
        case 'quantity':
          aValue = a.quantity_available;
          bValue = b.quantity_available;
          break;
        case 'expiry':
          aValue = a.expiry_date ? new Date(a.expiry_date).getTime() : 0;
          bValue = b.expiry_date ? new Date(b.expiry_date).getTime() : 0;
          break;
        case 'updated':
          aValue = new Date(a.last_updated).getTime();
          bValue = new Date(b.last_updated).getTime();
          break;
        default:
          return 0;
      }
      
      if (filters.sortOrder === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });
    
    return items;
  }, [data?.results, searchTerm, filters]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading inventory...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Failed to load inventory items</span>
          </div>
          <Button 
            onClick={() => refetch()} 
            variant="outline" 
            className="mt-4"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Inventory Management</h1>
          <p className="text-muted-foreground">
            Manage your inventory items, track stock levels, and monitor expiry dates
          </p>
        </div>
        <Button onClick={onAdd} className="flex items-center gap-2 cursor-pointer">
          <Plus className="h-4 w-4" />
          Add Item
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search by product, store, or branch..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 focus-visible:ring-0"
        />
      </div>

      {/* Filters */}
      <InventoryFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={() => setFilters({
          stockStatus: 'all',
          expiryStatus: 'all',
          sortBy: 'name',
          sortOrder: 'asc',
        })}
      />

      {/* Items Table */}
      <InventoryTableView
        items={filteredAndSortedItems}
        filters={filters}
        onEdit={onEdit}
        onDelete={handleDelete}
        onSort={handleSort}
        onAdd={onAdd}
        isDeleting={isDeleting}
        searchTerm={searchTerm}
      />
    </div>
  );
};