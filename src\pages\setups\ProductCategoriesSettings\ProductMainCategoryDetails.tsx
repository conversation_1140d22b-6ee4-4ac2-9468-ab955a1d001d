import React, { useState } from "react";
import { useNavigate, usePara<PERSON>, <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Screen } from "@/app-components/layout/screen";
import { ArrowLeft, Loader2 } from "lucide-react";
import ProductCategoryPage from "./ProductCategoriesPage";
import AddProductCategory from "./modals/AddProductCategory";
import { ProductMainCategory } from "@/types/products";
import { mainCategoryTestData } from "./ProductCategoryTestData";
import { useRetrieveProductMainCategoryQuery } from "@/redux/slices/productCategories";

const ProductMainCategoryDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const { data: mainCategory, isLoading: loadingMainCategory } =
    useRetrieveProductMainCategoryQuery(id);

  return (
    <Screen>
      <div className=" mx-auto px-4 py-6">
        {/* Header */}
        {loadingMainCategory ? (
          <div>
            <Loader2 className="animate-spin h-5 w-5 text-gray-500" />
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <Button variant="ghost" onClick={() => navigate(-1)}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <h1 className="text-2xl font-bold">{mainCategory.name}</h1>
              </div>
              {/* <Button variant="outline" onClick={() => setIsAddModalOpen(true)}>
            Add Product Category
          </Button> */}
            </div>

            {/* Main Category Details Card */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Main Category Details</CardTitle>
                <CardDescription>
                  View and manage main category information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm text-gray-500">Name</h3>
                    <p className="text-lg font-medium">{mainCategory.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-500">Code</h3>
                    <p className="text-lg font-medium">{mainCategory.code}</p>
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-500">Description</h3>
                    <p className="text-lg font-medium">
                      {mainCategory.description}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-500">Cost Center</h3>
                    <p className="text-lg font-medium">
                      {mainCategory.cost_center}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Product Categories Section */}
        <div className="mt-8">
          {/* <h2 className="text-xl font-semibold mb-4">Product Categories</h2> */}
          <ProductCategoryPage
            main_category_id={mainCategory?.id}
            main_category_code={mainCategory?.code}
          />
        </div>

        {/* Add Product Category Modal */}
        <AddProductCategory
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          main_category_code={mainCategory?.code}
        />
      </div>
    </Screen>
  );
};

export default ProductMainCategoryDetails;
