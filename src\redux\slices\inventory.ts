import { CreateInventoryItemRequest, InventoryApiResponse, InventoryItem } from "@/pages/inventory/types/inventory-item.type";
import { apiSlice } from "../apiSlice";

export const inventoryApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getInventoryItem: builder.query<InventoryApiResponse, {params:{}}>({
            query: (params) => ({
                url: `/inventory/inventory-items`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<InventoryApiResponse>) => response.data,
            providesTags: ["/inventory/inventory-items"],
        }),
        updateInventoryItem: builder.mutation<InventoryItem, { id: number; body: Partial<CreateInventoryItemRequest> }>({
            query: ({ id, body }) => ({
                url: `/inventory/inventory-items/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/inventory/inventory-items"],
        }),
        createInventoryItem: builder.mutation<InventoryItem, CreateInventoryItemRequest>({
            query: (body) => ({
                url: `/inventory/inventory-items`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/inventory/inventory-items"],
        }),
        getOneInventoryItem: builder.query<InventoryItem, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/inventory/inventory-items/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/inventory/inventory-items"],
        }),
        deleteInventoryItem: builder.mutation<void, number>({
            query: (id) => ({
                url: `/inventory/inventory-items/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/inventory/inventory-items"],
        }),
    })
})

export const {
    useGetInventoryItemQuery,
    useUpdateInventoryItemMutation,
    useCreateInventoryItemMutation,
    useGetOneInventoryItemQuery,
    useDeleteInventoryItemMutation,
} = inventoryApiSlice

interface ApiResponse<T> {
  data: T;
  message: string;
}