import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { searchDebouncer } from "@/utils/debouncers";
import { Link } from "react-router-dom";
import AddProductCategory from "./modals/AddProductCategory";
import { ProductCategory } from "@/types/products";
import { categoryTestData } from "./ProductCategoryTestData";
import {
  useGetProductCategoriesQuery,
  useGetProductMainCategoriesQuery,
} from "@/redux/slices/productCategories";

type propTypes = {
  main_category_id: string;
  main_category_code: string;
};

const ProductCategoryPage = ({
  main_category_id,
  main_category_code,
}: propTypes) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] =
    useState<ProductCategory | null>(null);

  const {
    data: productCategories,
    isLoading,
    isFetching,
    isError,
    error,
  } = useGetProductCategoriesQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
    main_category: main_category_code,
  });

  const columns: ColumnDef<ProductCategory>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => row.original.name,
      // <Link
      //   to={`/product-categories/${row.original.main_category_id}/${row.original.id}`}
      // >
      //   <span className="font-medium underline capitalize text-blue-400">
      //   </span>
      // </Link>
      enableColumnFilter: false,
    },
    {
      accessorKey: "code",
      header: "Code",
      enableColumnFilter: false,
    },
    {
      accessorKey: "description",
      header: "Description",
      enableColumnFilter: false,
    },
    {
      accessorKey: "main_category",
      header: "Main Category",
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row.original)}
          >
            Edit
          </Button>
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  const handleEdit = (category: ProductCategory) => {
    setSelectedCategory(category);
    setIsEditModalOpen(true);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold">Products Sub Categories</h1>
        <div className="flex items-center gap-2">
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            Add Sub Category
          </Button>
        </div>
      </div>

      <DataTable<ProductCategory>
        data={productCategories?.data?.results || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-gray-50"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search categories..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={productCategories?.data?.total_data || 0}
      />

      {/* Modal Components */}
      {isAddModalOpen && (
        <AddProductCategory
          main_category_code={main_category_code}
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}

      {isEditModalOpen && (
        <AddProductCategory
          main_category_code={main_category_code}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          updateData={selectedCategory!}
        />
      )}
    </div>
  );
};

export default ProductCategoryPage;
