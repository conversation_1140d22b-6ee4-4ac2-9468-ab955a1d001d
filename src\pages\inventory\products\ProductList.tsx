import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pencil, Trash2, Plus, Search, Package, Filter, ArrowUpDown, ArrowUp, ArrowDown, X } from 'lucide-react';
import { useGetProductSubCategoryQuery } from '@/redux/slices/product-sub-category';
import { ProductItem } from '../types/product.type';
import { useDeleteProductMutation, useGetProductQuery } from '@/redux/slices/product';

interface ProductListProps {
  onEdit: (item: ProductItem) => void;
  onAdd: () => void;
}

type SortField = 'name' | 'code' | 'unit_price' | 'sub_category' | 'id';
type SortDirection = 'asc' | 'desc';

export const ProductList: React.FC<ProductListProps> = ({
  onEdit,
  onAdd
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [perishableFilter, setPerishableFilter] = useState<string>('all');
  const [subCategoryFilter, setSubCategoryFilter] = useState<string>('all');
  const [priceRangeMin, setPriceRangeMin] = useState<string>('');
  const [priceRangeMax, setPriceRangeMax] = useState<string>('');
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [deleteItem] = useDeleteProductMutation();

  const { data, isLoading, error } = useGetProductQuery({ params: {} });
  const { data: subCategories } = useGetProductSubCategoryQuery({ params: {} });

  const getSubCategoryName = (subCategoryId: string) => {
    const category = subCategories?.results?.find(cat => cat.id.toString() === subCategoryId);
    return category ? category.name : subCategoryId;
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteItem(id).unwrap();
      } catch (err) {
        console.error('Failed to delete product:', err);
      }
    }
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setPerishableFilter('all');
    setSubCategoryFilter('all');
    setPriceRangeMin('');
    setPriceRangeMax('');
    setSortField('name');
    setSortDirection('asc');
  };

  const hasActiveFilters = searchTerm || statusFilter !== 'all' || perishableFilter !== 'all' ||
    subCategoryFilter !== 'all' || priceRangeMin || priceRangeMax || sortField !== 'name' || sortDirection !== 'asc';

  const filteredAndSortedItems = useMemo(() => {
    let filtered = data?.results?.filter(item => {
      const matchesSearch = !searchTerm ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        getSubCategoryName(item.sub_category).toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'active' && item.is_active) ||
        (statusFilter === 'inactive' && !item.is_active);

      const matchesPerishable = perishableFilter === 'all' ||
        (perishableFilter === 'perishable' && item.perishable) ||
        (perishableFilter === 'non-perishable' && !item.perishable);

      const matchesSubCategory = subCategoryFilter === 'all' || item.sub_category === subCategoryFilter;

      const price = parseFloat(item.unit_price);
      const matchesPriceMin = !priceRangeMin || price >= parseFloat(priceRangeMin);
      const matchesPriceMax = !priceRangeMax || price <= parseFloat(priceRangeMax);

      return matchesSearch && matchesStatus && matchesPerishable && matchesSubCategory && matchesPriceMin && matchesPriceMax;
    }) || [];

    // Sort the filtered results
    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'code':
          aValue = a.code.toLowerCase();
          bValue = b.code.toLowerCase();
          break;
        case 'unit_price':
          aValue = parseFloat(a.unit_price);
          bValue = parseFloat(b.unit_price);
          break;
        case 'sub_category':
          aValue = getSubCategoryName(a.sub_category).toLowerCase();
          bValue = getSubCategoryName(b.sub_category).toLowerCase();
          break;
        case 'id':
          aValue = a.id;
          bValue = b.id;
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [data?.results, searchTerm, statusFilter, perishableFilter, subCategoryFilter, priceRangeMin, priceRangeMax, sortField, sortDirection, subCategories]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2 text-gray-600">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
            Loading products...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Failed to load products</p>
            <p className="text-sm text-gray-500 mt-1">Please try again later</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-5'>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search products..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 w-full focus-visible:ring-0"
        />
      </div>

      <div className="mt-4 p-4 rounded-lg border">
        <div className="flex flex-col md:flex-row gap-4">
          <div className='flex-1'>
            <label className="text-sm font-medium text-gray-700 mb-1 block">
              Sub Category
            </label>
            <Select value={subCategoryFilter} onValueChange={setSubCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All sub categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All sub categories</SelectItem>
                {subCategories?.results?.map((category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className='flex-1'>
            <label className="text-sm font-medium text-gray-700 mb-1 block">
              Status
            </label>
            <Select value={perishableFilter} onValueChange={setPerishableFilter}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="perishable">Perishable</SelectItem>
                <SelectItem value="non-perishable">Non-Perishable</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='flex-1'>
            <label className="text-sm font-medium text-gray-700 mb-1 block">
              Min Price (Ksh)
            </label>
            <Input
              type="number"
              step="0.01"
              placeholder="0.00"
              value={priceRangeMin}
              onChange={(e) => setPriceRangeMin(e.target.value)}
            />
          </div>
          <div className='flex-1'>
            <label className="text-sm font-medium text-gray-700 mb-1 block">
              Max Price (Ksh)
            </label>
            <Input
              type="number"
              step="0.01"
              placeholder="999.99"
              value={priceRangeMax}
              onChange={(e) => setPriceRangeMax(e.target.value)}
            />
          </div>
          <div className="">
            <label className="text-sm font-medium text-gray-700 mb-1 block">
              Action
            </label>
            <Button
              variant="outline"
              onClick={clearFilters}
              className={`flex items-center gap-2 py-[19px] ${!hasActiveFilters as boolean ? 'cursor-not-allowed' : 'cursor-pointer'}`}
            >
              <X className="h-4 w-4" />
              Clear
            </Button>
          </div>
        </div>
      </div>

      <div>
        {filteredAndSortedItems.length === 0 ? (
          <div className="flex flex-col items-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {hasActiveFilters ? 'No products found' : 'No products yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {hasActiveFilters
                ? 'Try adjusting your search terms or filters'
                : 'Get started by creating your first product'
              }
            </p>
            {!hasActiveFilters && (
              <Button onClick={onAdd} className="flex items-center gap-2 w-fit">
                <Plus className="h-4 w-4" />
                Add First Product
              </Button>
            )}
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className=''>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('name')}
                      className="h-auto !p-0 font-semibold hover:bg-transparent !px-0"
                    >
                      Name {getSortIcon('name')}
                    </Button>
                  </TableHead>
                  <TableHead className='text-center'>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('code')}
                      className="h-auto !p-0 font-semibold hover:bg-transparent !px-0"
                    >
                      Code {getSortIcon('code')}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('unit_price')}
                      className="h-auto !p-0 font-semibold hover:bg-transparent !px-0"
                    >
                      Price {getSortIcon('unit_price')}
                    </Button>
                  </TableHead>
                  <TableHead className='text-center'>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('sub_category')}
                      className="h-auto !p-0 font-semibold hover:bg-transparent !px-0"
                    >
                      Sub Category {getSortIcon('sub_category')}
                    </Button>
                  </TableHead>
                  <TableHead className='text-center font-bold'>Status</TableHead>
                  <TableHead className='text-center font-bold'>Type</TableHead>
                  <TableHead className="text-right font-bold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedItems.map((item: ProductItem) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">
                      <div>
                        <div className="font-medium">{item.name}</div>
                        {item.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {item.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className='text-center'>
                      <Badge variant="outline" className="font-mono pt-1.5 pb-0 px-3">
                        {item.code}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      Ksh {parseFloat(item.unit_price).toFixed(2)}
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="secondary">
                        {getSubCategoryName(item.sub_category)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant={item.is_active ? "default" : "secondary"}>
                        {item.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col items-center gap-1">
                        <Badge variant={item.perishable ? "destructive" : "outline"} className="w-fit">
                          {item.perishable ? 'Perishable' : 'Non-Perishable'}
                        </Badge>
                        {item.perishable && item.shelf_life_days && (
                          <span className="text-xs text-gray-500">
                            Shelf Life ({item.shelf_life_days} days)
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit(item)}
                          className="h-8 w-8 p-0"
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item.id)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
};



{/* <div className="flex flex-wrap gap-3 mt-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-500">Quick Filters:</span>
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
          <Select value={perishableFilter} onValueChange={setPerishableFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="perishable">Perishable</SelectItem>
              <SelectItem value="non-perishable">Non-Perishable</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Advanced Filters
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 text-xs">
                !
              </Badge>
            )}
          </Button>
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Clear All
            </Button>
          )}
        </div> */}