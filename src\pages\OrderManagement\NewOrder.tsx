// src/pages/OrderManagement/NewOrder.tsx
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import BaseModal from "@/components/custom/modals/BaseModal";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { 
  Users, MapPin, DollarSign, User, Monitor, Building, FileText, 
  Utensils, Car, Truck, Calendar, Package, Plus, Minus, ShoppingCart, X, AlertCircle, Loader2 
} from "lucide-react";
import { useGetModifierMenusQuery } from "@/redux/slices/menuitemmodifier";
import { useGetComboMenusQuery } from "@/redux/slices/comboMenu";
import { useGetMenusQuery } from "@/redux/slices/menuMake";
import { useGetTablesQuery } from "@/redux/slices/tables";
import { useGetUsersQuery } from "@/redux/slices/users";
import { useGetWorkstationsQuery } from "@/redux/slices/workstations";
import { useGetRevenueCentersQuery } from "@/redux/slices/revenueCenters";
import { useAddOrderMutation } from "@/redux/slices/order";

// Types
interface User { id: number; employee_no: string | number; first_name: string; last_name: string; email: string; username: string; }
interface Workstation { id: number; Workstation_code: string; name: string; is_active: boolean; }
interface Table { id: number; table_number: string; capacity: number; is_active: boolean; }
interface MenuItem { id: number; name: string; price: string; description: string; stock_status: boolean; is_active: boolean; pos_name: string; is_combo?: boolean; }
interface Modifier { id: number; name: string; price: string; description: string; menu_item: number; modifier_group: number; }
interface RevenueCenter { id: number; name: string; is_active: boolean; }

interface OrderItemPayload {
  quantity: number;
  unit_price: string;
  modifiers: Record<string, { name: string; price: string; quantity: number }>;
  special_instructions: string;
  menu_item: number;
}

interface OrderLineItem extends OrderItemPayload {
  order: number;
  menu_item_details?: MenuItem;
}

interface ExtendedCheckInForm {
  orderNumber: string;
  order_type: "Dine In" | "Takeaway" | "Delivery" | "Reservation" | "Event" | "Other";
  status: "Open" | "In Progress" | "Completed" | "Cancelled" | "Refunded";
  total_amount: string;
  payment_status: boolean;
  guest_count: number;
  tax_amount: string;
  service_charge: string;
  catering_levy: string;
  revenue_center: number;
  workstation: number;
  table_number: number | null;
  created_by: string | null;
  specialRequests: string;
  orderItems: OrderLineItem[];
  error?: string;
}

interface CheckInModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  checkInForm: ExtendedCheckInForm;
  setCheckInForm: React.Dispatch<React.SetStateAction<ExtendedCheckInForm>>;
  onSuccess?: () => void;
}

// Constants
const orderTypeConfig = {
  "Dine In": { icon: Utensils, color: "bg-primary" },
  Takeaway: { icon: Car, color: "bg-primary" },
  Delivery: { icon: Truck, color: "bg-primary" },
  Reservation: { icon: Calendar, color: "bg-primary" },
  Event: { icon: Users, color: "bg-primary" },
  Other: { icon: Package, color: "bg-primary" },
};

const statusConfig = {
  Open: "bg-blue-500",
  "In Progress": "bg-yellow-500",
  Completed: "bg-green-500",
  Cancelled: "bg-red-500",
  Refunded: "bg-gray-500",
};

// Utility functions
const generateOrderNumber = () => `ORD-${Date.now().toString().slice(-6)}-${Math.floor(Math.random() * 1000).toString().padStart(3, "0")}`;

const extractArray = (data: any, name: string) => {
  if (!data) {
    console.warn(`No ${name} data provided`);
    return [];
  }
  if (Array.isArray(data)) return data;
  const possibleKeys = ['results', 'data', 'items', 'list', 'data.results', 'data.items', 'data.list'];
  for (const key of possibleKeys) {
    const value = key.includes('.') ? key.split('.').reduce((obj, k) => obj?.[k], data) : data[key];
    if (Array.isArray(value)) return value;
  }
  console.warn(`No array found in ${name} data:`, data);
  return [];
};

const isValidModifier = (modifier: any): modifier is Modifier => {
  return (
    typeof modifier.id === 'number' &&
    typeof modifier.name === 'string' && modifier.name.trim() !== '' &&
    typeof modifier.price === 'string' && !isNaN(parseFloat(modifier.price)) && parseFloat(modifier.price) >= 0 &&
    typeof modifier.menu_item === 'number' &&
    typeof modifier.modifier_group === 'number'
  );
};

// Component
export const CheckInModal: React.FC<CheckInModalProps> = ({
  open, onOpenChange, checkInForm, setCheckInForm, onSuccess
}) => {
  // API hooks
  const { data: modifierMenuData, isLoading: loadingModifierMenu, error: modifierMenuError } = useGetModifierMenusQuery({});
  const { data: comboMenusData, isLoading: loadingComboMenus, error: comboMenusError } = useGetComboMenusQuery({});
  const { data: menuItemsData, isLoading: loadingMenuItems, error: menuItemsError } = useGetMenusQuery({});
  const { data: tablesData, isLoading: loadingTables, error: tablesError } = useGetTablesQuery({});
  const { data: usersData, isLoading: loadingUsers, error: usersError } = useGetUsersQuery({});
  const { data: workstationsData, isLoading: loadingWorkstations, error: workstationsError } = useGetWorkstationsQuery({});
  const { data: revenueCentersData, isLoading: loadingRevenueCenters, error: revenueCentersError } = useGetRevenueCentersQuery({});
  const [addOrder, { isLoading: isSubmittingOrder }] = useAddOrderMutation();

  // Local state
  const [selectedMenuItem, setSelectedMenuItem] = React.useState<MenuItem | null>(null);
  const [menuItemQuantity, setMenuItemQuantity] = React.useState(1);
  const [menuItemInstructions, setMenuItemInstructions] = React.useState("");
  const [selectedModifiers, setSelectedModifiers] = React.useState<Record<string, { name: string; price: string; quantity: number }>>({});
  const [showMenuItems, setShowMenuItems] = React.useState(false);
  const [showComboMenus, setShowComboMenus] = React.useState(false);
  const [modifierError, setModifierError] = React.useState<string | null>(null);

  // Debug API responses
  React.useEffect(() => {
    console.log("Menu Items:", { data: menuItemsData, error: menuItemsError, loading: loadingMenuItems });
    console.log("Combo Menus:", { data: comboMenusData, error: comboMenusError, loading: loadingComboMenus });
    console.log("Modifier Menus:", { data: modifierMenuData, error: modifierMenuError, loading: loadingModifierMenu });
    console.log("Tables:", { data: tablesData, error: tablesError, loading: loadingTables });
    console.log("Users:", { data: usersData, error: usersError, loading: loadingUsers });
  }, [menuItemsData, comboMenusData, modifierMenuData, tablesData, usersData, menuItemsError, comboMenusError, modifierMenuError, tablesError, usersError, loadingMenuItems, loadingComboMenus, loadingModifierMenu, loadingTables, loadingUsers]);

  // Memoized options
  const userOptions = React.useMemo(() => {
    const users = extractArray(usersData, "users");
    console.log("Raw Users:", users);
    return users
      .filter((user: User) => user.employee_no && user.first_name && user.last_name)
      .map((user: User) => ({
        value: user.employee_no.toString(),
        label: `${user.first_name} ${user.last_name}`,
      }));
  }, [usersData]);

  const tableOptions = React.useMemo(() => {
    const tables = extractArray(tablesData, "tables");
    console.log("Raw Tables:", tables);
    return tables
      .filter((table: Table) => table.is_active)
      .map((table: Table) => ({
        value: table.id.toString(),
        label: `Table ${table.table_number} (Capacity: ${table.capacity})`,
      }))
      .sort((a, b) => parseInt(a.label) - parseInt(b.label));
  }, [tablesData]);

  const workstations = React.useMemo(() => {
    const ws = extractArray(workstationsData, "workstations");
    console.log("Raw Workstations:", ws);
    return ws.filter((w: Workstation) => w.is_active);
  }, [workstationsData]);

  const revenueCenters = React.useMemo(() => {
    const rc = extractArray(revenueCentersData, "revenueCenters");
    console.log("Raw Revenue Centers:", rc);
    return rc.filter((rc: RevenueCenter) => rc.is_active);
  }, [revenueCentersData]);

  const regularItems = React.useMemo(() => {
    const items = extractArray(menuItemsData, "menuItems");
    console.log("Raw Menu Items:", items);
    return items
      .filter((item: MenuItem) => item.is_active && item.stock_status)
      .map((item: MenuItem) => ({ ...item, is_combo: false }));
  }, [menuItemsData]);

  const comboItems = React.useMemo(() => {
    const items = extractArray(comboMenusData, "comboMenus");
    console.log("Raw Combo Menus:", items);
    return items.map((item: any) => ({
      id: item.id,
      name: item.name,
      price: item.price,
      description: item.description,
      stock_status: true,
      is_active: true,
      pos_name: item.code || item.name,
      is_combo: true,
    }));
  }, [comboMenusData]);

  const modifiers = React.useMemo(() => {
    const items = extractArray(modifierMenuData, "modifierMenus");
    console.log("Raw Modifiers:", items);
    return items
      .filter(isValidModifier)
      .map((item: Modifier) => ({
        id: item.id,
        name: item.name,
        price: item.price,
        description: "", 
        menu_item: item.menu_item,
        modifier_group: item.modifier_group,
      }));
  }, [modifierMenuData]);

  const filteredModifiers = React.useMemo(() => {
    if (!selectedMenuItem) return [];
    return modifiers.filter(mod => mod.menu_item === selectedMenuItem.id);
  }, [modifiers, selectedMenuItem]);

  // Effects
  React.useEffect(() => {
    if (open && !checkInForm.orderNumber) {
      setCheckInForm({ ...checkInForm, orderNumber: generateOrderNumber(), orderItems: [], error: undefined });
    }
  }, [open, checkInForm.orderNumber, setCheckInForm]);

  React.useEffect(() => {
    const subtotal = checkInForm.orderItems.reduce((sum, item) => {
      const itemTotal = parseFloat(item.unit_price) * item.quantity;
      const modifierTotal = Object.values(item.modifiers).reduce(
        (modSum, mod) => modSum + parseFloat(mod.price) * mod.quantity,
        0
      );
      return sum + itemTotal + modifierTotal;
    }, 0);
    const taxAmount = subtotal * 0.16;
    const serviceCharge = subtotal * 0.10;
    const cateringLevy = subtotal * 0.015;
    setCheckInForm(prev => ({
      ...prev,
      total_amount: (subtotal + taxAmount + serviceCharge + cateringLevy).toFixed(2),
      tax_amount: taxAmount.toFixed(2),
      service_charge: serviceCharge.toFixed(2),
      catering_levy: cateringLevy.toFixed(2),
    }));
  }, [checkInForm.orderItems, setCheckInForm]);

  // Handlers
  const updateForm = <K extends keyof ExtendedCheckInForm>(key: K, value: ExtendedCheckInForm[K]) => 
    setCheckInForm(prev => ({ ...prev, [key]: value }));

  const handleAddMenuItem = () => {
    if (!selectedMenuItem) return;
    
    // Validate modifiers
    const invalidModifiers = Object.entries(selectedModifiers).filter(
      ([, mod]) => !mod.name.trim() || isNaN(parseFloat(mod.price)) || mod.quantity > 4294967295
    );
    if (invalidModifiers.length > 0) {
      setModifierError("Invalid modifiers detected. Ensure all additives have a valid name, price, and quantity.");
      return;
    }
    if (menuItemQuantity > 4294967295) {
      setModifierError("Item quantity exceeds maximum limit of 4294967295.");
      return;
    }
    
    // Validate selected modifiers are valid for this menu item
    const invalidMenuItemModifiers = Object.keys(selectedModifiers).filter(
      modId => !filteredModifiers.some(mod => mod.id.toString() === modId)
    );
    if (invalidMenuItemModifiers.length > 0) {
      setModifierError("Selected additives are not applicable to this menu item.");
      return;
    }
    
    updateForm("orderItems", [...checkInForm.orderItems, {
      quantity: menuItemQuantity,
      unit_price: selectedMenuItem.price,
      modifiers: selectedModifiers,
      special_instructions: menuItemInstructions,
      order: 0, // This will be set after order creation
      menu_item: selectedMenuItem.id,
      menu_item_details: selectedMenuItem,
    }]);
    
    // Reset form
    setSelectedMenuItem(null);
    setMenuItemQuantity(1);
    setMenuItemInstructions("");
    setSelectedModifiers({});
    setShowMenuItems(false);
    setShowComboMenus(false);
    setModifierError(null);
  };

  const handleRemoveMenuItem = (index: number) => 
    updateForm("orderItems", checkInForm.orderItems.filter((_, i) => i !== index));

  const handleUpdateItemQuantity = (index: number, quantity: number) => {
    if (quantity < 1 || quantity > 4294967295) return;
    updateForm("orderItems", checkInForm.orderItems.map((item, i) => 
      i === index ? { ...item, quantity } : item));
  };

  const handleModifierToggle = (modifier: Modifier, quantity: number) => {
    if (quantity > 4294967295) {
      setModifierError(`Quantity for ${modifier.name} exceeds maximum limit of 4294967295.`);
      return;
    }
    setSelectedModifiers(prev => {
      if (quantity <= 0) {
        const { [modifier.id]: _, ...rest } = prev;
        return rest;
      }
      return {
        ...prev,
        [modifier.id]: { name: modifier.name, price: modifier.price, quantity },
      };
    });
    setModifierError(null);
  };

  const handleCheckIn = async () => {
    try {
      // Prepare order items payload
      const orderItemsPayload: OrderItemPayload[] = checkInForm.orderItems.map(item => ({
        quantity: item.quantity,
        unit_price: item.unit_price,
        modifiers: item.modifiers || {},
        special_instructions: item.special_instructions || "",
        menu_item: item.menu_item,
      }));

      // Create order with items included
      const orderPayload = {
        order_type: checkInForm.order_type,
        status: checkInForm.status,
        total_amount: checkInForm.total_amount,
        payment_status: checkInForm.payment_status,
        guest_count: checkInForm.guest_count,
        tax_amount: checkInForm.tax_amount,
        service_charge: checkInForm.service_charge,
        catering_levy: checkInForm.catering_levy,
        revenue_center: checkInForm.revenue_center,
        workstation: checkInForm.workstation,
        table_number: checkInForm.order_type === "Dine In" ? checkInForm.table_number : null,
        created_by: checkInForm.created_by || "unknown",
        order_items: orderItemsPayload, // Include order items in the main payload
      };
      
      console.log("Complete Order Payload:", orderPayload);
      
      const orderResponse = await addOrder(orderPayload).unwrap();
      console.log("Order created successfully:", orderResponse);

      onSuccess?.();
      onOpenChange(false);
      setCheckInForm(prev => ({ ...prev, error: undefined }));
    } catch (error: any) {
      console.error("Check-in failed:", error);
      const errorMessage = error?.data?.error || error?.message || "Failed to create order. Please check your inputs and try again.";
      setCheckInForm(prev => ({ ...prev, error: errorMessage }));
    }
  };

  const isFormValid = () => {
    return (
      checkInForm.order_type && 
      checkInForm.status && 
      checkInForm.guest_count > 0 && 
      checkInForm.guest_count <= 4294967295 && 
      checkInForm.created_by && 
      checkInForm.revenue_center && 
      checkInForm.workstation > 0 && 
      (checkInForm.order_type !== "Dine In" || checkInForm.table_number) && 
      checkInForm.orderItems.length > 0
    );
  };

  // UI Components
  const FormSection = ({ title, icon: Icon, children }: { title: string; icon: any; children: React.ReactNode }) => (
    <Card className="border-0 shadow-sm">
      <CardHeader className="flex flex-row items-center gap-2 p-4">
        <Icon className="w-5 h-5 text-primary" />
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 p-4">{children}</CardContent>
    </Card>
  );

  const FormField = ({ label, icon: Icon, children, required }: { label: string; icon?: any; children: React.ReactNode; required?: boolean }) => (
    <div className="space-y-1">
      <Label className="flex items-center gap-1">
        {Icon && <Icon className="w-4 h-4 text-muted-foreground" />}
        {label}
        {required && <span className="text-destructive">*</span>}
      </Label>
      {children}
    </div>
  );

  const OrderTypeCard = ({ type, isSelected, onClick }: { type: ExtendedCheckInForm["order_type"]; isSelected: boolean; onClick: () => void }) => {
    const { icon: Icon, color } = orderTypeConfig[type];
    return (
      <Button
        variant={isSelected ? "default" : "outline"}
        className={cn("flex-col h-auto p-2", isSelected && color)}
        onClick={onClick}
      >
        <Icon className="w-4 h-4" />
        <span className="text-xs">{type}</span>
      </Button>
    );
  };

  const StatusBadge = ({ status, isSelected, onClick }: { status: ExtendedCheckInForm["status"]; isSelected: boolean; onClick: () => void }) => (
    <Badge
      variant={isSelected ? "default" : "outline"}
      className={cn("cursor-pointer", isSelected && statusConfig[status])}
      onClick={onClick}
    >
      {status}
    </Badge>
  );

  const MenuItemCard = ({ item, onSelect }: { item: MenuItem; onSelect: () => void }) => (
    <Card className="cursor-pointer hover:border-primary mb-2" onClick={onSelect}>
      <CardContent className="p-3">
        <div className="flex justify-between">
          <h4 className="font-medium">{item.name}</h4>
          <span className="font-bold text-primary">KSh {item.price}</span>
        </div>
        <p className="text-xs text-muted-foreground">{item.description}</p>
        <Badge variant={item.is_combo ? "secondary" : item.stock_status ? "default" : "destructive"} className="mt-2">
          {item.is_combo ? "Combo" : item.stock_status ? "Available" : "Out of Stock"}
        </Badge>
      </CardContent>
    </Card>
  );

  const ModifierCard = ({ modifier, selectedQuantity, onToggle }: { modifier: Modifier; selectedQuantity: number; onToggle: (quantity: number) => void }) => (
    <Card className="cursor-pointer hover:border-primary mb-2">
      <CardContent className="p-3 flex justify-between items-center">
        <div>
          <h4 className="font-medium">{modifier.name}</h4>
          <span className="text-sm font-bold text-primary">KSh {modifier.price}</span>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => onToggle(Math.max(0, selectedQuantity - 1))} disabled={selectedQuantity <= 0}>
            <Minus className="w-3 h-3" />
          </Button>
          <span className="min-w-[2rem] text-center">{selectedQuantity}</span>
          <Button variant="outline" size="sm" onClick={() => onToggle(selectedQuantity + 1)}>
            <Plus className="w-3 h-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const OrderItemCard = ({ item, index }: { item: OrderLineItem; index: number }) => (
    <Card className="mb-2">
      <CardContent className="p-3">
        <div className="flex justify-between">
          <div>
            <h4 className="font-medium">{item.menu_item_details?.name || "Unknown Item"}</h4>
            {item.special_instructions && (
              <p className="text-xs text-blue-600">Note: {item.special_instructions}</p>
            )}
            {Object.values(item.modifiers).length > 0 && (
              <div className="text-xs text-muted-foreground">
                Modifiers: {Object.values(item.modifiers).map(mod => `${mod.name} (x${mod.quantity})`).join(", ")}
              </div>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={() => handleRemoveMenuItem(index)} className="text-destructive">
            <X className="w-4 h-4" />
          </Button>
        </div>
        <div className="flex justify-between mt-2">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => handleUpdateItemQuantity(index, item.quantity - 1)} disabled={item.quantity <= 1}>
              <Minus className="w-3 h-3" />
            </Button>
            <span className="min-w-[2rem] text-center">{item.quantity}</span>
            <Button variant="outline" size="sm" onClick={() => handleUpdateItemQuantity(index, item.quantity + 1)}>
              <Plus className="w-3 h-3" />
            </Button>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold text-primary">
              KSh {(
                (parseFloat(item.unit_price) * item.quantity) + 
                Object.values(item.modifiers).reduce((sum, mod) => sum + parseFloat(mod.price) * mod.quantity, 0)
              ).toFixed(2)}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Create New Order"
      className="max-w-6xl"
      size="full"
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Order Info */}
        <div className="space-y-4">
          <FormSection title="Order Info" icon={FileText}>
            <FormField label="Order Number">
              <Input value={checkInForm.orderNumber} readOnly className="font-mono" />
            </FormField>
            <FormField label="Order Type" required>
              <div className="grid grid-cols-3 gap-2">
                {Object.keys(orderTypeConfig).map(type => (
                  <OrderTypeCard
                    key={type}
                    type={type as ExtendedCheckInForm["order_type"]}
                    isSelected={checkInForm.order_type === type}
                    onClick={() => updateForm("order_type", type as ExtendedCheckInForm["order_type"])}
                  />
                ))}
              </div>
            </FormField>
            <FormField label="Status" required>
              <div className="flex flex-wrap gap-2">
                {Object.keys(statusConfig).map(status => (
                  <StatusBadge
                    key={status}
                    status={status as ExtendedCheckInForm["status"]}
                    isSelected={checkInForm.status === status}
                    onClick={() => updateForm("status", status as ExtendedCheckInForm["status"])}
                  />
                ))}
              </div>
            </FormField>
          </FormSection>

          <AnimatePresence>
            {checkInForm.order_type === "Dine In" && (
              <motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: "auto" }} exit={{ opacity: 0, height: 0 }}>
                <FormSection title="Table" icon={MapPin}>
                  <FormField label="Table Number" required>
                    <Select value={checkInForm.table_number?.toString() || ""} onValueChange={v => updateForm("table_number", v ? parseInt(v) : null)}>
                      <SelectTrigger><SelectValue placeholder="Choose a table" /></SelectTrigger>
                      <SelectContent>
                        {loadingTables ? <SelectItem value="loading" disabled>Loading...</SelectItem> : 
                         tablesError ? <SelectItem value="error" disabled>Error loading tables</SelectItem> : 
                         tableOptions.length ? tableOptions.map(table => (
                          <SelectItem key={table.value} value={table.value}>{table.label}</SelectItem>
                         )) : <SelectItem value="no-tables" disabled>No tables available</SelectItem>}
                      </SelectContent>
                    </Select>
                  </FormField>
                </FormSection>
              </motion.div>
            )}
          </AnimatePresence>

          <FormSection title="Guests & Staff" icon={Users}>
            <FormField label="Guests" required>
              <Input
                type="number"
                value={checkInForm.guest_count}
                onChange={e => updateForm("guest_count", parseInt(e.target.value) || 0)}
                min="0"
                max="4294967295"
              />
            </FormField>
            <FormField label="Staff" required>
              <Select value={checkInForm.created_by || ""} onValueChange={v => updateForm("created_by", v || null)}>
                <SelectTrigger><SelectValue placeholder="Select staff" /></SelectTrigger>
                <SelectContent>
                  {loadingUsers ? <SelectItem value="loading" disabled>Loading...</SelectItem> : 
                   usersError ? <SelectItem value="error" disabled>Error loading staff</SelectItem> : 
                   userOptions.length ? userOptions.map(user => (
                    <SelectItem key={user.value} value={user.value}>{user.label}</SelectItem>
                   )) : <SelectItem value="no-users" disabled>No staff available</SelectItem>}
                </SelectContent>
              </Select>
            </FormField>
          </FormSection>
        </div>

        {/* Menu Items and Combo Menus */}
        <div className="space-y-4">
          <FormSection title="Menu Items" icon={ShoppingCart}>
            <Button onClick={() => setShowMenuItems(!showMenuItems)} className="w-full">
              <Plus className="w-4 h-4 mr-2" /> Add Regular Item
            </Button>
            <AnimatePresence>
              {showMenuItems && (
                <motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: "auto" }} exit={{ opacity: 0, height: 0 }}>
                  <ScrollArea className="h-48 rounded-md border p-2">
                    {loadingMenuItems ? (
                      <div className="flex items-center justify-center h-40 gap-2">
                        <Loader2 className="w-4 h-4 animate-spin" /> Loading...
                      </div>
                    ) : menuItemsError ? (
                      <Alert variant="destructive">
                        <AlertCircle className="w-4 h-4" />
                        <AlertDescription>Failed to load menu: {JSON.stringify(menuItemsError)}</AlertDescription>
                      </Alert>
                    ) : regularItems.length ? (
                      regularItems.map((item: MenuItem) => (
                        <MenuItemCard key={item.id} item={item} onSelect={() => setSelectedMenuItem(item)} />
                      ))
                    ) : (
                      <div className="flex items-center justify-center h-40 text-muted-foreground">No regular items available</div>
                    )}
                  </ScrollArea>
                </motion.div>
              )}
            </AnimatePresence>
          </FormSection>

          <FormSection title="Combo Menus" icon={Package}>
            <Button onClick={() => setShowComboMenus(!showComboMenus)} className="w-full">
              <Plus className="w-4 h-4 mr-2" /> Add Combo Item
            </Button>
            <AnimatePresence>
              {showComboMenus && (
                <motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: "auto" }} exit={{ opacity: 0, height: 0 }}>
                  <ScrollArea className="h-48 rounded-md border p-2">
                    {loadingComboMenus ? (
                      <div className="flex items-center justify-center h-40 gap-2">
                        <Loader2 className="w-4 h-4 animate-spin" /> Loading...
                      </div>
                    ) : comboMenusError ? (
                      <Alert variant="destructive">
                        <AlertCircle className="w-4 h-4" />
                        <AlertDescription>Failed to load combo menus: {JSON.stringify(comboMenusError)}</AlertDescription>
                      </Alert>
                    ) : comboItems.length ? (
                      comboItems.map((item: MenuItem) => (
                        <MenuItemCard key={item.id} item={item} onSelect={() => setSelectedMenuItem(item)} />
                      ))
                    ) : (
                      <div className="flex items-center justify-center h-40 text-muted-foreground">No combo menus available</div>
                    )}
                  </ScrollArea>
                </motion.div>
              )}
            </AnimatePresence>
          </FormSection>

          {selectedMenuItem && (
            <FormSection title="Add Item" icon={Plus}>
              <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="p-3 bg-secondary/50 rounded-lg border">
                <h4 className="font-medium mb-2">Adding: {selectedMenuItem.name} {selectedMenuItem.is_combo ? "(Combo)" : ""}</h4>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Label>Quantity:</Label>
                    <Button variant="outline" size="sm" onClick={() => setMenuItemQuantity(Math.max(1, menuItemQuantity - 1))}>
                      <Minus className="w-3 h-3" />
                    </Button>
                    <Input
                      type="number"
                      value={menuItemQuantity}
                      onChange={e => setMenuItemQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                      className="w-16 text-center"
                      min="1"
                      max="4294967295"
                    />
                    <Button variant="outline" size="sm" onClick={() => setMenuItemQuantity(menuItemQuantity + 1)}>
                      <Plus className="w-3 h-3" />
                    </Button>
                  </div>
                  <Input
                    placeholder="Special instructions..."
                    value={menuItemInstructions}
                    onChange={e => setMenuItemInstructions(e.target.value)}
                  />
                  <div className="space-y-2">
                    <Label>Additives</Label>
                    {modifierError && (
                      <Alert variant="destructive">
                        <AlertCircle className="w-4 h-4" />
                        <AlertDescription>{modifierError}</AlertDescription>
                      </Alert>
                    )}
                    <ScrollArea className="h-24 rounded-md border p-2">
                      {loadingModifierMenu ? (
                        <div className="flex items-center justify-center h-20 gap-2">
                          <Loader2 className="w-4 h-4 animate-spin" /> Loading...
                        </div>
                      ) : modifierMenuError ? (
                        <Alert variant="destructive">
                          <AlertCircle className="w-4 h-4" />
                          <AlertDescription>Failed to load additives: {JSON.stringify(modifierMenuError)}</AlertDescription>
                        </Alert>
                      ) : filteredModifiers.length ? (
                        filteredModifiers.map((modifier: Modifier) => (
                          <ModifierCard
                            key={modifier.id}
                            modifier={modifier}
                            selectedQuantity={selectedModifiers[modifier.id]?.quantity || 0}
                            onToggle={(quantity) => handleModifierToggle(modifier, quantity)}
                          />
                        ))
                      ) : (
                        <div className="flex items-center justify-center h-20 text-muted-foreground">
                          No additives available for this menu item
                        </div>
                      )}
                    </ScrollArea>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddMenuItem} size="sm" disabled={!!modifierError}>Add</Button>
                    <Button onClick={() => { setSelectedMenuItem(null); setSelectedModifiers({}); setModifierError(null); }} variant="outline" size="sm">Cancel</Button>
                  </div>
                </div>
              </motion.div>
            </FormSection>
          )}

          {checkInForm.orderItems.length > 0 && (
            <FormSection title="Order Items" icon={Package}>
              <ScrollArea className="h-64 rounded-md p-2">
                {checkInForm.orderItems.map((item, index) => (
                  <OrderItemCard key={index} item={item} index={index} />
                ))}
              </ScrollArea>
            </FormSection>
          )}
        </div>

        {/* Financial & System Details */}
        <div className="space-y-4">
          <FormSection title="Financials" icon={DollarSign}>
            {[
              { label: "Total Amount", key: "total_amount" },
              { label: "Tax (16%)", key: "tax_amount" },
              { label: "Service Charge (10%)", key: "service_charge" },
              { label: "Catering Levy (1.5%)", key: "catering_levy" },
            ].map(({ label, key }) => (
              <FormField key={key} label={label} required>
                <Input
                  type="number"
                  step="0.01"
                  value={checkInForm[key as keyof ExtendedCheckInForm] as string}
                  onChange={e => updateForm(key as keyof ExtendedCheckInForm, e.target.value)}
                  readOnly={key !== "total_amount"}
                />
              </FormField>
            ))}
            {checkInForm.orderItems.length > 0 && (
              <Card className="mt-2">
                <CardHeader className="p-3"><CardTitle className="text-sm">Summary</CardTitle></CardHeader>
                <CardContent className="space-y-1 text-sm p-3">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>KSh {checkInForm.orderItems.reduce((sum, item) => {
                      const itemTotal = parseFloat(item.unit_price) * item.quantity;
                      const modifierTotal = Object.values(item.modifiers).reduce(
                        (modSum, mod) => modSum + parseFloat(mod.price) * mod.quantity,
                        0
                      );
                      return sum + itemTotal + modifierTotal;
                    }, 0).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-muted-foreground">
                    <span>Total:</span>
                    <span>KSh {checkInForm.total_amount}</span>
                  </div>
                </CardContent>
              </Card>
            )}
          </FormSection>

          <FormSection title="System" icon={Monitor}>
            <FormField label="Revenue Center" required>
              <Select value={checkInForm.revenue_center.toString()} onValueChange={v => updateForm("revenue_center", parseInt(v) || 0)}>
                <SelectTrigger><SelectValue placeholder="Select revenue center" /></SelectTrigger>
                <SelectContent>
                  {loadingRevenueCenters ? <SelectItem value="loading" disabled>Loading...</SelectItem> : 
                   revenueCentersError ? <SelectItem value="error" disabled>Error loading</SelectItem> : 
                   revenueCenters.length ? revenueCenters.map((rc: RevenueCenter) => (
                    <SelectItem key={rc.id} value={rc.id.toString()}>{rc.name}</SelectItem>
                   )) : <SelectItem value="no-rc" disabled>No revenue centers</SelectItem>}
                </SelectContent>
              </Select>
            </FormField>
            <FormField label="Workstation" required>
              {loadingWorkstations ? (
                <div className="flex items-center gap-2 p-2 rounded-lg bg-secondary">
                  <Loader2 className="w-4 h-4 animate-spin" /> Loading...
                </div>
              ) : workstationsError ? (
                <Alert variant="destructive"><AlertCircle className="w-4 h-4" /><AlertDescription>Failed to load workstations</AlertDescription></Alert>
              ) : (
                <Select value={checkInForm.workstation.toString()} onValueChange={v => updateForm("workstation", parseInt(v) || 0)}>
                  <SelectTrigger><SelectValue placeholder="Select workstation" /></SelectTrigger>
                  <SelectContent>
                    {workstations.length ? workstations.map((ws: Workstation) => (
                      <SelectItem key={ws.id} value={ws.id.toString()}>
                        {ws.name} (Code: {ws.Workstation_code})
                      </SelectItem>
                    )) : <SelectItem value="no-ws" disabled>No workstations</SelectItem>}
                  </SelectContent>
                </Select>
              )}
            </FormField>
            <FormField label="Notes">
              <Input
                placeholder="Special requests..."
                value={checkInForm.specialRequests}
                onChange={e => updateForm("specialRequests", e.target.value)}
              />
            </FormField>
          </FormSection>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
        <Button onClick={handleCheckIn} disabled={!isFormValid() || isSubmittingOrder}>
          {isSubmittingOrder ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <ShoppingCart className="w-4 h-4 mr-2" />
              Create Order ({checkInForm.orderItems.length})
            </>
          )}
        </Button>
      </div>

      {/* Validation and Errors */}
      {(checkInForm.error || !isFormValid()) && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="w-4 h-4" />
          <AlertDescription>
            <ul className="list-disc pl-5">
              {checkInForm.error && <li>{checkInForm.error}</li>}
              {!isFormValid() && (
                <>
                  {!checkInForm.order_type && <li>Select order type</li>}
                  {!checkInForm.status && <li>Select status</li>}
                  {checkInForm.guest_count <= 0 && <li>Enter guest count</li>}
                  {!checkInForm.created_by && <li>Select staff</li>}
                  {checkInForm.order_type === "Dine In" && !checkInForm.table_number && <li>Select table</li>}
                  {!checkInForm.revenue_center && <li>Select revenue center</li>}
                  {checkInForm.workstation <= 0 && <li>Select workstation</li>}
                  {checkInForm.orderItems.length === 0 && <li>Add at least one item</li>}
                </>
              )}
            </ul>
          </AlertDescription>
        </Alert>
      )}
    </BaseModal>
  );
};