import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  ArrowLeft, 
  Edit, 
  History, 
  Clock, 
  ChefHat, 
  DollarSign,
  Users,
  AlertTriangle,
  Utensils,
  Image as ImageIcon,
  MapPin,
  Calendar,
  User,
  Shield,
  TrendingUp,
  Calculator,
  FileText,
  Package,
  Leaf,
  AlertCircle,
  CheckCircle,
  Info,
  Target,
  Timer
} from 'lucide-react';
import type { Recipe } from '@/types/recipe-type';
import { useGetRecipeIngredientQuery } from '@/redux/slices/recipe-ingredient';
import { useGetInventoryItemQuery } from '@/redux/slices/inventory';
import { useGetRecipeGroupQuery } from '@/redux/slices/recipe-group';
import { format } from 'date-fns';

interface RecipeViewProps {
  recipe: Recipe;
  onClose: () => void;
  onEdit: () => void;
  onViewHistory: () => void;
  readOnly?: boolean; // For access from different modules
  showEditButton?: boolean; // Control edit button visibility
}

export const RecipeView: React.FC<RecipeViewProps> = ({
  recipe,
  onClose,
  onEdit,
  onViewHistory,
  readOnly = false,
  showEditButton = true
}) => {
  const { data: ingredientsData } = useGetRecipeIngredientQuery({ params: { recipe: recipe.id } });
  const { data: inventoryData } = useGetInventoryItemQuery({ params: {} });
  const { data: groupsData } = useGetRecipeGroupQuery({ params: {} });

  const ingredients = ingredientsData?.results || [];
  const inventoryItems = inventoryData?.results || [];
  const groups = groupsData?.results || [];

  const recipeGroup = groups.find(g => g.id === recipe.recipe_group);
  
  // Calculate total cost from ingredients
  const totalIngredientCost = ingredients.reduce((sum, ingredient) => {
    return sum + (parseFloat(ingredient.total_cost || '0'));
  }, 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getInventoryItemName = (ingredientId: number) => {
    const item = inventoryItems.find(inv => inv.id === ingredientId);
    return item?.name || 'Unknown Item';
  };

  const getInventoryItemDetails = (ingredientId: number) => {
    const item = inventoryItems.find(inv => inv.id === ingredientId);
    return item || null;
  };

  // Enhanced cost calculations
  const laborCost = 0; // This would come from recipe data if available
  const packagingCost = 0; // This would come from recipe data if available
  const totalRecipeCost = totalIngredientCost + laborCost + packagingCost;
  const costPerPortion = totalRecipeCost / (parseFloat(recipe.portion_size) || 1);
  
  // Selling price recommendation (assuming 30% food cost target)
  const targetFoodCostPercentage = 30;
  const recommendedSellingPrice = costPerPortion / (targetFoodCostPercentage / 100);

  // Role badge color helper
  const getRoleBadgeColor = (role: string) => {
    switch (role?.toLowerCase()) {
      case 'chef': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'kitchen manager': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'sous chef': return 'bg-green-100 text-green-800 border-green-200';
      case 'admin': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Allergen detection from dietary flags
  const allergens = recipe.dietary_flags?.filter(flag => 
    flag.toLowerCase().includes('contains') || 
    flag.toLowerCase().includes('allergen')
  ) || [];
  
  const dietaryInfo = recipe.dietary_flags?.filter(flag => 
    !flag.toLowerCase().includes('contains') && 
    !flag.toLowerCase().includes('allergen')
  ) || [];

  // Format tools required into array
  const toolsArray = recipe.tools_required ? 
    recipe.tools_required.split(',').map(tool => tool.trim()) : [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onClose}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to List
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{recipe.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={recipe.recipe_type === 'Prep' ? 'secondary' : 'default'}>
                {recipe.recipe_type}
              </Badge>
              <Badge variant={recipe.is_active ? 'default' : 'destructive'}>
                {recipe.is_active ? 'Active' : 'Inactive'}
              </Badge>
              {recipeGroup && (
                <Badge variant="outline">{recipeGroup.name}</Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onViewHistory}>
            <History className="h-4 w-4 mr-2" />
            View History
          </Button>
          <Button onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Recipe
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recipe Image */}
          {recipe.image && (
            <Card>
              <CardContent className="p-6">
                <img 
                  src={recipe.image} 
                  alt={recipe.name}
                  className="w-full h-64 object-cover rounded-lg"
                />
              </CardContent>
            </Card>
          )}

          {/* Ingredients */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ChefHat className="h-5 w-5" />
                Ingredients
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ingredient</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit Cost</TableHead>
                    <TableHead>Total Cost</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {ingredients.map((ingredient) => (
                    <TableRow key={ingredient.id}>
                      <TableCell className="font-medium">
                        {getInventoryItemName(ingredient.id)}
                      </TableCell>
                      <TableCell>{ingredient.quantity}</TableCell>
                      <TableCell>{formatCurrency(parseFloat(ingredient.cost_per_unit || '0'))}</TableCell>
                      <TableCell>{formatCurrency(parseFloat(ingredient.total_cost || '0'))}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {ingredients.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No ingredients added yet
                </div>
              )}
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Preparation Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="whitespace-pre-wrap">{recipe.instructions}</p>
              </div>
              {recipe.tools_required && (
                <div className="mt-4">
                  <h4 className="font-semibold mb-2">Tools Required:</h4>
                  <p className="text-muted-foreground">{recipe.tools_required}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Dietary Information */}
          {recipe.dietary_flags && recipe.dietary_flags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Dietary Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {recipe.dietary_flags.map((flag, index) => (
                    <Badge key={index} variant="outline">
                      {flag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Recipe Details */}
          <Card>
            <CardHeader>
              <CardTitle>Recipe Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Portion Size: {recipe.portion_size}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Prep Time: {recipe.preparation_time} min</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Cook Time: {recipe.cooking_time} min</span>
              </div>
              {recipe.location && (
                <div className="text-sm">
                  <span className="font-medium">Location: </span>
                  {recipe.location}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Cost Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Cost Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Ingredient Cost:</span>
                <span className="font-medium">{formatCurrency(totalIngredientCost)}</span>
              </div>
              <div className="flex justify-between">
                <span>Cost per Portion:</span>
                <span className="font-medium">
                  {formatCurrency(totalIngredientCost / (parseFloat(recipe.portion_size) || 1))}
                </span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between font-semibold">
                  <span>Total Recipe Cost:</span>
                  <span>{formatCurrency(totalIngredientCost)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Menu Item Link */}
          {recipe.menu_item && (
            <Card>
              <CardHeader>
                <CardTitle>Linked Menu Item</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Menu Item ID: {recipe.menu_item}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};