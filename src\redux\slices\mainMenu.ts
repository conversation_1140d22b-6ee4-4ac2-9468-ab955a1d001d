import { apiSlice } from "../apiSlice";

export const mainMenuApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMainMenus: builder.query({
      query: (params) => ({
        url: "/menu/menus",
        method: "GET",
        params,
      }),
      providesTags: ["Menu"],
    }),

    retrieveMainMenu: builder.query({
      query: (id) => ({
        url: `/menu/menus/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Menu", id }],
    }),

    addMainMenu: builder.mutation({
      query: (payload) => ({
        url: "/menu/menus",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Menu"],
    }),

    patchMainMenu: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/menu/menus/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Menu", id },
        "Menu",
      ],
    }),

    deleteMainMenu: builder.mutation({
      query: (id) => ({
        url: `/menu/menus/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Menu"],
    }),
  }),
});

export const {
  useGetMainMenusQuery,
  useAddMainMenuMutation,
  usePatchMainMenuMutation,
  useDeleteMainMenuMutation,
  useLazyGetMainMenusQuery,
  useRetrieveMainMenuQuery, // Corrected to match singular endpoint
} = mainMenuApiSlice;