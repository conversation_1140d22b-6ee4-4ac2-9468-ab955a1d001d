import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Minus, ShoppingCart } from 'lucide-react';
import { MenuItem, GuestCheckItem, ItemModifier } from './types/types';
import { useGetMenusQuery } from '@/redux/slices/menuMake';

interface AddItemsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddItems: (items: GuestCheckItem[]) => void;
  checkId: string;
}

interface CartItem extends GuestCheckItem {
  tempId: string;
}

export const AddItemsDialog: React.FC<AddItemsDialogProps> = ({
  open,
  onOpenChange,
  onAddItems,
  checkId,
}) => {
  const { data: menuData, isLoading } = useGetMenusQuery({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [itemNotes, setItemNotes] = useState('');
  const [selectedModifiers, setSelectedModifiers] = useState<ItemModifier[]>([]);

  // Process menu data
  const menuItems = useMemo(() => {
    if (!menuData?.data?.results) return [];
    return menuData.data.results.map((item: any) => ({
      id: item.id.toString(),
      name: item.name || 'Unknown Item',
      price: parseFloat(item.price) || 0,
      category: item.category?.name || 'Other',
      description: item.description || '',
      available: item.is_active !== false,
      modifiers: item.modifiers || [],
    }));
  }, [menuData]);

  // Get unique categories
  const categories = useMemo(() => {
    const cats = ['all', ...new Set(menuItems.map(item => item.category))];
    return cats;
  }, [menuItems]);

  // Filter menu items
  const filteredItems = useMemo(() => {
    return menuItems.filter(item => {
      const matchesSearch = searchTerm === '' || 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
      return matchesSearch && matchesCategory && item.available;
    });
  }, [menuItems, searchTerm, selectedCategory]);

  const addToCart = (menuItem: MenuItem, quantity: number = 1) => {
    const cartItem: CartItem = {
      tempId: `temp-${Date.now()}-${Math.random()}`,
      id: menuItem.id,
      name: menuItem.name,
      price: menuItem.price,
      qty: quantity,
      category: menuItem.category,
      modifiers: selectedModifiers,
      notes: itemNotes.trim() || undefined,
    };

    setCart(prev => [...prev, cartItem]);
    setSelectedItem(null);
    setItemNotes('');
    setSelectedModifiers([]);
  };

  const updateCartItemQuantity = (tempId: string, newQty: number) => {
    if (newQty <= 0) {
      setCart(prev => prev.filter(item => item.tempId !== tempId));
    } else {
      setCart(prev => prev.map(item => 
        item.tempId === tempId ? { ...item, qty: newQty } : item
      ));
    }
  };

  const removeFromCart = (tempId: string) => {
    setCart(prev => prev.filter(item => item.tempId !== tempId));
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => {
      const modifiersTotal = item.modifiers?.reduce((sum, mod) => sum + mod.price, 0) || 0;
      return total + ((item.price + modifiersTotal) * item.qty);
    }, 0);
  };

  const handleAddItems = () => {
    if (cart.length === 0) return;
    
    const itemsToAdd: GuestCheckItem[] = cart.map(({ tempId, ...item }) => ({
      ...item,
      id: `${checkId}-${Date.now()}-${Math.random()}`,
    }));
    
    onAddItems(itemsToAdd);
    setCart([]);
    onOpenChange(false);
  };

  const formatKES = (value: number): string => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(value);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Add Items to Check</DialogTitle>
        </DialogHeader>

        <div className="flex gap-6 h-[70vh]">
          {/* Menu Items Section */}
          <div className="flex-1 flex flex-col">
            {/* Search and Filter */}
            <div className="space-y-4 mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search menu items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="capitalize"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>

            {/* Menu Items Grid */}
            <div className="flex-1 overflow-y-auto">
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <p>Loading menu items...</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredItems.map(item => (
                    <div
                      key={item.id}
                      className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => setSelectedItem(item)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-sm">{item.name}</h3>
                        <Badge variant="secondary" className="text-xs">
                          {item.category}
                        </Badge>
                      </div>
                      {item.description && (
                        <p className="text-xs text-gray-600 mb-2">{item.description}</p>
                      )}
                      <div className="flex justify-between items-center">
                        <span className="font-bold text-primary">{formatKES(item.price)}</span>
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            addToCart(item);
                          }}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Cart Section */}
          <div className="w-80 border-l pl-6 flex flex-col">
            <div className="flex items-center gap-2 mb-4">
              <ShoppingCart className="h-5 w-5" />
              <h3 className="font-semibold">Cart ({cart.length})</h3>
            </div>

            <div className="flex-1 overflow-y-auto space-y-3">
              {cart.map(item => (
                <div key={item.tempId} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{item.name}</h4>
                      {item.modifiers && item.modifiers.length > 0 && (
                        <div className="text-xs text-gray-600">
                          {item.modifiers.map(mod => `+ ${mod.name}`).join(', ')}
                        </div>
                      )}
                      {item.notes && (
                        <div className="text-xs text-gray-600 italic">Note: {item.notes}</div>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFromCart(item.tempId)}
                      className="text-red-500 hover:text-red-700"
                    >
                      ×
                    </Button>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => updateCartItemQuantity(item.tempId, item.qty - 1)}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                      <span className="w-8 text-center">{item.qty}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => updateCartItemQuantity(item.tempId, item.qty + 1)}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                    <span className="font-medium">
                      {formatKES((item.price + (item.modifiers?.reduce((sum, mod) => sum + mod.price, 0) || 0)) * item.qty)}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {cart.length > 0 && (
              <div className="border-t pt-4 mt-4">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-semibold">Total:</span>
                  <span className="font-bold text-lg">{formatKES(getCartTotal())}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Item Details Modal */}
        {selectedItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="font-bold text-lg mb-4">{selectedItem.name}</h3>
              
              {selectedItem.description && (
                <p className="text-gray-600 mb-4">{selectedItem.description}</p>
              )}

              {selectedItem.modifiers && selectedItem.modifiers.length > 0 && (
                <div className="mb-4">
                  <Label className="text-sm font-medium mb-2 block">Modifiers:</Label>
                  <div className="space-y-2">
                    {selectedItem.modifiers.map(modifier => (
                      <label key={modifier.id} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={selectedModifiers.some(m => m.id === modifier.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedModifiers(prev => [...prev, modifier]);
                            } else {
                              setSelectedModifiers(prev => prev.filter(m => m.id !== modifier.id));
                            }
                          }}
                        />
                        <span className="text-sm">{modifier.name} (+{formatKES(modifier.price)})</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              <div className="mb-4">
                <Label htmlFor="notes" className="text-sm font-medium mb-2 block">Special Notes:</Label>
                <Textarea
                  id="notes"
                  value={itemNotes}
                  onChange={(e) => setItemNotes(e.target.value)}
                  placeholder="Any special instructions..."
                  rows={3}
                />
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setSelectedItem(null)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => addToCart(selectedItem)}
                  className="flex-1"
                >
                  Add to Cart - {formatKES(selectedItem.price + selectedModifiers.reduce((sum, mod) => sum + mod.price, 0))}
                </Button>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddItems}
            disabled={cart.length === 0}
            className="bg-primary hover:bg-primary/90"
          >
            Add {cart.length} Items - {formatKES(getCartTotal())}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};