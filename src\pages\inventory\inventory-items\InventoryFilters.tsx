import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Filter, X } from 'lucide-react';

export interface FilterOptions {
  stockStatus: 'all' | 'in-stock' | 'low-stock' | 'out-of-stock';
  expiryStatus: 'all' | 'expiring-soon' | 'expired' | 'no-expiry';
  sortBy: 'name' | 'quantity' | 'expiry' | 'updated';
  sortOrder: 'asc' | 'desc';
}

interface InventoryFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  onClearFilters: () => void;
}

export const InventoryFilters: React.FC<InventoryFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
}) => {
  const hasActiveFilters =
    filters.stockStatus !== 'all' ||
    filters.expiryStatus !== 'all' ||
    filters.sortBy !== 'name' ||
    filters.sortOrder !== 'asc';

  const updateFilter = (key: keyof FilterOptions, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  return (
    <Card className='shadow-none'>
      <CardContent className="p-4">
        <div className="flex flex-col  gap-2 items-start">
          <div className="flex items-center gap-2 ml-1">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filters</span>
          </div>

          <div className="flex flex-col md:flex-row gap-4 w-full">
            <Select
              value={filters.stockStatus}
              onValueChange={(value) => updateFilter('stockStatus', value)}
            >
              <SelectTrigger className="focus:ring-0  cursor-pointer">
                <SelectValue placeholder="Stock Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Stock</SelectItem>
                <SelectItem value="in-stock">In Stock</SelectItem>
                <SelectItem value="low-stock">Low Stock</SelectItem>
                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.expiryStatus}
              onValueChange={(value) => updateFilter('expiryStatus', value)}
            >
              <SelectTrigger className="focus:ring-0  cursor-pointer">
                <SelectValue placeholder="Expiry Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Items</SelectItem>
                <SelectItem value="expiring-soon">Expiring Soon</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
                <SelectItem value="no-expiry">No Expiry</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.sortBy}
              onValueChange={(value) => updateFilter('sortBy', value)}
            >
              <SelectTrigger className="focus:ring-0  cursor-pointer">
                <SelectValue placeholder="Sort By" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="quantity">Quantity</SelectItem>
                <SelectItem value="expiry">Expiry Date</SelectItem>
                <SelectItem value="updated">Last Updated</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.sortOrder}
              onValueChange={(value) => updateFilter('sortOrder', value)}
            >
              <SelectTrigger className="focus:ring-0  cursor-pointer">
                <SelectValue placeholder="Order" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asc">A-Z / Low-High</SelectItem>
                <SelectItem value="desc">Z-A / High-Low</SelectItem>
              </SelectContent>
            </Select>
            <div className="">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                className={`flex items-center gap-1 py-[19px] ${!hasActiveFilters as boolean ? 'cursor-not-allowed' : 'cursor-pointer'}`}
              >
                <X className="h-3 w-3" />
                Clear
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};