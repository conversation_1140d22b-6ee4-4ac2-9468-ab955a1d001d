import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  useCreateProductSubCategoryMutation,
  useUpdateProductSubCategoryMutation
} from '@/redux/slices/product-sub-category';
import { useGetProductMainCategoryQuery } from '@/redux/slices/product-main-category';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Save, AlertCircle } from 'lucide-react';
import { ProductSubCategoryItem } from '../types/product-sub-category.type';

const productSubCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  code: z.string().min(1, 'Code is required').max(20, 'Code must be less than 20 characters'),
  description: z.string().nullable().optional(),
  main_category: z.string().min(1, 'Main category is required'),
});

type ProductSubCategoryFormData = z.infer<typeof productSubCategorySchema>;

interface ProductSubCategoryFormProps {
  item?: ProductSubCategoryItem;
  onClose: () => void;
  onSuccess: () => void;
}

export const ProductSubCategoryForm: React.FC<ProductSubCategoryFormProps> = ({
  item,
  onClose,
  onSuccess
}) => {
  const [createItem, { isLoading: isCreating, error: createError }] = useCreateProductSubCategoryMutation();
  const [updateItem, { isLoading: isUpdating, error: updateError }] = useUpdateProductSubCategoryMutation();
  const { data: mainCategories } = useGetProductMainCategoryQuery({ params: {} });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid }
  } = useForm<ProductSubCategoryFormData>({
    resolver: zodResolver(productSubCategorySchema),
    defaultValues: {
      name: item?.name || '',
      code: item?.code || '',
      description: item?.description || '',
      main_category: item?.main_category || '',
    },
    mode: 'onChange'
  });

  const isLoading = isCreating || isUpdating;
  const error = createError || updateError;
  const selectedMainCategory = watch('main_category');

  const onSubmit = async (data: ProductSubCategoryFormData) => {
    try {
      const submitData = {
        ...data,
        description: data.description || null,
      };

      if (item) {
        await updateItem({
          id: item.id,
          body: submitData
        }).unwrap();
      } else {
        await createItem(submitData).unwrap();
      }
      onSuccess();
    } catch (err) {
      console.error('Failed to save product sub category:', err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold">
            {item ? 'Edit Product Sub Category' : 'Add Product Sub Category'}
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">An unexpected error occurred. Please try again later.</span>
              </div>
            )}

            <div className="space-y-2">
              <label htmlFor="main_category" className="text-sm font-medium text-gray-700">
                Main Category *
              </label>
              <Select
                value={selectedMainCategory}
                onValueChange={(value) => setValue('main_category', value, { shouldValidate: true })}
              >
                <SelectTrigger className={`focus:ring-0 ${errors.main_category ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select main category" />
                </SelectTrigger>
                <SelectContent>
                  {mainCategories?.results?.map((category) => (
                    <SelectItem key={category.id} value={category.code.toString()}>
                      {category.name} ({category.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.main_category && (
                <p className="text-sm text-red-600">{errors.main_category.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium text-gray-700">
                Name *
              </label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter sub category name"
                className={`focus-visible:ring-0 ${errors.name ? 'border-red-500' : ''}`}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="code" className="text-sm font-medium text-gray-700">
                Code *
              </label>
              <Input
                id="code"
                {...register('code')}
                placeholder="Enter sub category code"
                className={`focus-visible:ring-0 ${errors.code ? 'border-red-500' : ''}`}
              />
              {errors.code && (
                <p className="text-sm text-red-600">{errors.code.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                {...register('description')}
                placeholder="Enter description (optional)"
                rows={3}
                className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="flex-1"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1"
                disabled={!isValid || isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Saving...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    {item ? 'Update' : 'Create'}
                  </div>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};