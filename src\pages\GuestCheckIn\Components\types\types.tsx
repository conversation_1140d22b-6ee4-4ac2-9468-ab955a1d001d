export interface GuestCheckItem {
  id: string;
  name: string;
  price: number;
  qty: number;
  category?: string;
  modifiers?: ItemModifier[];
  notes?: string;
  voided?: boolean;
  voidedAt?: string;
  voidedBy?: string;
  voidReason?: string;
}

export interface ItemModifier {
  id: string;
  name: string;
  price: number;
}

export interface CheckDiscount {
  id: string;
  type: 'percentage' | 'fixed';
  value: number;
  reason: string;
  appliedBy: string;
  appliedAt: string;
}

export interface CheckVoid {
  id: string;
  itemId: string;
  itemName: string;
  quantity: number;
  reason: string;
  voidedBy: string;
  voidedAt: string;
}

export interface CheckTransfer {
  id: string;
  fromEmployee: string;
  toEmployee: string;
  transferredAt: string;
  reason?: string;
}

export interface LinkedCheck {
  id: number;
  check_number: string;
  table_number: string;
  linked_at: string;
}

export interface GuestCheck {
  tableNumber: string;
  waiterName: string;
  items: GuestCheckItem[];
  voids: CheckVoid[];
  discounts: CheckDiscount[];
  transfers: CheckTransfer[];
  total: number;
  id: string;
  databaseId: number;
  check_number?: string;
  guest_count: number;
  opened_at: string;
  orderTime: string;
  closed_at?: string;
  closedAt?: string | null;
  status: string;
  payment_status: string;
  paymentStatus: string;
  sub_total: number;
  subtotal: number;
  tax_total: number;
  tax: number;
  service_charge_total: number;
  serviceCharge: number;
  discount_total: number;
  discountTotal: number;
  grand_total: number;
  order: number;
  table_number: string;
  employee: string;
  linked_checks: LinkedCheck[];
  linkedChecks: LinkedCheck[];
  qrCode?: string;
  archived?: boolean;
  archivedAt?: string;
}

export interface CheckInForm {
  tableNumber: string;
  guestCount: string;
  waiterName: string;
  specialRequests: string;
  order: string;
}

export interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  available: boolean;
  modifiers?: ItemModifier[];
}

export interface PrintConfig {
  logo?: string;
  businessName: string;
  address: string;
  phone: string;
  email: string;
  taxPin: string;
  serviceDetails: string;
  footerMessage?: string;
}

export interface UserRole {
  id: string;
  name: string;
  permissions: string[];
}

export interface CheckPermissions {
  canAddItems: boolean;
  canRemoveItems: boolean;
  canVoidItems: boolean;
  canApplyDiscounts: boolean;
  canTransferCheck: boolean;
  canMergeChecks: boolean;
  canCloseCheck: boolean;
  canViewAllChecks: boolean;
  canArchiveChecks: boolean;
}