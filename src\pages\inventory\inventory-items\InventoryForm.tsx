import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  useCreateInventoryItemMutation,
  useUpdateInventoryItemMutation
} from '@/redux/slices/inventory';
import { useGetProductQuery } from '@/redux/slices/product';
import {
  useGetInventoryUtilityStoresQuery,
  useGetInventoryUtilityBranchQuery,
} from '@/redux/slices/inventory-utilities';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { X, Save, AlertCircle, ChevronDownIcon } from 'lucide-react';
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { format } from 'date-fns';
import { InventoryItem } from '../types/inventory-item.type';
import { ProductItem } from '../types/product.type';

const inventorySchema = z.object({
  quantity_available: z.number().min(0, 'Quantity must be non-negative'),
  reorder_level: z.number().min(0, 'Reorder level must be non-negative'),
  expiry_date: z.string().nullable().optional().refine((date) => {
    if (!date) return true;
    const expiryDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return expiryDate >= today;
  }, 'Expiry date cannot be in the past'),
  product: z.string().min(1, 'Please select a valid product'),
  store: z.string().min(1, 'Please select a valid store'),
  branch: z.string().min(1, 'Please select a valid branch'),
});

type InventoryFormData = z.infer<typeof inventorySchema>;

interface InventoryFormProps {
  item?: InventoryItem;
  onClose: () => void;
  onSuccess: () => void;
}

export const InventoryForm: React.FC<InventoryFormProps> = ({
  item,
  onClose,
  onSuccess
}) => {
  const [createItem, { isLoading: isCreating, error: createError }] = useCreateInventoryItemMutation();
  const [updateItem, { isLoading: isUpdating, error: updateError }] = useUpdateInventoryItemMutation();
  
  // Fetch data for select options
  const { data: productsData, isLoading: isLoadingProducts } = useGetProductQuery({ params: {} });
  const { data: storesData, isLoading: isLoadingStores } = useGetInventoryUtilityStoresQuery({ params: {} });
  const { data: branchesData, isLoading: isLoadingBranches } = useGetInventoryUtilityBranchQuery({ params: {} });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid }
  } = useForm<InventoryFormData>({
    resolver: zodResolver(inventorySchema),
    defaultValues: {
      quantity_available: item?.quantity_available || 0,
      reorder_level: item?.reorder_level || 0,
      expiry_date: item?.expiry_date || null,
      product: item?.product || '',
      store: item?.store || '',
      branch: item?.branch || '',
    },
    mode: 'onChange'
  });

  const isLoading = isCreating || isUpdating;
  const error = createError || updateError;
  const selectedProduct = watch('product');
  const selectedStore = watch('store');
  const selectedBranch = watch('branch');
  const expiryDate = watch('expiry_date');

  const [open, setOpen] = React.useState(false);

  const onSubmit = async (data: InventoryFormData) => {
    try {
      const submitData = {
        ...data,
        expiry_date: data.expiry_date || null,
      };

      if (item) {
        await updateItem({
          id: item.id,
          body: submitData
        }).unwrap();
      } else {
        await createItem(submitData).unwrap();
      }
      onSuccess();
    } catch (err) {
      console.error('Failed to save inventory item:', err);
    }
  };
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>
            {item ? 'Edit Inventory Item' : 'Add New Inventory Item'}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <div className="flex items-center space-x-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">
                  {(error as any)?.data?.message || 'An error occurred while saving the item'}
                </span>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Product <span className="text-destructive">*</span>
                </label>
                <Select
                  value={selectedProduct}
                  onValueChange={(value) => setValue('product', value, { shouldValidate: true })}
                >
                  <SelectTrigger className={`focus:ring-0 ${errors.product ? 'border-destructive' : ''}`}>
                    <SelectValue placeholder="Select a product" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingProducts ? (
                      <SelectItem value="loading" disabled>Loading products...</SelectItem>
                    ) : productsData?.results && productsData.results.length > 0 ? (
                      productsData.results.map((product: ProductItem) => (
                        <SelectItem key={product.id} value={product.code.toString()}>
                          {product.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="not-available" disabled>No products available</SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.product && (
                  <p className="text-sm text-destructive mt-1">{errors.product.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Store <span className="text-destructive">*</span>
                </label>
                <Select
                  value={selectedStore}
                  onValueChange={(value) => setValue('store', value, { shouldValidate: true })}
                >
                  <SelectTrigger className={`focus:ring-0 ${errors.store ? 'border-destructive' : ''}`}>
                    <SelectValue placeholder="Select a store" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingStores ? (
                      <SelectItem value="loading" disabled>Loading stores...</SelectItem>
                    ) : storesData && Array.isArray(storesData) && storesData.length > 0 ? (
                      storesData.map((store: any) => (
                        <SelectItem key={store.id} value={store.code.toString()}>
                          {store.name}
                        </SelectItem>
                      ))
                    ) : storesData && storesData.results && Array.isArray(storesData.results) && storesData.results.length > 0 ? (
                      storesData.results.map((store: any) => (
                        <SelectItem key={store.id} value={store.code.toString()}>
                          {store.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="not-available" disabled>No stores available</SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.store && (
                  <p className="text-sm text-destructive mt-1">{errors.store.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Branch <span className="text-destructive">*</span>
                </label>
                <Select
                  value={selectedBranch}
                  onValueChange={(value) => setValue('branch', value, { shouldValidate: true })}
                >
                  <SelectTrigger className={`focus:ring-0 ${errors.branch ? 'border-destructive' : ''}`}>
                    <SelectValue placeholder="Select a branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingBranches ? (
                      <SelectItem value="loading" disabled>Loading branches...</SelectItem>
                    ) : branchesData && Array.isArray(branchesData) && branchesData.length > 0 ? (
                      branchesData.map((branch: any) => (
                        <SelectItem key={branch.id} value={branch.branch_code.toString()}>
                          {branch.name}
                        </SelectItem>
                      ))
                    ) : branchesData && branchesData.results && Array.isArray(branchesData.results) && branchesData.results.length > 0 ? (
                      branchesData.results.map((branch: any) => (
                        <SelectItem key={branch.id} value={branch.branch_code.toString()}>
                          {branch.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="not-available" disabled>No branches available</SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.branch && (
                  <p className="text-sm text-destructive mt-1">{errors.branch.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Quantity Available <span className="text-destructive">*</span>
                </label>
                <Input
                  type="number"
                  min="0"
                  {...register('quantity_available', { valueAsNumber: true })}
                  placeholder="Enter quantity"
                  className={`focus-visible:ring-0 ${errors.quantity_available ? 'border-destructive' : ''}`}
                />
                {errors.quantity_available && (
                  <p className="text-sm text-destructive mt-1">{errors.quantity_available.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Reorder Level <span className="text-destructive">*</span>
                </label>
                <Input
                  type="number"
                  min="0"
                  {...register('reorder_level', { valueAsNumber: true })}
                  placeholder="Enter reorder level"
                  className={`focus-visible:ring-0 ${errors.reorder_level ? 'border-destructive' : ''}`}
                />
                {errors.reorder_level && (
                  <p className="text-sm text-destructive mt-1">{errors.reorder_level.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Expiry Date (Optional)
                </label>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild className={errors.expiry_date ? 'border-destructive' : ''}>
                    <Button
                      variant="outline"
                      id="date"
                      className="w-full justify-between font-normal py-[19px]"
                    >
                      {expiryDate ? new Date(expiryDate).toLocaleDateString() : "Select date"}
                      <ChevronDownIcon />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto overflow-hidden p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={expiryDate ? new Date(expiryDate) : undefined}
                      captionLayout="dropdown"
                      onSelect={(date) => {
                        setValue('expiry_date', date ? format(new Date(date), 'yyyy-MM-dd') : null, { shouldValidate: true });
                        setOpen(false);
                      }}
                    />
                  </PopoverContent>
                </Popover>
                {errors.expiry_date && (
                  <p className="text-sm text-destructive mt-1">{errors.expiry_date.message}</p>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className='cursor-pointer'
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!isValid || isLoading}
                className="flex items-center gap-2 cursor-pointer"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {item ? 'Update Item' : 'Create Item'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};