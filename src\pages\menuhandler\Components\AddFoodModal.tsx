import React, { useState, useEffect } from "react";
import { X, Utensils, Clock, Star, ChefHat, Building2, Users, Tag, Loader2, Check } from "lucide-react";
import { useAddMenuMutation } from "@/redux/slices/menuMake";
import { useGetBranchesQuery } from "@/redux/slices/branches";
import { useGetPrintersQuery } from "@/redux/slices/printers";
import { useGetTaxClassesQuery } from "@/redux/slices/taxClasses";
import { useGetMenuGroupsQuery } from "@/redux/slices/menuGroup";
import { useGetMenuSubGroupsQuery } from "@/redux/slices/subGroup";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-500">
          Something went wrong. Please try again or contact support.
        </div>
      );
    }
    return this.props.children;
  }
}

interface CreateMenuItemModalProps {
  onClose: () => void;
  onSave: (newItem: {
    title: string;
    description: string;
    imageUrl: string;
    price: string;
    category: string;
    rating: number;
    prepTime: string;
  }) => void;
  selectedMenuGroup?: string | number;      // <-- Add this prop
  selectedMenuSubGroup?: string | number;   // <-- Add this prop
}

export function CreateMenuItemModal({
  onClose,
  onSave,
  selectedMenuGroup,
  selectedMenuSubGroup,
}: CreateMenuItemModalProps) {
  const { data: MenuGroup, isLoading: isLoadingGroup, error: groupError } = useGetMenuGroupsQuery({});
  const { data: MenuSubGroup, isLoading: isLoadingMenuSubGroup, error: MenuSubGroupError } = useGetMenuSubGroupsQuery({});
  const { data: branchData, isLoading: isLoadingBranch, error: branchError } = useGetBranchesQuery({});
  const { data: Printer, isLoading: isLoadingPrinter, error: PrinterError } = useGetPrintersQuery({});
  const { data: Tax, isLoading: isLoadingTaxClass, error: TaxError } = useGetTaxClassesQuery({});
  const [addFoodOrder, { isLoading: isSubmitting }] = useAddMenuMutation();

  const isLoading = isLoadingGroup || isLoadingMenuSubGroup || isLoadingBranch || 
                   isLoadingPrinter || isLoadingTaxClass || isSubmitting;

  const [formData, setFormData] = useState({
    name: "",
    price: "",
    description: "",
    button_color: "#FF6B35",
    availability_type: "ALWAYS",
    available_days: {},
    available_times: {},
    ordering_channels: {},
    stock_status: true,
    is_active: true,
    pos_name: "",
    menu_group: "",
    menu_subgroup: "",
    product: "",
    tax_class: "",
    printer: "",
    store: ""
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [errorMessage, setErrorMessage] = useState<string | null>(null); // Added errorMessage state
  const totalSteps = 3;

  useEffect(() => {
    if (Array.isArray(branchData) && branchData.length && !formData.store && !isLoadingBranch) {
      const defaultBranch = branchData.find((b: any) => b.is_default) || branchData[0];
      const branchId = defaultBranch?.id || defaultBranch?._id;
      if (branchId) {
        setFormData((prev) => ({
          ...prev,
          store: branchId.toString()
        }));
      }
    }
  }, [branchData, formData.store, isLoadingBranch]);

  // Set initial values if selectedMenuGroup/SubGroup are provided
  useEffect(() => {
    if (selectedMenuGroup) {
      setFormData((prev) => ({
        ...prev,
        menu_group: selectedMenuGroup.toString(),
      }));
    }
    if (selectedMenuSubGroup) {
      setFormData((prev) => ({
        ...prev,
        menu_subgroup: selectedMenuSubGroup.toString(),
      }));
    }
  }, [selectedMenuGroup, selectedMenuSubGroup]);

  const handleInputChange = (name: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));

    if (validationErrors[name]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
    setErrorMessage(null); // Clear error message on input change
  };

  const validateStep = (step: number): boolean => {
    const errors: Record<string, string> = {};
    let isValid = true;

    switch (step) {
      case 1:
        if (!formData.name.trim()) {
          errors.name = "Name is required";
          isValid = false;
        }
        if (!formData.description.trim()) {
          errors.description = "Description is required";
          isValid = false;
        }
        if (!formData.price.trim()) {
          errors.price = "Price is required";
          isValid = false;
        } else if (isNaN(Number(formData.price)) || Number(formData.price) < 0) {
          errors.price = "Price must be a valid number";
          isValid = false;
        }
        break;
      case 2:
        if (!formData.store) {
          errors.store = "Store is required";
          isValid = false;
        }
        if (!formData.menu_group) {
          errors.menu_group = "Menu group is required";
          isValid = false;
        }
        break;
      case 3:
        break;
      default:
        isValid = false;
        break;
    }

    setValidationErrors(errors);
    if (!isValid) {
      setErrorMessage("Please fill all required fields correctly");
    } else {
      setErrorMessage(null);
    }
    return isValid;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      if (currentStep === 1 && (isLoadingBranch || isLoadingGroup || !branchData?.length || !MenuGroup?.data?.results?.length)) {
        setErrorMessage("Please wait for branches and menu groups to load");
        return;
      }
      setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
    setErrorMessage(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (currentStep < totalSteps) {
      nextStep();
      return;
    }

    if (!validateStep(currentStep)) {
      return;
    }

    try {
      const payload: any = {
        name: formData.name,
        price: formData.price,
        description: formData.description,
        button_color: formData.button_color,
        availability_type: formData.availability_type,
        available_days: formData.available_days,
        available_times: formData.available_times,
        ordering_channels: formData.ordering_channels,
        stock_status: formData.stock_status,
        is_active: formData.is_active,
        pos_name: formData.pos_name || formData.name,
        store: parseInt(formData.store),
        menu_group: parseInt(formData.menu_group)
      };

      if (formData.menu_subgroup && formData.menu_subgroup !== "none") {
        payload.menu_subgroup = parseInt(formData.menu_subgroup);
      }
      if (formData.product && formData.product !== "none") {
        payload.product = parseInt(formData.product);
      }
      if (formData.tax_class && formData.tax_class !== "none") {
        payload.tax_class = parseInt(formData.tax_class);
      }
      if (formData.printer && formData.printer !== "none") {
        payload.printer = parseInt(formData.printer);
      }

      console.log("Sending payload:", payload);
      const result = await addFoodOrder(payload).unwrap();

      const savedItem = {
        title: formData.name,
        description: formData.description,
        imageUrl: "",
        price: formData.price,
        category: "",
        rating: 0,
        prepTime: "",
      };

      onSave(savedItem);
      setErrorMessage(null);
      onClose();
    } catch (error: any) {
      console.error("Failed to add menu item:", error);
      if (error?.data && typeof error.data === 'object') {
        const errorMessages = Object.entries(error.data)
          .map(([field, messages]: [string, any]) => {
            if (Array.isArray(messages)) {
              return `${field}: ${messages.join(', ')}`;
            }
            return `${field}: ${messages}`;
          })
          .join('; ');
        setErrorMessage(errorMessages || "Please check your input and try again.");
      } else {
        setErrorMessage("Failed to create menu item. Please try again.");
      }
    }
  };

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return "Basic Information";
      case 2: return "Organization";
      case 3: return "Settings & Review";
      default: return "";
    }
  };

  const getStepDescription = (step: number) => {
    switch (step) {
      case 1: return "Add the basic details of your menu item";
      case 2: return "Choose the category and location";
      case 3: return "Configure availability and finalize";
      default: return "";
    }
  };

  const getFilteredSubGroups = () => {
    if (!MenuSubGroup?.data?.results || !formData.menu_group) return [];
    return MenuSubGroup.data.results.filter((subGroup: any) => 
      subGroup.group && subGroup.group.toString() === formData.menu_group
    );
  };

  if (isLoading && !branchData && !MenuGroup && !MenuSubGroup && !Printer && !Tax) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl h-[85vh] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
          onClick={onClose}
        />
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl h-[85vh] flex flex-col overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
          <div className="relative bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 p-4 text-white flex-shrink-0">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                      <ChefHat className="h-5 w-5" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold">Create Menu Item</h2>
                      <p className="text-white/90 text-xs">
                        {getStepDescription(currentStep)}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onClose}
                    className="text-white hover:bg-white/20 rounded-full"
                    disabled={isLoading}
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
                <div className="flex items-center gap-3">
                  {[1, 2, 3].map((step) => (
                    <div key={step} className="flex items-center gap-2">
                      <div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-300 ${
                        step < currentStep 
                          ? 'bg-white text-orange-500' 
                          : step === currentStep 
                            ? 'bg-white/20 text-white ring-2 ring-white' 
                            : 'bg-white/10 text-white/60'
                      }`}>
                        {step < currentStep ? <Check className="h-3 w-3" /> : step}
                      </div>
                      <span className={`text-xs font-medium ${step === currentStep ? 'text-white' : 'text-white/60'}`}>
                        {getStepTitle(step)}
                      </span>
                      {step < 3 && <div className="w-6 h-0.5 bg-white/20 ml-1" />}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="p-6">
                {errorMessage && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm mb-4">
                    {errorMessage}
                  </div>
                )}
                <form onSubmit={handleSubmit} className="space-y-6">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-64">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <>
                      {currentStep === 1 && (
                        <Card className="border-0 shadow-none">
                          <CardContent className="p-0 space-y-6">
                            <div className="space-y-2">
                              <Label htmlFor="name" className="text-sm font-semibold flex items-center gap-2">
                                <Utensils className="h-4 w-4 text-orange-500" />
                                Menu Item Name <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="name"
                                placeholder="e.g., Grilled Salmon with Herbs"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                                className="h-11 text-base"
                                disabled={isLoading}
                              />
                              {validationErrors.name && (
                                <p className="text-sm text-red-500">{validationErrors.name}</p>
                              )}
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="price" className="text-sm font-semibold">
                                Price <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="price"
                                placeholder="e.g., 15.99"
                                value={formData.price}
                                onChange={(e) => handleInputChange("price", e.target.value)}
                                className="h-11 text-base"
                                type="number"
                                step="0.01"
                                disabled={isLoading}
                              />
                              {validationErrors.price && (
                                <p className="text-sm text-red-500">{validationErrors.price}</p>
                              )}
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="description" className="text-sm font-semibold">
                                Description <span className="text-red-500">*</span>
                              </Label>
                              <Textarea
                                id="description"
                                placeholder="Describe your menu item in detail"
                                value={formData.description}
                                onChange={(e) => handleInputChange("description", e.target.value)}
                                className="min-h-[100px] text-base resize-none"
                                disabled={isLoading}
                              />
                              {validationErrors.description && (
                                <p className="text-sm text-red-500">{validationErrors.description}</p>
                              )}
                              <p className="text-xs text-muted-foreground">
                                {formData.description.length}/500 characters
                              </p>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="pos_name" className="text-sm font-semibold">
                                POS Name
                              </Label>
                              <Input
                                id="pos_name"
                                placeholder="Name displayed on POS system (optional)"
                                value={formData.pos_name}
                                onChange={(e) => handleInputChange("pos_name", e.target.value)}
                                className="h-11 text-base"
                                disabled={isLoading}
                              />
                              <p className="text-xs text-muted-foreground">
                                If left empty, the menu item name will be used
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {currentStep === 2 && (
                        <Card className="border-0 shadow-none">
                          <CardContent className="p-0 space-y-6">
                            <div className="space-y-2">
                              <Label className="text-sm font-semibold flex items-center gap-2">
                                <Building2 className="h-4 w-4 text-blue-500" />
                                Store <span className="text-red-500">*</span>
                              </Label>
                              {isLoadingBranch ? (
                                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span className="text-sm">Loading stores...</span>
                                </div>
                              ) : branchError ? (
                                <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                                  Error loading stores
                                </div>
                              ) : !branchData?.length ? (
                                <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                  No stores available
                                </div>
                              ) : (
                                <>
                                  <Select 
                                    value={formData.store} 
                                    onValueChange={(value) => handleInputChange("store", value)}
                                    disabled={isLoading}
                                  >
                                    <SelectTrigger className="h-11">
                                      <SelectValue placeholder="Select a store" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {branchData
                                        .filter((branch: any) => branch && (branch.id || branch._id))
                                        .map((branch: any) => (
                                          <SelectItem 
                                            key={branch.id || branch._id} 
                                            value={(branch.id || branch._id).toString()}
                                          >
                                            <div className="flex items-center gap-2">
                                              <Badge variant="outline" className="text-xs">
                                                {branch.branch_code || "N/A"}
                                              </Badge>
                                              {branch.name || "Unknown"}
                                            </div>
                                          </SelectItem>
                                        ))}
                                    </SelectContent>
                                  </Select>
                                  {validationErrors.store && (
                                    <p className="text-sm text-red-500">{validationErrors.store}</p>
                                  )}
                                </>
                              )}
                            </div>

                            <div className="space-y-2">
                              <Label className="text-sm font-semibold flex items-center gap-2">
                                <Users className="h-4 w-4 text-green-500" />
                                Menu Group <span className="text-red-500">*</span>
                              </Label>
                              {selectedMenuGroup ? (
                                <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                  {
                                    MenuGroup?.data?.results?.find(
                                      (g: any) => g.id.toString() === formData.menu_group
                                    )?.name || "Selected group"
                                  }
                                </div>
                              ) : isLoadingGroup ? (
                                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span className="text-sm">Loading menu groups...</span>
                                </div>
                              ) : groupError ? (
                                <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                                  Error loading menu groups
                                </div>
                              ) : !MenuGroup?.data?.results?.length ? (
                                <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                  No menu groups available
                                </div>
                              ) : (
                                <>
                                  <Select 
                                    value={formData.menu_group} 
                                    onValueChange={(value) => {
                                      handleInputChange("menu_group", value);
                                      handleInputChange("menu_subgroup", "");
                                    }}
                                    disabled={isLoading}
                                  >
                                    <SelectTrigger className="h-11">
                                      <SelectValue placeholder="Select a menu group" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {MenuGroup.data.results
                                        .filter((group: any) => group.id)
                                        .map((group: any) => (
                                          <SelectItem 
                                            key={group.id} 
                                            value={group.id.toString()}
                                          >
                                            {group.name || "Unknown"}
                                          </SelectItem>
                                        ))}
                                    </SelectContent>
                                  </Select>
                                  {validationErrors.menu_group && (
                                    <p className="text-sm text-red-500">{validationErrors.menu_group}</p>
                                  )}
                                </>
                              )}
                            </div>

                            <div className="space-y-2">
                              <Label className="text-sm font-semibold flex items-center gap-2">
                                <Tag className="h-4 w-4 text-purple-500" />
                                Menu Sub Group (Optional)
                              </Label>
                              {selectedMenuSubGroup ? (
                                <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                  {
                                    getFilteredSubGroups().find(
                                      (sg: any) => sg.id.toString() === formData.menu_subgroup
                                    )?.name || "Selected sub group"
                                  }
                                </div>
                              ) : isLoadingMenuSubGroup ? (
                                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span className="text-sm">Loading sub groups...</span>
                                </div>
                              ) : MenuSubGroupError ? (
                                <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                                  Error loading sub groups
                                </div>
                              ) : !formData.menu_group ? (
                                <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                  Please select a menu group first
                                </div>
                              ) : !getFilteredSubGroups().length ? (
                                <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                  No sub groups available
                                </div>
                              ) : (
                                <Select 
                                  value={formData.menu_subgroup} 
                                  onValueChange={(value) => handleInputChange("menu_subgroup", value)}
                                  disabled={isLoading}
                                >
                                  <SelectTrigger className="h-11">
                                    <SelectValue placeholder="Select a sub group (optional)" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="none">No sub group</SelectItem>
                                    {getFilteredSubGroups()
                                      .filter((subGroup: any) => subGroup.id)
                                      .map((subGroup: any) => (
                                        <SelectItem 
                                          key={subGroup.id} 
                                          value={subGroup.id.toString()}
                                        >
                                          <div className="space-y-1">
                                            <div className="font-medium">{subGroup.name || "Unknown"}</div>
                                            <div className="text-xs text-muted-foreground">{subGroup.description || "No description"}</div>
                                          </div>
                                        </SelectItem>
                                      ))}
                                  </SelectContent>
                                </Select>
                              )}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label className="text-sm font-semibold">Tax Class (Optional)</Label>
                                {isLoadingTaxClass ? (
                                  <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span className="text-sm">Loading...</span>
                                  </div>
                                ) : TaxError ? (
                                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                                    Error loading tax classes
                                  </div>
                                ) : !Tax?.data?.results?.length ? (
                                  <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                    No tax classes available
                                  </div>
                                ) : (
                                  <Select 
                                    value={formData.tax_class} 
                                    onValueChange={(value) => handleInputChange("tax_class", value)}
                                    disabled={isLoading}
                                  >
                                    <SelectTrigger className="h-11">
                                      <SelectValue placeholder="Select tax class (optional)" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="none">No tax class</SelectItem>
                                      {Tax.data.results
                                        .filter((tax: any) => tax.id)
                                        .map((tax: any) => (
                                          <SelectItem 
                                            key={tax.id} 
                                            value={tax.id.toString()}
                                          >
                                            {tax.name || "Unknown"}
                                          </SelectItem>
                                        ))}
                                    </SelectContent>
                                  </Select>
                                )}
                              </div>

                              <div className="space-y-2">
                                <Label className="text-sm font-semibold">Printer (Optional)</Label>
                                {isLoadingPrinter ? (
                                  <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span className="text-sm">Loading...</span>
                                  </div>
                                ) : PrinterError ? (
                                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                                    Error loading printers
                                  </div>
                                ) : !Printer?.data?.results?.length ? (
                                  <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                                    No printers available
                                  </div>
                                ) : (
                                  <Select 
                                    value={formData.printer} 
                                    onValueChange={(value) => handleInputChange("printer", value)}
                                    disabled={isLoading}
                                  >
                                    <SelectTrigger className="h-11">
                                      <SelectValue placeholder="Select printer (optional)" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="none">No printer</SelectItem>
                                      {Printer.data.results
                                        .filter((printer: any) => printer.id)
                                        .map((printer: any) => (
                                          <SelectItem 
                                            key={printer.id} 
                                            value={printer.id.toString()}
                                          >
                                            {printer.name || "Unknown"}
                                          </SelectItem>
                                        ))}
                                    </SelectContent>
                                  </Select>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {currentStep === 3 && (
                        <Card className="border-0 shadow-none">
                          <CardContent className="p-0 space-y-6">
                            <div className="space-y-4">
                              <Label className="text-sm font-semibold flex items-center gap-2">
                                <Clock className="h-4 w-4 text-indigo-500" />
                                Availability Settings
                              </Label>
                              <div className="space-y-2">
                                <Label className="text-sm">Availability Type</Label>
                                <Select 
                                  value={formData.availability_type} 
                                  onValueChange={(value) => handleInputChange("availability_type", value)}
                                  disabled={isLoading}
                                >
                                  <SelectTrigger className="h-11">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="ALWAYS">
                                      <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                        Always Available
                                      </div>
                                    </SelectItem>
                                    <SelectItem value="SCHEDULED">
                                      <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                        Scheduled Availability
                                      </div>
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                                  <div className="space-y-1">
                                    <Label className="text-sm font-medium">Active Status</Label>
                                    <p className="text-xs text-muted-foreground">
                                      Enable for ordering
                                    </p>
                                  </div>
                                  <Switch
                                    checked={formData.is_active}
                                    onCheckedChange={(checked) => handleInputChange("is_active", checked)}
                                    disabled={isLoading}
                                  />
                                </div>
                                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                                  <div className="space-y-1">
                                    <Label className="text-sm font-medium">Stock Status</Label>
                                    <p className="text-xs text-muted-foreground">
                                      In stock
                                    </p>
                                  </div>
                                  <Switch
                                    checked={formData.stock_status}
                                    onCheckedChange={(checked) => handleInputChange("stock_status", checked)}
                                    disabled={isLoading}
                                  />
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="button_color" className="text-sm font-semibold">
                                  Button Color
                                </Label>
                                <Input
                                  id="button_color"
                                  type="color"
                                  value={formData.button_color}
                                  onChange={(e) => handleInputChange("button_color", e.target.value)}
                                  className="h-11 w-20"
                                  disabled={isLoading}
                                />
                              </div>
                            </div>

                            <Separator />

                            <div className="space-y-4">
                              <Label className="text-sm font-semibold flex items-center gap-2">
                                <Star className="h-4 w-4 text-yellow-500" />
                                Review Your Menu Item
                              </Label>
                              <div className="p-4 bg-muted/30 rounded-lg space-y-3">
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">Name</p>
                                  <p className="font-semibold">{formData.name || "Not specified"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">Price</p>
                                  <p className="font-semibold">${formData.price || "0.00"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">Description</p>
                                  <p className="text-sm">{formData.description || "Not specified"}</p>
                                </div>
                                <div className="flex gap-4">
                                  <div>
                                    <p className="text-sm font-medium text-muted-foreground">Store</p>
                                    <p className="text-sm">{branchData?.find((b: any) => (b.id || b._id).toString() === formData.store)?.name || "Not selected"}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-muted-foreground">Group</p>
                                    <p className="text-sm">{MenuGroup?.data?.results?.find((g: any) => g.id.toString() === formData.menu_group)?.name || "Not selected"}</p>
                                  </div>
                                </div>
                                {(formData.menu_subgroup || formData.tax_class || formData.printer) && (
                                  <div className="flex gap-4">
                                    {formData.menu_subgroup && (
                                      <div>
                                        <p className="text-sm font-medium text-muted-foreground">Sub Group</p>
                                        <p className="text-sm">{getFilteredSubGroups().find((sg: any) => sg.id.toString() === formData.menu_subgroup)?.name || "Not found"}</p>
                                      </div>
                                    )}
                                    {formData.tax_class && (
                                      <div>
                                        <p className="text-sm font-medium text-muted-foreground">Tax Class</p>
                                        <p className="text-sm">{Tax?.data?.results?.find((t: any) => t.id.toString() === formData.tax_class)?.name || "Not found"}</p>
                                      </div>
                                    )}
                                    {formData.printer && (
                                      <div>
                                        <p className="text-sm font-medium text-muted-foreground">Printer</p>
                                        <p className="text-sm">{Printer?.data?.results?.find((p: any) => p.id.toString() === formData.printer)?.name || "Not found"}</p>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </>
                  )}
                </form>
              </div>
            </ScrollArea>
          </div>

          <div className="border-t bg-muted/30 p-4 flex-shrink-0">
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1 || isLoading}
                className="min-w-[100px]"
              >
                Previous
              </Button>
              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                {currentStep < totalSteps ? (
                  <Button
                    type="button"
                    onClick={nextStep}
                    className="min-w-[100px] bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                    disabled={isLoading}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    onClick={handleSubmit}
                    disabled={isLoading}
                    className="min-w-[100px] bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Create Item
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}