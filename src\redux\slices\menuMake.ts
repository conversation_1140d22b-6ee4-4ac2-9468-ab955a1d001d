import { apiSlice } from "../apiSlice";

export const menuApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMenus: builder.query({
      query: (params) => ({
        url: "/menu/menu-items",
        method: "GET",
        params,
      }),
      providesTags: ["Menu"],
    }),

    

    

    retrieveMenu: builder.query({
      query: (id) => ({
        url: `/menu/menu-items/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Menu", id }],
    }),

    addMenu: builder.mutation({
      query: (payload) => ({
        url: "/menu/menu-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Menu"],
    }),

    patchMenu: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/menu/menu-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Menu", id },
        "Menu",
      ],
    }),

    deleteMenu: builder.mutation({
      query: (id) => ({
        url: `/menu/menu-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Menu"],
    }),
  }),
});

export const {
  useGetMenusQuery,

  
  useRetrieveMenuQuery,
  useAddMenuMutation,
  usePatchMenuMutation,
  useDeleteMenuMutation,
  useLazyGetMenusQuery,
  useLazyRetrieveMenuQuery,
} = menuApiSlice;