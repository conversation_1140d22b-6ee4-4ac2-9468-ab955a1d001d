import { GuestCheck, PrintConfig } from '../Components/types/types';

/**
 * Default print configuration
 */
export const DEFAULT_PRINT_CONFIG: PrintConfig = {
  businessName: 'Restaurant Name',
  address: '123 Main Street, City, Country',
  phone: '+*********** 000',
  email: '<EMAIL>',
  taxPin: 'P051234567M',
  serviceDetails: 'Service Charge: 10% | VAT: 16%',
  footerMessage: 'Thank you for dining with us!',
};

/**
 * Generate print-ready HTML for a guest check
 */
export const generateCheckPrintHTML = (
  check: GuestCheck, 
  config: PrintConfig = DEFAULT_PRINT_CONFIG,
  isPrintPreview: boolean = false
): string => {
  const formatKES = (value: number): string => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString('en-KE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const activeItems = check.items.filter(item => !item.voided);
  const voidedItems = check.items.filter(item => item.voided);

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Check #${check.id}</title>
      <style>
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
        body {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
          max-width: 300px;
          margin: 0 auto;
          padding: 10px;
          background: white;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #000;
          padding-bottom: 10px;
          margin-bottom: 15px;
        }
        .logo {
          max-width: 100px;
          height: auto;
          margin-bottom: 10px;
        }
        .business-name {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .business-info {
          font-size: 10px;
          margin-bottom: 2px;
        }
        .check-info {
          margin-bottom: 15px;
          border-bottom: 1px dashed #000;
          padding-bottom: 10px;
        }
        .check-info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 3px;
        }
        .items-section {
          margin-bottom: 15px;
        }
        .item-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
          align-items: flex-start;
        }
        .item-details {
          flex: 1;
          margin-right: 10px;
        }
        .item-name {
          font-weight: bold;
        }
        .item-modifiers {
          font-size: 10px;
          color: #666;
          margin-left: 10px;
        }
        .item-notes {
          font-size: 10px;
          font-style: italic;
          color: #666;
          margin-left: 10px;
        }
        .item-price {
          white-space: nowrap;
        }
        .voided-item {
          text-decoration: line-through;
          color: #999;
        }
        .void-reason {
          font-size: 10px;
          color: #999;
          font-style: italic;
        }
        .totals-section {
          border-top: 1px solid #000;
          padding-top: 10px;
          margin-top: 15px;
        }
        .total-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 3px;
        }
        .grand-total {
          font-weight: bold;
          font-size: 14px;
          border-top: 1px solid #000;
          padding-top: 5px;
          margin-top: 5px;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          border-top: 1px dashed #000;
          padding-top: 10px;
          font-size: 10px;
        }
        .qr-section {
          text-align: center;
          margin: 15px 0;
        }
        .qr-code {
          width: 100px;
          height: 100px;
          margin: 0 auto;
          border: 1px solid #000;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 8px;
        }
        ${isPrintPreview ? `
          .preview-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .preview-controls button {
            margin: 0 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
          }
          .print-btn {
            background: #007bff;
            color: white;
          }
          .close-btn {
            background: #6c757d;
            color: white;
          }
        ` : ''}
      </style>
    </head>
    <body>
      ${isPrintPreview ? `
        <div class="preview-controls no-print">
          <button class="print-btn" onclick="window.print()">Print</button>
          <button class="close-btn" onclick="window.close()">Close</button>
        </div>
      ` : ''}
      
      <div class="header">
        ${config.logo ? `<img src="${config.logo}" alt="Logo" class="logo">` : ''}
        <div class="business-name">${config.businessName}</div>
        <div class="business-info">${config.address}</div>
        <div class="business-info">Tel: ${config.phone}</div>
        <div class="business-info">Email: ${config.email}</div>
        <div class="business-info">Tax PIN: ${config.taxPin}</div>
      </div>

      <div class="check-info">
        <div class="check-info-row">
          <span>Check #:</span>
          <span>${check.id}</span>
        </div>
        <div class="check-info-row">
          <span>Table:</span>
          <span>${check.tableNumber}</span>
        </div>
        <div class="check-info-row">
          <span>Guests:</span>
          <span>${check.guest_count}</span>
        </div>
        <div class="check-info-row">
          <span>Server:</span>
          <span>${check.waiterName}</span>
        </div>
        <div class="check-info-row">
          <span>Date/Time:</span>
          <span>${formatDateTime(check.opened_at)}</span>
        </div>
        ${check.status === 'closed' && check.closedAt ? `
          <div class="check-info-row">
            <span>Closed:</span>
            <span>${formatDateTime(check.closedAt)}</span>
          </div>
        ` : ''}
      </div>

      <div class="items-section">
        <div style="font-weight: bold; margin-bottom: 10px;">ITEMS ORDERED:</div>
        ${activeItems.map(item => `
          <div class="item-row">
            <div class="item-details">
              <div class="item-name">${item.qty}x ${item.name}</div>
              ${item.modifiers && item.modifiers.length > 0 ? `
                <div class="item-modifiers">
                  ${item.modifiers.map(mod => `+ ${mod.name} (${formatKES(mod.price)})`).join('<br>')}
                </div>
              ` : ''}
              ${item.notes ? `<div class="item-notes">Note: ${item.notes}</div>` : ''}
            </div>
            <div class="item-price">${formatKES(item.price * item.qty)}</div>
          </div>
        `).join('')}

        ${voidedItems.length > 0 ? `
          <div style="margin-top: 15px; font-weight: bold;">VOIDED ITEMS:</div>
          ${voidedItems.map(item => `
            <div class="item-row voided-item">
              <div class="item-details">
                <div class="item-name">${item.qty}x ${item.name}</div>
                ${item.voidReason ? `<div class="void-reason">Void: ${item.voidReason}</div>` : ''}
              </div>
              <div class="item-price">${formatKES(item.price * item.qty)}</div>
            </div>
          `).join('')}
        ` : ''}
      </div>

      <div class="totals-section">
        <div class="total-row">
          <span>Subtotal:</span>
          <span>${formatKES(check.subtotal)}</span>
        </div>
        
        ${check.discounts && check.discounts.length > 0 ? `
          ${check.discounts.map(discount => `
            <div class="total-row">
              <span>Discount (${discount.reason}):</span>
              <span>-${discount.type === 'percentage' ? discount.value + '%' : formatKES(discount.value)}</span>
            </div>
          `).join('')}
        ` : ''}
        
        <div class="total-row">
          <span>Service Charge:</span>
          <span>${formatKES(check.serviceCharge)}</span>
        </div>
        <div class="total-row">
          <span>Tax (VAT):</span>
          <span>${formatKES(check.tax)}</span>
        </div>
        <div class="total-row grand-total">
          <span>TOTAL:</span>
          <span>${formatKES(check.total)}</span>
        </div>
      </div>

      ${check.qrCode ? `
        <div class="qr-section">
          <div>Scan to view check online:</div>
          <div class="qr-code">
            QR Code<br>
            ${check.id}
          </div>
        </div>
      ` : ''}

      <div class="footer">
        <div>${config.serviceDetails}</div>
        ${config.footerMessage ? `<div style="margin-top: 10px;">${config.footerMessage}</div>` : ''}
        <div style="margin-top: 10px;">
          Printed: ${formatDateTime(new Date().toISOString())}
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Print a guest check
 */
export const printCheck = (
  check: GuestCheck, 
  config?: PrintConfig, 
  preview: boolean = false
): void => {
  const printHTML = generateCheckPrintHTML(check, config, preview);
  
  if (preview) {
    const printWindow = window.open('', '_blank', 'width=400,height=600');
    if (printWindow) {
      printWindow.document.write(printHTML);
      printWindow.document.close();
    }
  } else {
    const printWindow = window.open('', '_blank', 'width=400,height=600');
    if (printWindow) {
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    }
  }
};

/**
 * Generate receipt for final print after payment
 */
export const printFinalReceipt = (
  check: GuestCheck, 
  paymentDetails: { method: string; amount: number; change?: number },
  config?: PrintConfig
): void => {
  const receiptHTML = generateCheckPrintHTML(check, config, false) + `
    <div style="border-top: 2px solid #000; margin-top: 20px; padding-top: 10px;">
      <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">PAYMENT DETAILS</div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
        <span>Payment Method:</span>
        <span>${paymentDetails.method}</span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
        <span>Amount Paid:</span>
        <span>${new Intl.NumberFormat('en-KE', { style: 'currency', currency: 'KES' }).format(paymentDetails.amount)}</span>
      </div>
      ${paymentDetails.change ? `
        <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
          <span>Change:</span>
          <span>${new Intl.NumberFormat('en-KE', { style: 'currency', currency: 'KES' }).format(paymentDetails.change)}</span>
        </div>
      ` : ''}
      <div style="text-align: center; margin-top: 15px; font-weight: bold;">
        PAID - THANK YOU!
      </div>
    </div>
  `;

  const printWindow = window.open('', '_blank', 'width=400,height=600');
  if (printWindow) {
    printWindow.document.write(receiptHTML);
    printWindow.document.close();
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
  }
};