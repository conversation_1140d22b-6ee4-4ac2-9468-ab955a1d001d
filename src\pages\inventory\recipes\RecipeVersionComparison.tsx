import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  ArrowLeft, 
  GitCompare, 
  ChefHat, 
  AlertTriangle,
  Plus,
  Minus
} from 'lucide-react';
import type { Recipe } from '@/types/recipe-type';
import { useGetRecipeVersionQuery } from '@/redux/slices/recipe-version';

interface RecipeVersionComparisonProps {
  recipe: Recipe;
  onClose: () => void;
}

export const RecipeVersionComparison: React.FC<RecipeVersionComparisonProps> = ({
  recipe,
  onClose
}) => {
  const [version1, setVersion1] = useState<string>('');
  const [version2, setVersion2] = useState<string>('');

  const { data: versionsData, isLoading } = useGetRecipeVersionQuery({
    params: { recipe: recipe.id }
  });

  const versions = versionsData?.results || [];

  const getVersionData = (versionId: string) => {
    const version = versions.find(v => v.id.toString() === versionId);
    return version?.data_snapshot || {};
  };

  const version1Data = version1 ? getVersionData(version1) : {};
  const version2Data = version2 ? getVersionData(version2) : {};

  const compareValues = (key: string, val1: any, val2: any) => {
    if (JSON.stringify(val1) !== JSON.stringify(val2)) {
      return 'changed';
    }
    return 'unchanged';
  };

  const renderComparisonRow = (label: string, key: string, val1: any, val2: any) => {
    const status = compareValues(key, val1, val2);
    const isChanged = status === 'changed';

    const formatValue = (value: any) => {
      if (value === null || value === undefined) return '-';
      if (typeof value === 'object') {
        if (Array.isArray(value)) {
          return value.length > 0 ? value.join(', ') : '-';
        }
        return JSON.stringify(value);
      }
      return String(value);
    };

    return (
      <TableRow key={key} className={isChanged ? 'bg-red-50 border-l-4 border-l-red-400' : ''}>
        <TableCell className="font-medium">{label}</TableCell>
        <TableCell className={isChanged ? 'text-red-700 font-medium bg-red-100' : ''}>
          {formatValue(val1)}
        </TableCell>
        <TableCell className={isChanged ? 'text-red-700 font-medium bg-red-100' : ''}>
          {formatValue(val2)}
        </TableCell>
        <TableCell>
          {isChanged && (
            <Badge variant="destructive" className="text-xs">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Changed
            </Badge>
          )}
        </TableCell>
      </TableRow>
    );
  };

  const renderIngredientsComparison = () => {
    const ingredients1 = version1Data.ingredients || [];
    const ingredients2 = version2Data.ingredients || [];
    
    // Create a map of all unique ingredients
    const allIngredients = new Map();
    
    ingredients1.forEach((ing: any, index: number) => {
      allIngredients.set(ing.inventory_item_id || index, {
        v1: ing,
        v2: null
      });
    });
    
    ingredients2.forEach((ing: any, index: number) => {
      const key = ing.inventory_item_id || index;
      if (allIngredients.has(key)) {
        allIngredients.get(key).v2 = ing;
      } else {
        allIngredients.set(key, {
          v1: null,
          v2: ing
        });
      }
    });

    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ChefHat className="h-5 w-5" />
            Ingredients Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ingredient</TableHead>
                <TableHead>Version 1 Quantity</TableHead>
                <TableHead>Version 2 Quantity</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from(allIngredients.entries()).map(([key, { v1, v2 }]) => {
                const isChanged = JSON.stringify(v1) !== JSON.stringify(v2);
                const isAdded = !v1 && v2;
                const isRemoved = v1 && !v2;
                
                return (
                  <TableRow key={key} className={isChanged ? 'bg-red-50 border-l-4 border-l-red-400' : ''}>
                    <TableCell className="font-medium">
                      {v1?.inventory_item_name || v2?.inventory_item_name || `Item ${key}`}
                    </TableCell>
                    <TableCell className={isChanged ? 'text-red-700 font-medium bg-red-100' : ''}>
                      {v1 ? `${v1.quantity} ${v1.unit_name || ''}` : '-'}
                    </TableCell>
                    <TableCell className={isChanged ? 'text-red-700 font-medium bg-red-100' : ''}>
                      {v2 ? `${v2.quantity} ${v2.unit_name || ''}` : '-'}
                    </TableCell>
                    <TableCell>
                      {isAdded && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <Plus className="h-3 w-3 mr-1" />
                          Added
                        </Badge>
                      )}
                      {isRemoved && (
                        <Badge variant="destructive">
                          <Minus className="h-3 w-3 mr-1" />
                          Removed
                        </Badge>
                      )}
                      {isChanged && !isAdded && !isRemoved && (
                        <Badge variant="destructive">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Changed
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading versions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onClose} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <GitCompare className="h-6 w-6" />
              Compare Recipe Versions
            </h1>
            <p className="text-muted-foreground">{recipe.name}</p>
          </div>
        </div>
      </div>

      {/* Version Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Versions to Compare</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Version 1</label>
              <Select value={version1} onValueChange={setVersion1}>
                <SelectTrigger>
                  <SelectValue placeholder="Select first version" />
                </SelectTrigger>
                <SelectContent>
                  {versions.map((version) => (
                    <SelectItem key={version.id} value={version.id.toString()}>
                      Version {version.version_number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Version 2</label>
              <Select value={version2} onValueChange={setVersion2}>
                <SelectTrigger>
                  <SelectValue placeholder="Select second version" />
                </SelectTrigger>
                <SelectContent>
                  {versions.map((version) => (
                    <SelectItem key={version.id} value={version.id.toString()}>
                      Version {version.version_number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comparison Results */}
      {version1 && version2 && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Basic Information Comparison
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Field</TableHead>
                    <TableHead>Version {versions.find(v => v.id.toString() === version1)?.version_number}</TableHead>
                    <TableHead>Version {versions.find(v => v.id.toString() === version2)?.version_number}</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {renderComparisonRow('Recipe Name', 'name', version1Data.name, version2Data.name)}
                  {renderComparisonRow('Recipe Type', 'recipe_type', version1Data.recipe_type, version2Data.recipe_type)}
                  {renderComparisonRow('Portion Size', 'portion_size', version1Data.portion_size, version2Data.portion_size)}
                  {renderComparisonRow('Preparation Time', 'preparation_time', version1Data.preparation_time, version2Data.preparation_time)}
                  {renderComparisonRow('Cooking Time', 'cooking_time', version1Data.cooking_time, version2Data.cooking_time)}
                  {renderComparisonRow('Instructions', 'instructions', version1Data.instructions, version2Data.instructions)}
                  {renderComparisonRow('Tools Required', 'tools_required', version1Data.tools_required, version2Data.tools_required)}
                  {renderComparisonRow('Labor Cost', 'labor_cost', version1Data.labor_cost, version2Data.labor_cost)}
                  {renderComparisonRow('Packaging Cost', 'packaging_cost', version1Data.packaging_cost, version2Data.packaging_cost)}
                  {renderComparisonRow('Target Food Cost %', 'target_food_cost_percentage', version1Data.target_food_cost_percentage, version2Data.target_food_cost_percentage)}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {renderIngredientsComparison()}
        </>
      )}

      {(!version1 || !version2) && (
        <Card>
          <CardContent className="text-center py-12">
            <GitCompare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Select two versions to compare their differences
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};