import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { StoreRequisition } from "@/types/procurement";
import { format } from "date-fns";
import {
  Calendar,
  User,
  Building,
  FileText,
  Package,
  Hash,
  Clock,
  Send,
  CheckCircle,
  XCircle,
  ArrowRight,
  AlertTriangle,
  Loader2,
  Eye,
  Edit,
  ShoppingCart
} from "lucide-react";
import { useState } from "react";
import {
  useSubmitStoreRequisitionMutation,
  useApproveStoreRequisitionMutation,
  useRejectStoreRequisitionMutation,
  useConvertToPurchaseRequisitionMutation,
  useMarkStoreRequisitionAsConvertedMutation
} from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";
import RejectStoreRequisition from "./RejectStoreRequisition";

interface ViewStoreRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
  requisition: StoreRequisition | null;
  onEdit?: (requisition: StoreRequisition) => void;
}

const ViewStoreRequisition = ({ isOpen, onClose, requisition, onEdit }: ViewStoreRequisitionProps) => {
  const [activeTab, setActiveTab] = useState("details");
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);

  // Mutation hooks
  const [submitStoreRequisition, { isLoading: isSubmitting }] = useSubmitStoreRequisitionMutation();
  const [approveStoreRequisition, { isLoading: isApproving }] = useApproveStoreRequisitionMutation();
  const [convertToPurchaseRequisition, { isLoading: isConverting }] = useConvertToPurchaseRequisitionMutation();
  const [markStoreRequisitionAsConverted] = useMarkStoreRequisitionAsConvertedMutation();

  if (!requisition) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Draft": return "bg-gray-100 text-gray-800";
      case "Submitted": return "bg-blue-100 text-blue-800";
      case "Approved": return "bg-green-100 text-green-800";
      case "Rejected": return "bg-red-100 text-red-800";
      case "Issued": return "bg-purple-100 text-purple-800";
      case "Converted": return "bg-orange-100 text-orange-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Draft": return <Edit className="h-4 w-4" />;
      case "Submitted": return <Send className="h-4 w-4" />;
      case "Approved": return <CheckCircle className="h-4 w-4" />;
      case "Rejected": return <XCircle className="h-4 w-4" />;
      case "Issued": return <Package className="h-4 w-4" />;
      case "Converted": return <ShoppingCart className="h-4 w-4" />;
      default: return <Eye className="h-4 w-4" />;
    }
  };

  // Action handlers
  const handleSubmit = async () => {
    try {
      await submitStoreRequisition(requisition.id).unwrap();
      toast.success("Store requisition submitted successfully");
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async () => {
    try {
      await approveStoreRequisition(requisition.id).unwrap();
      toast.success("Store requisition approved successfully");
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = () => {
    setIsRejectModalOpen(true);
  };

  const handleConvertToPurchaseRequisition = async () => {
    try {
      const result = await convertToPurchaseRequisition(requisition).unwrap();
      await markStoreRequisitionAsConverted(requisition.id).unwrap();
      toast.success(`Successfully converted to purchase requisition: ${result.code || 'PR-' + String(result.id).padStart(6, '0')}`);
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to convert to purchase requisition");
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(requisition);
      onClose();
    }
  };

  // Get available actions based on status
  const getAvailableActions = () => {
    const actions = [];

    if (requisition.status === "Draft") {
      actions.push(
        <Button key="edit" variant="outline" onClick={handleEdit} className="flex items-center gap-2">
          <Edit className="h-4 w-4" />
          Edit
        </Button>,
        <Button key="submit" onClick={handleSubmit} disabled={isSubmitting} className="flex items-center gap-2">
          {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
          Submit
        </Button>
      );
    }

    if (requisition.status === "Submitted") {
      actions.push(
        <Button key="approve" onClick={handleApprove} disabled={isApproving} className="flex items-center gap-2">
          {isApproving ? <Loader2 className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
          Approve
        </Button>,
        <Button key="reject" variant="destructive" onClick={handleReject} className="flex items-center gap-2">
          <XCircle className="h-4 w-4" />
          Reject
        </Button>
      );
    }

    if (requisition.status === "Approved") {
      actions.push(
        <Button key="convert" onClick={handleConvertToPurchaseRequisition} disabled={isConverting} className="flex items-center gap-2">
          {isConverting ? <Loader2 className="h-4 w-4 animate-spin" /> : <ArrowRight className="h-4 w-4" />}
          Convert to Purchase Requisition
        </Button>
      );
    }

    return actions;
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Store Requisition Details - {requisition.code}
              </div>
              <div className="flex items-center gap-2">
                <Badge className={`${getStatusColor(requisition.status)} flex items-center gap-1`}>
                  {getStatusIcon(requisition.status)}
                  {requisition.status}
                </Badge>
              </div>
            </DialogTitle>
          </DialogHeader>

          {/* Action Buttons */}
          {getAvailableActions().length > 0 && (
            <div className="flex items-center gap-2 p-4 bg-gray-50 rounded-lg border">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <AlertTriangle className="h-4 w-4" />
                Available Actions:
              </div>
              <div className="flex items-center gap-2">
                {getAvailableActions()}
              </div>
            </div>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Details
              </TabsTrigger>
              <TabsTrigger value="workflow" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Workflow
              </TabsTrigger>
              <TabsTrigger value="items" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Items ({requisition.items?.length || 0})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6 mt-6">
              {/* Header Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Requisition Code</p>
                      <p className="text-sm text-gray-600">{requisition.code}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Requested By</p>
                      <p className="text-sm text-gray-600">
                        {requisition.requested_by_name || `User ID: ${requisition.requested_by}`}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Cost Center</p>
                      <p className="text-sm text-gray-600">
                        {requisition.cost_center_name || `Cost Center ID: ${requisition.cost_center}`}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Store</p>
                      <p className="text-sm text-gray-600">
                        {requisition.store_name || `Store ID: ${requisition.store}`}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Required By</p>
                      <p className="text-sm text-gray-600">
                        {format(new Date(requisition.required_by), "PPP")}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Created At</p>
                      <p className="text-sm text-gray-600">
                        {requisition.created_at ? format(new Date(requisition.created_at), "PPp") : "N/A"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Purpose */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Purpose
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700">{requisition.purpose}</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="workflow" className="space-y-6 mt-6">
              {/* Workflow Progress */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Workflow Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    {/* Progress Line */}
                    <div className="absolute top-6 left-6 right-6 h-1 bg-gray-200 rounded-full">
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-700"
                        style={{
                          width: requisition.status === "Draft" ? "0%" :
                                 requisition.status === "Submitted" ? "33%" :
                                 requisition.status === "Approved" ? "66%" :
                                 requisition.status === "Converted" ? "100%" :
                                 requisition.status === "Rejected" ? "33%" : "0%"
                        }}
                      />
                    </div>

                    {/* Status Steps */}
                    <div className="relative flex justify-between">
                      {["Draft", "Submitted", "Approved", "Converted"].map((status, index) => {
                        const isActive = requisition.status === status;
                        const isCompleted = ["Draft", "Submitted", "Approved", "Converted"].indexOf(requisition.status) > index;
                        const isRejected = requisition.status === "Rejected" && status === "Submitted";

                        return (
                          <div key={status} className="flex flex-col items-center">
                            <div
                              className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                                isActive
                                  ? "bg-blue-500 border-blue-500 text-white"
                                  : isCompleted
                                  ? "bg-green-500 border-green-500 text-white"
                                  : isRejected
                                  ? "bg-red-500 border-red-500 text-white"
                                  : "bg-white border-gray-300 text-gray-500"
                              }`}
                            >
                              {isCompleted || isActive ? (
                                isRejected ? <XCircle className="h-5 w-5" /> : <CheckCircle className="h-5 w-5" />
                              ) : (
                                <span className="text-sm font-medium">{index + 1}</span>
                              )}
                            </div>
                            <span className={`mt-2 text-xs font-medium ${
                              isActive ? "text-blue-600" : isCompleted ? "text-green-600" : isRejected ? "text-red-600" : "text-gray-500"
                            }`}>
                              {status}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Workflow History */}
              {(requisition.submitted_at || requisition.approved_at || requisition.rejected_at || requisition.converted_at) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Workflow History
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {requisition.submitted_at && (
                      <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <div>
                          <p className="text-sm font-medium text-blue-800">Submitted</p>
                          <p className="text-xs text-blue-600">
                            {format(new Date(requisition.submitted_at), "PPp")}
                          </p>
                        </div>
                      </div>
                    )}
                    {requisition.approved_at && (
                      <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div>
                          <p className="text-sm font-medium text-green-800">Approved</p>
                          <p className="text-xs text-green-600">
                            {format(new Date(requisition.approved_at), "PPp")}
                          </p>
                        </div>
                      </div>
                    )}
                    {requisition.rejected_at && (
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <div>
                          <p className="text-sm font-medium text-red-800">Rejected</p>
                          <p className="text-xs text-red-600">
                            {format(new Date(requisition.rejected_at), "PPp")}
                          </p>
                          {requisition.rejection_reason && (
                            <p className="text-xs text-red-700 mt-1 italic">
                              Reason: {requisition.rejection_reason}
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                    {requisition.converted_at && (
                      <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <div>
                          <p className="text-sm font-medium text-orange-800">Converted to Purchase Requisition</p>
                          <p className="text-xs text-orange-600">
                            {format(new Date(requisition.converted_at), "PPp")}
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="items" className="space-y-6 mt-6">
              {/* Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Items ({requisition.items?.length || 0})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {requisition.items && requisition.items.length > 0 ? (
                    <div className="space-y-4">
                      {requisition.items.map((item, index) => (
                        <div key={item.id || index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <p className="text-sm font-medium">Product</p>
                              <p className="text-sm text-gray-600">
                                {item.product_name || `Product ID: ${item.product}`}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Quantity</p>
                              <p className="text-sm text-gray-600">
                                {item.quantity} {item.unit_of_measure_symbol || item.unit_of_measure_name || `Unit ID: ${item.unit_of_measure}`}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Unit of Measure</p>
                              <p className="text-sm text-gray-600">
                                {item.unit_of_measure_name || `Unit ID: ${item.unit_of_measure}`}
                              </p>
                            </div>
                          </div>
                          {item.remarks && (
                            <div className="mt-3 pt-3 border-t">
                              <p className="text-sm font-medium">Remarks</p>
                              <p className="text-sm text-gray-600">{item.remarks}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Package className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-sm text-gray-500">No items found</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Reject Modal */}
      <RejectStoreRequisition
        isOpen={isRejectModalOpen}
        onClose={() => setIsRejectModalOpen(false)}
        requisitionId={requisition.id || null}
        requisitionNumber={requisition.code}
      />
    </>
  );
};

export default ViewStoreRequisition;
