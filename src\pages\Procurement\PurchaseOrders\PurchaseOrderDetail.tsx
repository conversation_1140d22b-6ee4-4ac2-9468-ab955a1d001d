import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import EditPurchaseOrder from "./modals/EditPurchaseOrder";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetPurchaseOrderQuery,
  useSubmitPurchaseOrderMutation,
  useApprovePurchaseOrderMutation,
  useCancelPurchaseOrderMutation,
  useGenerateGRNFromPurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useGetGRNsQuery,
} from "@/redux/slices/procurement";
import {
  ArrowLeft,
  Send,
  Edit,
  CheckCircle,
  XCircle,
  FileText,
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  Mail,
  Download,
  MapPin,
  CreditCard,
  DollarSign,
  Truck,
  Plus
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const PurchaseOrderDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [cancelReason, setCancelReason] = useState("");

  const { data: purchaseOrder, isLoading, error } = useGetPurchaseOrderQuery(id!);

  // Fetch related GRNs for this PO
  const { data: grnsData } = useGetGRNsQuery({
    purchase_order: id
  }, { skip: !id });

  const [submitPurchaseOrder, { isLoading: submitting }] = useSubmitPurchaseOrderMutation();
  const [approvePurchaseOrder, { isLoading: approving }] = useApprovePurchaseOrderMutation();
  const [cancelPurchaseOrder, { isLoading: cancelling }] = useCancelPurchaseOrderMutation();
  const [deletePurchaseOrder, { isLoading: deleting }] = useDeletePurchaseOrderMutation();
  const [generateGRN, { isLoading: generatingGRN }] = useGenerateGRNFromPurchaseOrderMutation();

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      Submitted: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Cancelled: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const handleSubmit = async () => {
    try {
      await submitPurchaseOrder(Number(id)).unwrap();
      toast.success("Purchase order submitted for approval");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit purchase order");
    }
  };

  const handleApprove = async () => {
    try {
      await approvePurchaseOrder(Number(id)).unwrap();
      toast.success("Purchase order approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve purchase order");
    }
  };

  const handleCancel = async () => {
    if (!cancelReason.trim()) {
      toast.error("Please provide a reason for cancellation");
      return;
    }

    try {
      await cancelPurchaseOrder({ id: Number(id), reason: cancelReason }).unwrap();
      toast.success("Purchase order cancelled successfully");
      setShowCancelDialog(false);
      setCancelReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to cancel purchase order");
    }
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this purchase order? This action cannot be undone.")) {
      return;
    }

    try {
      await deletePurchaseOrder(Number(id)).unwrap();
      toast.success("Purchase order deleted successfully");
      navigate("/procurement/purchase-orders");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete purchase order");
    }
  };

  const handleGenerateGRN = async () => {
    try {
      const payload = {
        id: Number(id),
        po_number: purchaseOrder.po_number,
        supplier: purchaseOrder.supplier,
        status: "Draft",
        delivery_location: purchaseOrder.delivery_location || "",
        payment_terms: purchaseOrder.payment_terms || "",
        delivery_date: purchaseOrder.delivery_date || new Date().toISOString().split('T')[0],
        created_by: purchaseOrder.created_by
      };

      const result = await generateGRN(payload).unwrap();
      toast.success(`GRN ${result.grn?.grn_number} created successfully`);
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to generate GRN");
    }
  };



  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Screen>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <Screen>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Purchase Order Not Found</h2>
          <p className="text-gray-600 mb-4">The purchase order you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => navigate("/procurement/purchase-orders")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Purchase Orders
          </Button>
        </div>
      </Screen>
    );
  }

  // Calculate totals from purchase order items
  const items = purchaseOrder.purchase_order_items || [];
  const subtotal = items.reduce((sum, item) => sum + parseFloat(item.total_price || "0"), 0);
  const totalTax = items.reduce((sum, item) => sum + parseFloat(item.tax_amount || "0"), 0);
  const totalValue = subtotal + totalTax;

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/procurement/purchase-orders")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                Purchase Order {purchaseOrder.po_number}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(purchaseOrder.status || "Draft")}
                <span className="text-gray-500">•</span>
                <span className="text-gray-600">
                  Created {new Date(purchaseOrder.created_at || "").toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {purchaseOrder.status === "Draft" && (
              <>
                <Button
                  variant="outline"
                  onClick={() => setShowEditModal(true)}
                  className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Details
                </Button>
                <Button onClick={handleSubmit} disabled={submitting}>
                  {submitting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="mr-2 h-4 w-4" />
                  )}
                  Submit
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={deleting}
                >
                  {deleting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <XCircle className="mr-2 h-4 w-4" />
                  )}
                  Delete
                </Button>
              </>
            )}

            {purchaseOrder.status === "Submitted" && (
              <>
                <Button onClick={handleApprove} disabled={approving}>
                  {approving ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  Approve
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowCancelDialog(true)}
                  disabled={cancelling}
                >
                  {cancelling ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <XCircle className="mr-2 h-4 w-4" />
                  )}
                  Cancel
                </Button>
              </>
            )}

            {purchaseOrder.status === "Approved" && (
              <>
                <Button
                  onClick={handleGenerateGRN}
                  disabled={generatingGRN}
                  className="bg-teal-600 hover:bg-teal-700"
                >
                  {generatingGRN ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Truck className="mr-2 h-4 w-4" />
                  )}
                  {generatingGRN ? "Generating GRN..." : "Generate GRN"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate("/procurement/grns")}
                  className="border-teal-200 text-teal-700 hover:bg-teal-50"
                >
                  <Package className="mr-2 h-4 w-4" />
                  View GRNs
                </Button>
              </>
            )}

            {(purchaseOrder.status === "Cancelled") && (
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleting}
              >
                {deleting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <XCircle className="mr-2 h-4 w-4" />
                )}
                Delete
              </Button>
            )}
          </div>
        </div>

        {/* Purchase Order Details */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Purchase Order Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">PO Number</Label>
                    <p className="font-medium">{purchaseOrder.po_number}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Status</Label>
                    <div className="mt-1">
                      {getStatusBadge(purchaseOrder.status || "Draft")}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Total Value</Label>
                    <p className="font-medium text-lg">
                      KES {totalValue.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Items Count</Label>
                    <p className="font-medium">{items.length} item(s)</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Items Table */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Order Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseOrder.purchase_order_items?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.product_name || item.product}</div>
                            <div className="text-xs text-gray-500">Code: {item.product}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.quantity}</div>
                            <div className="text-xs text-gray-500">
                              Received: {item.quantity_received} | Pending: {item.quantity_pending}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{item.unit_of_measure_name || `UOM ${item.unit_of_measure}`}</TableCell>
                        <TableCell>
                          KES {parseFloat(item.unit_price || "0").toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          <div>
                            <div>KES {parseFloat(item.total_price || "0").toLocaleString()}</div>
                            {item.tax_amount && parseFloat(item.tax_amount) > 0 && (
                              <div className="text-xs text-gray-500">
                                Tax: KES {parseFloat(item.tax_amount).toLocaleString()}
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Order Summary */}
                <Separator className="my-4" />
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span className="font-medium">
                      KES {subtotal.toLocaleString()}
                    </span>
                  </div>
                  {totalTax > 0 && (
                    <div className="flex justify-between">
                      <span>Tax:</span>
                      <span className="font-medium">
                        KES {totalTax.toLocaleString()}
                      </span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span>
                      KES {totalValue.toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Supplier Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Supplier Code</Label>
                  <p className="font-medium">{purchaseOrder.supplier}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created By</Label>
                  <p className="font-medium">{purchaseOrder.created_by}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created At</Label>
                  <p className="text-sm">
                    {new Date(purchaseOrder.created_at || "").toLocaleString()}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Delivery Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Delivery Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Delivery Location</Label>
                  <p className="font-medium">{purchaseOrder.delivery_location || "Not specified"}</p>
                </div>
                {purchaseOrder.delivery_address && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Delivery Address</Label>
                    <p className="text-sm">{purchaseOrder.delivery_address}</p>
                  </div>
                )}
                <div>
                  <Label className="text-sm font-medium text-gray-500">Expected Delivery Date</Label>
                  <p className="font-medium">
                    {purchaseOrder.delivery_date
                      ? new Date(purchaseOrder.delivery_date).toLocaleDateString()
                      : "Not specified"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Terms
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Payment Terms</Label>
                  <p className="font-medium">{purchaseOrder.payment_terms || "Not specified"}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Status</Label>
                  <div className="mt-1">
                    {getStatusBadge(purchaseOrder.status || "Draft")}
                  </div>
                </div>
                {purchaseOrder.requires_director_approval && (
                  <div className="p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-sm text-yellow-800 font-medium">
                      Requires Director Approval
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Creation Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Creation Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created By</Label>
                  <p className="font-medium">{purchaseOrder.created_by_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created Date</Label>
                  <p className="text-sm">
                    {new Date(purchaseOrder.created_at || "").toLocaleString()}
                  </p>
                </div>
                {purchaseOrder.approved_by_name && (
                  <>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Approved By</Label>
                      <p className="font-medium">{purchaseOrder.approved_by_name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Approved Date</Label>
                      <p className="text-sm">
                        {new Date(purchaseOrder.approved_at || "").toLocaleString()}
                      </p>
                    </div>
                  </>
                )}
                {purchaseOrder.sent_at && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Sent to Supplier</Label>
                    <p className="text-sm">
                      {new Date(purchaseOrder.sent_at).toLocaleString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Related GRNs */}
            {purchaseOrder.status === "Approved" && grnsData && grnsData.results && grnsData.results.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="h-5 w-5" />
                    Related GRNs
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {grnsData.results.map((grn: any) => (
                    <div key={grn.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{grn.grn_number}</p>
                        <p className="text-sm text-gray-600">
                          {new Date(grn.received_date).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={
                          grn.status === "Full" ? "bg-green-100 text-green-800 border-green-200" :
                            grn.status === "Partial" ? "bg-yellow-100 text-yellow-800 border-yellow-200" :
                              "bg-red-100 text-red-800 border-red-200"
                        }>
                          {grn.status}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => navigate(`/procurement/grns/${grn.id}`)}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Notes and Terms */}
        {(purchaseOrder.notes || purchaseOrder.terms_and_conditions) && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {purchaseOrder.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">{purchaseOrder.notes}</p>
                </CardContent>
              </Card>
            )}

            {purchaseOrder.terms_and_conditions && (
              <Card>
                <CardHeader>
                  <CardTitle>Terms and Conditions</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">{purchaseOrder.terms_and_conditions}</p>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Cancel Dialog */}
        <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-red-600">
                <XCircle className="h-5 w-5" />
                Cancel Purchase Order
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-800">
                  You are about to cancel Purchase Order{" "}
                  <span className="font-semibold">{purchaseOrder?.po_number}</span>
                </p>
              </div>
              <div>
                <Label htmlFor="cancel-reason">
                  Reason for Cancellation <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="cancel-reason"
                  placeholder="Please provide a reason for cancelling this purchase order..."
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  className="mt-1"
                  rows={4}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowCancelDialog(false);
                  setCancelReason("");
                }}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleCancel}
                disabled={cancelling || !cancelReason.trim()}
              >
                {cancelling ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <XCircle className="mr-2 h-4 w-4" />
                )}
                Cancel Purchase Order
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Purchase Order Modal */}
        <EditPurchaseOrder
          open={showEditModal}
          onClose={() => setShowEditModal(false)}
          purchaseOrder={purchaseOrder}
          onSuccess={() => setShowEditModal(false)}
        />

      </div>
    </Screen>
  );
};

export default PurchaseOrderDetail;
