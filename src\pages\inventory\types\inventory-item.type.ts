// Define the base types
interface Product {
  id: number;
  name: string;
  // Add other product fields as needed
}

interface Store {
  id: number;
  name: string;
  // Add other store fields as needed
}

interface Branch {
  id: number;
  name: string;
  // Add other branch fields as needed
}

export interface InventoryItem {
  id: number;
  name: string;
  quantity_available: number;
  reorder_level: number;
  last_updated: string; // ISO date string
  expiry_date: string | null; // nullable date
  product: string;
  store: string;
  branch: string;
}

// API response structure
export interface InventoryApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: InventoryItem[];
}

export interface CreateInventoryItemRequest {
  quantity_available: number;
  reorder_level: number;
  expiry_date?: string | null;
  product: string;
  store: string;
  branch: string;
}