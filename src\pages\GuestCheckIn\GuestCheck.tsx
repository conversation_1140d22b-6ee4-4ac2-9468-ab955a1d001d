import React, { useState, useMemo } from 'react';
import { AlertCircle, Plus, Search } from 'lucide-react';
import { CheckInForm, GuestCheck } from './Components/types/types';
import { CheckDetailsDialog } from './Components/CheckDetailsModal';
import { CheckInDialog } from './Components/CheckInDialogue';

import { useGetGuestChecksQuery, useAddGuestCheckMutation, usePatchGuestMutation } from '@/redux/slices/guestCheck';
import { useGetUsersQuery } from '@/redux/slices/users';
import { useGetTablesQuery } from '@/redux/slices/tables';
import { Screen } from '@/app-components/layout/screen';
import LinkCheckDialog from './Components/SplitCheckDialogue';
import { CheckCard } from './Components/CheckCard';
 // Import the updated CheckCard component

// Interfaces for type safety
interface User {
  id: string | number;
  username?: string;
}

interface Table {
  id: string | number;
  number?: string | number;
}

interface ApiGuestCheck {
  id: number;
  check_number: string;
  guest_count: number;
  opened_at: string;
  closed_at: string | null;
  employee: string;
  table_number: number;
  status: string;
  payment_status: string;
  sub_total: string;
  tax_total: string;
  service_charge_total: string;
  discount_total: string;
  grand_total: string;
  order: number;
  linked_checks: any[];
}

// Map API response to GuestCheck format
const mapApiCheckToComponentFormat = (apiCheck: ApiGuestCheck): GuestCheck => ({
  id: apiCheck.check_number,
  databaseId: apiCheck.id,
  tableNumber: apiCheck.table_number.toString(),
  guestCount: apiCheck.guest_count,
  waiterName: apiCheck.employee || 'Unknown',
  orderTime: apiCheck.opened_at,
  status: apiCheck.status.toLowerCase() === 'open' ? 'active' : 'closed',
  paymentStatus: apiCheck.payment_status,
  subtotal: parseFloat(apiCheck.sub_total) || 0,
  tax: parseFloat(apiCheck.tax_total) || 0,
  serviceCharge: parseFloat(apiCheck.service_charge_total) || 0,
  total: parseFloat(apiCheck.grand_total) || 0,
  discountTotal: parseFloat(apiCheck.discount_total) || 0,
  closedAt: apiCheck.closed_at,
  items: [],
  discounts: [],
  voids: [],
  order: apiCheck.order,
  linkedChecks: apiCheck.linked_checks || [],
});

// Extract guest checks from API response
const extractGuestChecksFromResponse = (data: any): GuestCheck[] => {
  if (!data) {
    console.warn('No guest checks data received');
    return [];
  }
  const apiChecks = data.data?.results || data.results || data.checks || data || [];
  if (!Array.isArray(apiChecks)) {
    console.warn('Invalid guest checks data format:', data);
    return [];
  }
  return apiChecks.map(mapApiCheckToComponentFormat);
};

// Extract users from API response
const extractUsersFromResponse = (data: any): User[] => {
  return data?.data?.results || data?.results || data?.users || data || [];
};

// Extract tables from API response
const extractTablesFromResponse = (data: any): Table[] => {
  return data?.data?.results || data?.results || data || [];
};

// Format currency in KES
const formatKES = (value: number): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 2,
  }).format(isNaN(value) ? 0 : value);
};

const GuestChecksUI: React.FC = () => {
  const { data: guestChecksData, isLoading, isError, error } = useGetGuestChecksQuery({});
  const { data: tablesData } = useGetTablesQuery({});
  const { data: usersData } = useGetUsersQuery({});
  const [addGuestCheck] = useAddGuestCheckMutation();
  const [patchGuestCheck] = usePatchGuestMutation({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCheck, setSelectedCheck] = useState<GuestCheck | null>(null);
  const [showCheckInDialog, setShowCheckInDialog] = useState(false);
  const [showCheckDetails, setShowCheckDetails] = useState(false);
  const [showLinkCheckDialog, setShowLinkCheckDialog] = useState(false);
  const [checkToLink, setCheckToLink] = useState<GuestCheck | null>(null);
  const [selectedCheckToLink, setSelectedCheckToLink] = useState('');
  const [checkInForm, setCheckInForm] = useState<CheckInForm>({
    guestCount: '',
    specialRequests: '',
    order: '',
  });

  // Process API data
  const processedData = useMemo(() => {
    const guestChecks = extractGuestChecksFromResponse(guestChecksData);
    const users = extractUsersFromResponse(usersData);
    const tables = extractTablesFromResponse(tablesData);
    const guestCheckError = isError
      ? error && 'status' in error
        ? `API Error ${error.status}: ${(error as any).data?.message || 'Unknown error'}`
        : (error as any)?.message || 'Network error'
      : !isLoading && guestChecks.length === 0
      ? 'No guest checks found'
      : null;

    return { guestChecks, users, tables, guestCheckError };
  }, [guestChecksData, isError, error, usersData, tablesData, isLoading]);

  // Filter checks based on search
  const filteredChecks = useMemo(() => {
    return processedData.guestChecks.filter((check) =>
      searchTerm
        ? check.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          check.tableNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
          check.waiterName.toLowerCase().includes(searchTerm.toLowerCase())
        : true
    );
  }, [processedData.guestChecks, searchTerm]);

  // Calculate stats
  const stats = useMemo(() => {
    const activeChecks = filteredChecks.filter((check) => check.status === 'active').length;
    const closedChecks = filteredChecks.filter((check) => check.status === 'closed').length;
    const totalRevenue = filteredChecks.reduce((sum, check) => sum + (check.total || 0), 0);
    const avgCheckSize = filteredChecks.length > 0 ? totalRevenue / filteredChecks.length : 0;
    return { activeChecks, closedChecks, totalRevenue, avgCheckSize };
  }, [filteredChecks]);

  // Handle check closure
  const handleCloseCheck = async (checkId: string) => {
    const checkToClose = processedData.guestChecks.find((check) => check.id === checkId);
    if (!checkToClose) return;
    try {
      await patchGuestCheck({
        id: checkToClose.databaseId,
        data: { status: 'Closed', closed_at: new Date().toISOString() },
      }).unwrap();
    } catch (error: any) {
      console.error('Failed to close check:', error?.data?.message || error?.message || 'Unknown error');
    }
  };

  // Handle linking checks
  const handleLinkCheck = (check: GuestCheck) => {
    setCheckToLink(check);
    setShowLinkCheckDialog(true);
  };

  const handleConfirmLinkCheck = async () => {
    if (!checkToLink || !selectedCheckToLink) return;
    const targetCheck = processedData.guestChecks.find((check) => check.id === selectedCheckToLink);
    if (!targetCheck) return;
    try {
      await patchGuestCheck({
        id: checkToLink.databaseId,
        data: {
          linked_checks: [
            ...checkToLink.linkedChecks,
            { id: targetCheck.databaseId, check_number: targetCheck.id, table_number: targetCheck.tableNumber, linked_at: new Date().toISOString() },
          ],
        },
      }).unwrap();
      setShowLinkCheckDialog(false);
      setCheckToLink(null);
      setSelectedCheckToLink('');
    } catch (error: any) {
      console.error('Failed to link check:', error?.data?.message || error?.message || 'Unknown error');
    }
  };

  // Handle check-in
  const handleCheckIn = async () => {
    try {
      await addGuestCheck({
        guest_count: parseInt(checkInForm.guestCount) || 1,
        opened_at: new Date().toISOString(),
        status: 'Open',
        payment_status: 'Unpaid',
        sub_total: '0.00',
        tax_total: '0.00',
        service_charge_total: '0.00',
        discount_total: '0.00',
        grand_total: '0.00',
        table_number: 1,
        employee: 'EMP001',
        linked_checks: [],
        ...(checkInForm.order && { order: parseInt(checkInForm.order) }),
      }).unwrap();
      setCheckInForm({ guestCount: '', specialRequests: '', order: '' });
      setShowCheckInDialog(false);
    } catch (error: any) {
      console.error('Failed to add guest check:', error?.data?.message || error?.message || 'Unknown error');
    }
  };

  return (
    <Screen>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="mb-6 flex justify-between items-center">
          <h1 className="text-2xl font-bold">Guest Checks (KES)</h1>
          <button
            onClick={() => setShowCheckInDialog(true)}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
          >
            <Plus className="h-4 w-4 inline mr-2" /> New Check-In
          </button>
        </div>

        {isLoading && (
          <div className="p-4 border border-gray-300 rounded-lg">
            <p>Loading guest checks...</p>
          </div>
        )}

        {processedData.guestCheckError && !isLoading && (
          <div className="p-4 border border-red-300 rounded-lg flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <p className="text-red-600">{processedData.guestCheckError}</p>
          </div>
        )}

        {!isLoading && !isError && processedData.guestChecks.length === 0 && (
          <div className="p-6 border border-gray-300 rounded-lg text-center">
            <h3 className="text-lg font-semibold">No Guest Checks Yet</h3>
            <p className="text-gray-600">Start by checking in your first table.</p>
            <button
              onClick={() => setShowCheckInDialog(true)}
              className="mt-4 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
            >
              Check In First Guest
            </button>
          </div>
        )}

        <div className="mb-6 flex items-center border border-gray-300 rounded-lg p-2">
          <Search className="h-5 w-5 text-gray-500 mr-2" />
          <input
            type="text"
            placeholder="Search checks, tables, or waiters..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 bg-transparent outline-none"
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="p-4 border border-gray-300 rounded-lg">
            <p className="text-sm text-gray-600">Active Checks</p>
            <p className="text-xl font-bold">{stats.activeChecks}</p>
          </div>
          <div className="p-4 border border-gray-300 rounded-lg">
            <p className="text-sm text-gray-600">Completed Today</p>
            <p className="text-xl font-bold">{stats.closedChecks}</p>
          </div>
          <div className="p-4 border border-gray-300 rounded-lg">
            <p className="text-sm text-gray-600">Total Revenue</p>
            <p className="text-xl font-bold">{formatKES(stats.totalRevenue)}</p>
          </div>
          <div className="p-4 border border-gray-300 rounded-lg">
            <p className="text-sm text-gray-600">Average Check</p>
            <p className="text-xl font-bold">{formatKES(stats.avgCheckSize)}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredChecks.map((check) => (
            <CheckCard
              key={check.id}
              check={check}
              onCloseCheck={check.status === 'active' ? handleCloseCheck : undefined}
              onSplitCheck={() => handleLinkCheck(check)}
              onViewCheck={() => {
                setSelectedCheck(check);
                setShowCheckDetails(true);
              }}
            />
          ))}
        </div>

        <CheckInDialog
          open={showCheckInDialog}
          onOpenChange={setShowCheckInDialog}
          checkInForm={checkInForm}
          setCheckInForm={setCheckInForm}
          onCheckIn={handleCheckIn}
        />

        <CheckDetailsDialog
          open={showCheckDetails}
          onOpenChange={setShowCheckDetails}
          selectedCheck={selectedCheck}
        />

        <LinkCheckDialog
          open={showLinkCheckDialog}
          onOpenChange={setShowLinkCheckDialog}
          checkToLink={checkToLink}
          availableChecks={processedData.guestChecks}
          selectedCheckToLink={selectedCheckToLink}
          setSelectedCheckToLink={setSelectedCheckToLink}
          onConfirm={handleConfirmLinkCheck}
        />
      </div>
    </Screen>
  );
};

export default GuestChecksUI;