import { CreateRecipeVersionRequest, RecipeVersion, RecipeVersionApiResponse } from "@/pages/inventory/types/recipe-version.type";
import { apiSlice } from "../apiSlice";

export const recipeVersionApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getRecipeVersion: builder.query<RecipeVersionApiResponse, { params: {} }>({
            query: (params) => ({
                url: `/menu/recipe-versions`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<RecipeVersionApiResponse>) => response.data,
            providesTags: ["/menu/recipe-versions"],
        }),
        updateRecipeVersion: builder.mutation<RecipeVersion, { id: number; body: Partial<CreateRecipeVersionRequest> }>({
            query: ({ id, body }) => ({
                url: `/menu/recipe-versions/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/menu/recipe-versions"],
        }),
        createRecipeVersion: builder.mutation<RecipeVersion, CreateRecipeVersionRequest>({
            query: (body) => ({
                url: `/menu/recipe-versions`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/menu/recipe-versions"],
        }),
        getOneRecipeVersion: builder.query<RecipeVersion, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/menu/recipe-versions/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/menu/recipe-versions"],
        }),
        deleteRecipeVersion: builder.mutation<void, number>({
            query: (id) => ({
                url: `/menu/recipe-versions/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/menu/recipe-versions"],
        }),
    })
})

export const {
    useCreateRecipeVersionMutation,
    useGetRecipeVersionQuery,
    useUpdateRecipeVersionMutation,
    useGetOneRecipeVersionQuery,
    useDeleteRecipeVersionMutation,
} = recipeVersionApiSlice

interface ApiResponse<T> {
    data: T;
    message: string;
}