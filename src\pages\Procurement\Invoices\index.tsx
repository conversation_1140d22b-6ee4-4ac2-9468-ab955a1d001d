import { Screen } from "@/app-components/layout/screen";
import AddInvoice from "./modals/AddInvoice";
import EditInvoice from "./modals/EditInvoice";
import { useState, useEffect } from "react";
import {
  useGetInvoicesQuery,
  useDeleteInvoiceMutation,
  useSubmitInvoiceMutation,
  useReviewInvoiceMutation,
  useApproveInvoiceMutation,
  useCreatePaymentVoucherMutation,
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { Invoice } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  Plus, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2, 
  FileText, 
  Calendar,
  User,
  Building,
  Loader2,
  Search,
  Filter,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Send,
  DollarSign,
  Receipt
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { paymentVoucherIntegrationHooks } from "@/utils/paymentVoucherIntegration";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const InvoicesIndex = () => {
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState<Invoice | null>(null);
  
  // Search and filter states
  const [searchInput, setSearchInput] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchTerm(searchInput);
      setCurrentPage(1);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchInput]);

  // Mock data for development
  const mockInvoicesData = {
    current_page: 1,
    last_page: 3,
    per_page: 20,
    total_data: 45,
    results: [
      {
        id: 1,
        invoice_number: "INV-2025-001",
        invoice_date: "2025-01-15",
        supplier: 1,
        supplier_name: "ABC Suppliers Ltd",
        grn: 1,
        grn_number: "GRN-2025-001",
        status: "Draft",
        total_amount: "1500.00",
        currency: "USD",
        gl_account: 1,
        gl_account_name: "Food Supplies",
        created_by_name: "John Doe",
        created_at: "2025-01-15T10:00:00Z",
      },
      {
        id: 2,
        invoice_number: "INV-2025-002",
        invoice_date: "2025-01-16",
        supplier: 2,
        supplier_name: "XYZ Trading Co",
        grn: 2,
        grn_number: "GRN-2025-002",
        status: "Reviewed",
        total_amount: "2300.50",
        currency: "USD",
        gl_account: 2,
        gl_account_name: "Office Supplies",
        created_by_name: "Jane Smith",
        created_at: "2025-01-16T14:30:00Z",
      },
      {
        id: 3,
        invoice_number: "INV-2025-003",
        invoice_date: "2025-01-17",
        supplier: 3,
        supplier_name: "Tech Solutions Inc",
        grn: 3,
        grn_number: "GRN-2025-003",
        status: "Approved",
        total_amount: "5750.00",
        currency: "USD",
        gl_account: 3,
        gl_account_name: "Equipment",
        created_by_name: "Mike Johnson",
        created_at: "2025-01-17T09:15:00Z",
        payment_voucher: 1,
        payment_voucher_number: "PV-2025-001",
      },
      {
        id: 4,
        invoice_number: "INV-2025-004",
        invoice_date: "2025-01-18",
        supplier: 1,
        supplier_name: "ABC Suppliers Ltd",
        grn: 4,
        grn_number: "GRN-2025-004",
        status: "Draft",
        total_amount: "890.25",
        currency: "USD",
        gl_account: 1,
        gl_account_name: "Food Supplies",
        created_by_name: "Sarah Wilson",
        created_at: "2025-01-18T11:45:00Z",
      },
      {
        id: 5,
        invoice_number: "INV-2025-005",
        invoice_date: "2025-01-18",
        supplier: 4,
        supplier_name: "Global Imports",
        grn: 5,
        grn_number: "GRN-2025-005",
        status: "Approved",
        total_amount: "3200.00",
        currency: "USD",
        gl_account: 4,
        gl_account_name: "Raw Materials",
        created_by_name: "David Brown",
        created_at: "2025-01-18T16:20:00Z",
      },
    ]
  };

  // Filter mock data based on search and status
  const filteredResults = mockInvoicesData.results.filter(invoice => {
    const matchesSearch = !searchTerm ||
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.supplier_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.grn_number.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || invoice.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const invoicesData = {
    ...mockInvoicesData,
    results: filteredResults,
    total_data: filteredResults.length,
  };

  const isLoading = false;
  const error = null;
  const refetch = () => console.log("Refetch called");

  // Mock mutation hooks
  const [deleting, setDeleting] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [reviewing, setReviewing] = useState(false);
  const [approving, setApproving] = useState(false);
  const [creatingVoucher, setCreatingVoucher] = useState(false);

  const deleteInvoice = async (id: number) => {
    setDeleting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setDeleting(false);
    return { unwrap: () => Promise.resolve() };
  };

  const submitInvoice = async (id: number) => {
    setSubmitting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSubmitting(false);
    return { unwrap: () => Promise.resolve() };
  };

  const reviewInvoice = async (payload: any) => {
    setReviewing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setReviewing(false);
    return { unwrap: () => Promise.resolve() };
  };

  const approveInvoice = async (id: number) => {
    setApproving(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setApproving(false);
    return { unwrap: () => Promise.resolve() };
  };

  const createPaymentVoucher = async (id: number) => {
    setCreatingVoucher(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setCreatingVoucher(false);
    return {
      unwrap: () => Promise.resolve({
        voucher_number: `PV-2025-${String(Date.now()).slice(-3)}`
      })
    };
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchInput(value);
  };

  // Handle status filter
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!invoiceToDelete) return;

    try {
      await deleteInvoice(invoiceToDelete.id!);
      toast.success("Invoice deleted successfully");
      setDeleteDialogOpen(false);
      setInvoiceToDelete(null);
      refetch();
    } catch (error: any) {
      toast.error("Failed to delete invoice");
    }
  };

  // Handle edit
  const handleEdit = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setEditModalOpen(true);
  };

  // Handle workflow actions
  const handleSubmit = async (invoice: Invoice) => {
    try {
      await submitInvoice(invoice.id!);
      toast.success("Invoice submitted for review");
      refetch();
    } catch (error: any) {
      toast.error("Failed to submit invoice");
    }
  };

  const handleApprove = async (invoice: Invoice) => {
    try {
      await approveInvoice(invoice.id!);
      toast.success("Invoice approved successfully");
      refetch();
    } catch (error: any) {
      toast.error("Failed to approve invoice");
    }
  };

  const handleCreateVoucher = async (invoice: Invoice) => {
    try {
      // Use the integration hook for validation and creation
      const result = await paymentVoucherIntegrationHooks.onCreatePaymentVoucher(invoice);

      // Also call the mock API endpoint
      const apiResult = await createPaymentVoucher(invoice.id!);
      const voucherResult = await apiResult.unwrap();

      toast.success(`Payment voucher ${voucherResult.voucher_number} created successfully`);
      refetch();
    } catch (error: any) {
      toast.error(error?.message || "Failed to create payment voucher");
    }
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case "Approved":
          return "bg-green-100 text-green-800 border-green-200";
        case "Reviewed":
          return "bg-blue-100 text-blue-800 border-blue-200";
        case "Draft":
          return "bg-yellow-100 text-yellow-800 border-yellow-200";
        default:
          return "bg-gray-100 text-gray-800 border-gray-200";
      }
    };

    const getStatusIcon = (status: string) => {
      switch (status) {
        case "Approved":
          return <CheckCircle className="h-3 w-3 mr-1" />;
        case "Reviewed":
          return <Clock className="h-3 w-3 mr-1" />;
        case "Draft":
          return <Edit className="h-3 w-3 mr-1" />;
        default:
          return null;
      }
    };

    return (
      <Badge className={`${getStatusColor(status)} border flex items-center`}>
        {getStatusIcon(status)}
        {status}
      </Badge>
    );
  };

  // Table columns
  const columns: ColumnDef<Invoice>[] = [
    {
      accessorKey: "invoice_number",
      header: "Invoice Number",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-blue-600" />
          <Link 
            to={`/procurement/invoices/${row.original.id}`}
            className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
          >
            {row.getValue("invoice_number")}
          </Link>
        </div>
      ),
    },
    {
      accessorKey: "grn_number",
      header: "GRN Number",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Receipt className="h-4 w-4 text-purple-600" />
          <Link 
            to={`/procurement/grns/${row.original.grn}`}
            className="font-medium text-purple-600 hover:text-purple-800 hover:underline"
          >
            {row.original.grn_number || `GRN-${row.original.grn}`}
          </Link>
        </div>
      ),
    },
    {
      accessorKey: "supplier_name",
      header: "Supplier",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Building className="h-4 w-4 text-gray-600" />
          <span>{row.original.supplier_name || "N/A"}</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => <StatusBadge status={row.getValue("status")} />,
    },
    {
      accessorKey: "total_amount",
      header: "Total Amount",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-green-600" />
          <span className="font-medium">
            {row.original.currency} {parseFloat(row.getValue("total_amount")).toLocaleString()}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "invoice_date",
      header: "Invoice Date",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-600" />
          <span>
            {new Date(row.getValue("invoice_date")).toLocaleDateString()}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "created_by_name",
      header: "Created By",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-gray-600" />
          <span>{row.original.created_by_name || "N/A"}</span>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const invoice = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/invoices/${invoice.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              
              {invoice.status === "Draft" && (
                <>
                  <DropdownMenuItem onClick={() => handleEdit(invoice)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSubmit(invoice)}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit for Review
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => {
                      setInvoiceToDelete(invoice);
                      setDeleteDialogOpen(true);
                    }}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}

              {invoice.status === "Reviewed" && (
                <>
                  <DropdownMenuItem onClick={() => handleApprove(invoice)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleEdit(invoice)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                </>
              )}

              {invoice.status === "Approved" && !invoice.payment_voucher && (
                <DropdownMenuItem onClick={() => handleCreateVoucher(invoice)}>
                  <DollarSign className="mr-2 h-4 w-4" />
                  Create Payment Voucher
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  if (error) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Failed to load invoices</p>
            <Button onClick={() => refetch()}>Try Again</Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Purchase Invoices
            </h1>
            <p className="text-gray-600 mt-1">
              Manage supplier invoices and link them to GRNs
            </p>
            {invoicesData && (
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                <span>Total: {invoicesData.total_data || 0}</span>
                <span>•</span>
                <span>Page {invoicesData.current_page || 1} of {invoicesData.last_page || 1}</span>
              </div>
            )}
          </div>
          <Button onClick={() => setAddModalOpen(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Invoice
          </Button>
        </div>

        {/* Quick Stats */}
        {invoicesData && invoicesData.results && invoicesData.results.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-600">
                    {invoicesData.total_data || 0}
                  </p>
                  <p className="text-sm text-gray-600">Total Invoices</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Edit className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-yellow-600">
                    {invoicesData.results.filter((inv: any) => inv.status === "Draft").length}
                  </p>
                  <p className="text-sm text-gray-600">Draft</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Clock className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-600">
                    {invoicesData.results.filter((inv: any) => inv.status === "Reviewed").length}
                  </p>
                  <p className="text-sm text-gray-600">Under Review</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">
                    {invoicesData.results.filter((inv: any) => inv.status === "Approved").length}
                  </p>
                  <p className="text-sm text-gray-600">Approved</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="flex items-center gap-4 bg-white p-4 rounded-lg border">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by invoice number, supplier, or GRN..."
                className="pl-10"
                value={searchInput}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-600" />
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
                <SelectItem value="Reviewed">Reviewed</SelectItem>
                <SelectItem value="Approved">Approved</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>

        {/* Data Table */}
        <div className="bg-white rounded-lg border">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={invoicesData?.results || []}
              pagination={{
                currentPage,
                totalPages: invoicesData?.last_page || 1,
                pageSize,
                totalItems: invoicesData?.total_data || 0,
                onPageChange: setCurrentPage,
                onPageSizeChange: setPageSize,
              }}
            />
          )}
        </div>
      </div>

      {/* Modals */}
      <AddInvoice 
        open={addModalOpen} 
        onClose={() => setAddModalOpen(false)} 
      />
      
      <EditInvoice 
        open={editModalOpen} 
        onClose={() => {
          setEditModalOpen(false);
          setSelectedInvoice(null);
        }}
        invoice={selectedInvoice}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Invoice</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete invoice "{invoiceToDelete?.invoice_number}"? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Screen>
  );
};

export default InvoicesIndex;
