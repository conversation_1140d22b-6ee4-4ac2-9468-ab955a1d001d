import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import {
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  MoreHorizontal,
  Printer as PrinterIcon,
  Building2,
  Store,

  Wifi,
  WifiOff,
  TestTube,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PrinterType as PrinterTypeEnum, PrinterInterfaceType } from '@/types/pos';
import { Printer } from '@/redux/slices/printers';
import { Screen } from '@/app-components/layout/screen';
import {
  useGetPrintersQuery,
  useDeletePrinterMutation,
  usePatchPrinterMutation
} from '@/redux/slices/printers';

import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { useEffect } from 'react';

const PrinterManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');
  const [selectedType, setSelectedType] = useState('all');

  // API hooks
  const { data: printers = [], isLoading: loadingPrinters, error: printersError, refetch: refetchPrinters } = useGetPrintersQuery({});
  const [deletePrinter] = useDeletePrinterMutation();
  const [patchPrinter] = usePatchPrinterMutation();

  // Handle API errors
  useEffect(() => {
    if (printersError) {
      handleApiError(printersError, 'load printers');
    }
  }, [printersError]);

  const handleDeletePrinter = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this printer?')) {
      try {
        await deletePrinter(id).unwrap();
        handleApiSuccess('Printer deleted successfully!');
      } catch (error: any) {
        handleApiError(error, 'delete printer');
      }
    }
  };

  const handleRefresh = () => {
    console.log('Manually refreshing printers data...');
    refetchPrinters();
  };

  const handleToggleActivation = async (printerId: string, currentStatus: boolean) => {
    if (!printerId) return;

    const action = currentStatus ? 'deactivate' : 'activate';
    if (window.confirm(`Are you sure you want to ${action} this printer?`)) {
      try {
        await patchPrinter({
          id: printerId,
          data: { is_active: !currentStatus }
        }).unwrap();
        handleApiSuccess(`Printer ${action}d successfully`);
        refetchPrinters();
      } catch (error) {
        handleApiError(error, `${action} printer`);
      }
    }
  };

  const handleToggleBackup = async (printerId: string, currentStatus: boolean) => {
    if (!printerId) return;

    const action = currentStatus ? 'Set as Primary' : 'Set as Backup';
    if (window.confirm(`Are you sure you want to ${action.toLowerCase()}?`)) {
      try {
        await patchPrinter({
          id: printerId,
          data: { is_backup_printer: !currentStatus }
        }).unwrap();
        handleApiSuccess(`Printer ${action.toLowerCase()} successfully`);
        refetchPrinters();
      } catch (error) {
        handleApiError(error, `update printer status`);
      }
    }
  };

  const columns: ColumnDef<Printer>[] = [
    {
      accessorKey: 'name',
      header: 'Printer Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <PrinterIcon className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.printer_code}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'printer_purpose',
      header: 'Type',
      cell: ({ row }) => {
        const type = row.getValue('printer_purpose') as string;
        const getTypeColor = (type: string) => {
          switch (type) {
            case 'kitchen': return 'bg-orange-100 text-orange-800';
            case 'receipt': return 'bg-blue-100 text-blue-800';
            case 'bill': return 'bg-green-100 text-green-800';
            case 'bar': return 'bg-purple-100 text-purple-800';
            default: return 'bg-gray-100 text-gray-800';
          }
        };

        return (
          <Badge variant="outline" className={getTypeColor(type)}>
            {type?.toUpperCase() || 'UNKNOWN'}
          </Badge>
        );
      },
    },

    {
      accessorKey: 'interface_type',
      header: 'Interface',
      cell: ({ row }) => {
        const interfaceType = row.getValue('interface_type') as PrinterInterfaceType;
        const ipAddress = row.original.ip_address;

        return (
          <div className="flex items-center space-x-2">
            <Wifi className="h-4 w-4 text-green-500" />
            <div>
              <div className="font-medium">{interfaceType}</div>
              {ipAddress && (
                <div className="text-sm text-muted-foreground font-mono">{ipAddress}</div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'location_note',
      header: 'Location',
      cell: ({ row }) => {
        const location = row.getValue('location_note') as string;
        return (
          <div className="text-sm">
            {location || 'Not specified'}
          </div>
        );
      },
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.original.is_active ?? true;
        const isBackup = row.original.is_backup_printer ?? false;
        return (
          <div className="flex gap-1">
            <Badge variant={isActive ? 'default' : 'secondary'}>
              {isActive ? 'Active' : 'Inactive'}
            </Badge>
            {isBackup && (
              <Badge variant="outline">Backup</Badge>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const printer = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/printers/${printer.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Configuration
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/printers/${printer.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Printer
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <TestTube className="h-4 w-4 mr-2" />
                Test Print
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Configure Routing
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleToggleActivation(printer.id?.toString() || '', printer.is_active ?? true)}
              >
                {printer.is_active ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleToggleBackup(printer.id?.toString() || '', printer.is_backup_printer ?? false)}
              >
                {printer.is_backup_printer ? 'Set as Primary' : 'Set as Backup'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeletePrinter(printer.id?.toString() || '')}
              >
                Delete Printer
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredPrinters = Array.isArray(printers) ? printers.filter(printer => {
    const matchesSearch = printer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         printer.printer_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         printer.ip_address?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || printer.printer_purpose === selectedType;
    return matchesSearch && matchesType;
  }) : [];

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Printer Management</h1>
          <p className="text-muted-foreground">
            Manage thermal and receipt printers for workstations and kitchen displays
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loadingPrinters}>
            🔄 Refresh
          </Button>
          <Link to="/pos/printers/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Printer
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find printers by name, device, or network configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search printers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {Object.values(PrinterTypeEnum).map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Printers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Printers ({filteredPrinters.length})</CardTitle>
          <CardDescription>
            Thermal and receipt printers across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingPrinters ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-muted-foreground">Loading printers...</div>
            </div>
          ) : printersError ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-red-600">Error loading printers. Please try again.</div>
            </div>
          ) : (
            <DataTable
              data={filteredPrinters}
              columns={columns}
              enablePagination={true}
              enableSorting={true}
              enableColumnFilters={true}
              enableSelectColumn={false}
            />
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Printers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Array.isArray(printers) ? printers.length : 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Printers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Array.isArray(printers) ? printers.filter(p => p.is_active).length : 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Kitchen Printers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(printers) ? printers.filter(p => p.printer_purpose === 'kitchen').length : 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Receipt Printers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(printers) ? printers.filter(p => p.printer_purpose === 'receipt').length : 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">LAN Connected</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(printers) ? printers.filter(p => p.interface_type === 'LAN').length : 0}
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default PrinterManagement;
