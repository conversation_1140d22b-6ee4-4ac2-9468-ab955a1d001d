import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Loader2, Users, Package, Edit } from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import {
  useUpdateBidAnalysisMutation,
  useUpdateBidAnalysisLineMutation,
  useGetBidAnalysisQuery,
  useGetSuppliersQuery,
  useGeneratePurchaseOrderFromBidAnalysisMutation,
} from "@/redux/slices/procurement";

interface EditBidAnalysisMultipleProps {
  open: boolean;
  onClose: () => void;
  bidAnalysis: any;
  onSuccess?: () => void;
}

interface BidLineFormData {
  id: number;
  supplier: string;
  unit_price: string;
  quantity_awarded: string;
  delivery_time_days: string;
  total_price: string;
}

interface MultipleBidAnalysisFormData {
  recommendation_notes: string;
  finalized_at: string;
  bid_lines: BidLineFormData[];
}

const EditBidAnalysisMultiple: React.FC<EditBidAnalysisMultipleProps> = ({
  open,
  onClose,
  bidAnalysis,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<MultipleBidAnalysisFormData>({
    recommendation_notes: "",
    finalized_at: "",
    bid_lines: [],
  });

  // API hooks
  const [updateBidAnalysis, { isLoading: isUpdatingAnalysis }] = useUpdateBidAnalysisMutation();
  const [updateBidAnalysisLine, { isLoading: isUpdatingLine }] = useUpdateBidAnalysisLineMutation();
  const [generatePurchaseOrder, { isLoading: generatingPO }] = useGeneratePurchaseOrderFromBidAnalysisMutation();
  const { data: bidAnalysisDetail } = useGetBidAnalysisQuery(bidAnalysis?.id, {
    skip: !bidAnalysis?.id
  });
  const { data: suppliersData } = useGetSuppliersQuery({});

  // Initialize form data when bidAnalysis changes
  useEffect(() => {
    if (bidAnalysisDetail) {
      setFormData({
        recommendation_notes: bidAnalysisDetail.recommendation_notes || "",
        finalized_at: bidAnalysisDetail.finalized_at || "",
        bid_lines: bidAnalysisDetail.bid_lines?.map((line: any) => ({
          id: line.id,
          supplier: line.supplier || "",
          unit_price: line.unit_price?.toString() || "",
          quantity_awarded: line.quantity_awarded?.toString() || "",
          delivery_time_days: line.delivery_time_days?.toString() || "",
          total_price: line.total_price?.toString() || "",
        })) || [],
      });
    }
  }, [bidAnalysisDetail]);

  const handleInputChange = (field: keyof MultipleBidAnalysisFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleBidLineChange = (lineIndex: number, field: keyof BidLineFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      bid_lines: prev.bid_lines.map((line, index) =>
        index === lineIndex ? { ...line, [field]: value } : line
      )
    }));

    // Auto-calculate total price when unit_price or quantity_awarded changes
    if (field === 'unit_price' || field === 'quantity_awarded') {
      const updatedLines = [...formData.bid_lines];
      const line = updatedLines[lineIndex];

      if (field === 'unit_price') {
        line.unit_price = value;
      } else {
        line.quantity_awarded = value;
      }

      const unitPrice = parseFloat(line.unit_price) || 0;
      const quantity = parseFloat(line.quantity_awarded) || 0;
      line.total_price = (unitPrice * quantity).toFixed(2);

      setFormData(prev => ({ ...prev, bid_lines: updatedLines }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.recommendation_notes.trim()) {
      toast.error("Recommendation notes are required");
      return;
    }

    try {
      // Update bid analysis
      await updateBidAnalysis({
        id: bidAnalysis.id,
        recommendation_notes: formData.recommendation_notes,
        finalized_at: formData.finalized_at || null,
      }).unwrap();

      // Update each bid line
      for (const line of formData.bid_lines) {
        if (line.supplier) {
          await updateBidAnalysisLine({
            id: line.id,
            supplier: line.supplier,
            unit_price: parseFloat(line.unit_price) || 0,
            quantity_awarded: parseFloat(line.quantity_awarded) || 0,
            delivery_time_days: parseInt(line.delivery_time_days) || 0,
            total_price: parseFloat(line.total_price) || 0,
          }).unwrap();
        }
      }

      toast.success("Bid Analysis updated successfully");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error updating bid analysis:", error);
      toast.error(error?.data?.message || "Failed to update bid analysis");
    }
  };

  const handleFinalize = async () => {
    // Check if all lines have suppliers assigned
    const unassignedLines = formData.bid_lines.filter(line => !line.supplier);
    if (unassignedLines.length > 0) {
      toast.error("Please assign suppliers to all bid lines before finalizing");
      return;
    }

    if (window.confirm("Finalize this bid analysis? This action cannot be undone.")) {
      try {
        await updateBidAnalysis({
          id: bidAnalysis.id,
          recommendation_notes: formData.recommendation_notes,
          finalized_at: new Date().toISOString(),
        }).unwrap();

        toast.success("Bid Analysis finalized successfully");
        onSuccess?.();
        onClose();
      } catch (error: any) {
        console.error("Error finalizing bid analysis:", error);
        toast.error(error?.data?.message || "Failed to finalize bid analysis");
      }
    }
  };

  const handleCreatePurchaseOrder = async () => {
    if (!bidAnalysis?.finalized_at) {
      toast.error("Please finalize the bid analysis before creating purchase order");
      return;
    }

    if (window.confirm("Create Purchase Order from this bid analysis? This will generate PO(s) for the selected suppliers.")) {
      try {
        const payload = {
          code: bidAnalysis.code || `BA-${String(bidAnalysis.id).padStart(4, '0')}`,
          rfq: bidAnalysis.rfq_number || `RFQ-${String(bidAnalysis.rfq).padStart(4, '0')}`,
          created_by: bidAnalysis.created_by_name || bidAnalysis.created_by,
          split_award: bidAnalysis.split_award,
          recommendation_notes: bidAnalysis.recommendation_notes,
          selected_responses: bidAnalysis.selected_responses,
          finalized_at: bidAnalysis.finalized_at,
        };

        const result = await generatePurchaseOrder({
          id: bidAnalysis.id,
          ...payload
        }).unwrap();

        toast.success(`Purchase Order(s) created successfully! ${result.pos?.length || 1} PO(s) generated.`);
        onSuccess?.();
        onClose();
      } catch (error: any) {
        console.error("Error creating purchase order:", error);
        toast.error(error?.data?.message || "Failed to create purchase order");
      }
    }
  };

  const suppliers = suppliersData?.results || [];
  const isLoading = isUpdatingAnalysis || isUpdatingLine;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Edit Bid Analysis - Split Award ({bidAnalysis?.code})
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Bid Analysis Info */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">Bid Analysis Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Code:</span> {bidAnalysis?.code}
              </div>
              <div>
                <span className="font-medium">RFQ:</span> {bidAnalysis?.rfq}
              </div>
              <div>
                <span className="font-medium">Award Type:</span> Split Award
              </div>
              <div>
                <span className="font-medium">Status:</span>{" "}
                <span className={bidAnalysis?.finalized_at ? "text-green-600" : "text-orange-600"}>
                  {bidAnalysis?.finalized_at ? "Finalized" : "Draft"}
                </span>
              </div>
            </div>
          </div>

          {/* Bid Lines */}
          <div>
            <Label className="text-lg font-semibold">Bid Lines - Supplier Assignment</Label>
            <p className="text-sm text-gray-600 mb-4">
              Assign suppliers to individual items. Each item can be awarded to a different supplier.
            </p>

            <div className="space-y-4">
              {formData.bid_lines.map((line, index) => (
                <div key={line.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center gap-2 mb-3">
                    <Package className="h-4 w-4" />
                    <h5 className="font-medium">Bid Line {index + 1}</h5>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    <div>
                      <Label>Supplier *</Label>
                      <Select
                        value={line.supplier}
                        onValueChange={(value) => handleBidLineChange(index, "supplier", value)}
                        disabled={!!bidAnalysis?.finalized_at}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select supplier" />
                        </SelectTrigger>
                        <SelectContent>
                          {suppliers.map((supplier: any) => (
                            <SelectItem key={supplier.code} value={supplier.code}>
                              {supplier.name} ({supplier.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Unit Price</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={line.unit_price}
                        onChange={(e) => handleBidLineChange(index, "unit_price", e.target.value)}
                        placeholder="0.00"
                        disabled={!!bidAnalysis?.finalized_at}
                      />
                    </div>

                    <div>
                      <Label>Quantity Awarded</Label>
                      <Input
                        type="number"
                        value={line.quantity_awarded}
                        onChange={(e) => handleBidLineChange(index, "quantity_awarded", e.target.value)}
                        placeholder="0"
                        disabled={!!bidAnalysis?.finalized_at}
                      />
                    </div>

                    <div>
                      <Label>Delivery Days</Label>
                      <Input
                        type="number"
                        value={line.delivery_time_days}
                        onChange={(e) => handleBidLineChange(index, "delivery_time_days", e.target.value)}
                        placeholder="0"
                        disabled={!!bidAnalysis?.finalized_at}
                      />
                    </div>

                    <div>
                      <Label>Total Price</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={line.total_price}
                        readOnly
                        className="bg-gray-100"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recommendation Notes */}
          <div>
            <Label htmlFor="recommendation_notes">Recommendation Notes *</Label>
            <Textarea
              id="recommendation_notes"
              value={formData.recommendation_notes}
              onChange={(e) => handleInputChange("recommendation_notes", e.target.value)}
              placeholder="Enter your recommendation notes and justification for supplier selections..."
              rows={4}
              required
              disabled={!!bidAnalysis?.finalized_at}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>

            <div className="flex space-x-2">
              {!bidAnalysis?.finalized_at && (
                <Button
                  type="button"
                  variant="default"
                  onClick={handleFinalize}
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Finalize Analysis
                </Button>
              )}

              {bidAnalysis?.finalized_at && (
                <Button
                  type="button"
                  variant="default"
                  onClick={handleCreatePurchaseOrder}
                  disabled={generatingPO}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {generatingPO && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Purchase Order
                </Button>
              )}

              <Button type="submit" disabled={isLoading || !!bidAnalysis?.finalized_at}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditBidAnalysisMultiple;
