import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import React, { useState, useEffect } from "react";
import { CreateComboMealModal } from "./Modals/CreateCombo";
import { MealCard } from "./Components/ComboCard";
import { useGetComboMenusQuery } from "@/redux/slices/comboMenu";

interface MealItem {
  title: string;
  description: string;
  imageUrl: string;
  price: string;
  category: string;
  taxClass?: string;
  rating?: number;
  prepTime: string;
}

// Updated API Response interfaces to match actual combo menu structure
interface ComboMenu {
  id: number;
  name: string;
  code: string;
  price: string;
  description: string;
  menu_group: number;
  created_at: string;
  updated_at: string;
  // Add other fields that might be in your actual API response
}

interface ComboApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: ComboMenu[];
  };
}

function FoodCombo() {
  // Add query parameters for pagination if needed
  const { data: comboMenus, isLoading, error, refetch } = useGetComboMenusQuery({
    page: 1,
    per_page: 50, // Adjust based on your needs
  });
  
  const [meals, setMeals] = useState<MealItem[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Debug: Log the raw API response
  useEffect(() => {
    console.log("Raw API Response:", comboMenus);
    console.log("Is Loading:", isLoading);
    console.log("Error:", error);
  }, [comboMenus, isLoading, error]);

  // Map actual API response to meals format
  useEffect(() => {
    if (comboMenus?.data?.results && Array.isArray(comboMenus.data.results)) {
      console.log("Processing combo menus:", comboMenus.data.results);
      const transformedMeals = transformApiDataToMeals(comboMenus.data.results);
      setMeals(transformedMeals);
      console.log("Transformed meals:", transformedMeals);
    } else {
      console.log("No results found or invalid structure:", comboMenus);
    }
  }, [comboMenus]);

  // Transform API data to meal format - Updated for combo menu structure
  const transformApiDataToMeals = (apiData: ComboMenu[]): MealItem[] => {
    return apiData.map((combo) => ({
      title: combo.name,
      description: combo.description || `Combo meal with code: ${combo.code}`,
      imageUrl: getComboImage(combo.id),
      price: formatPrice(combo.price),
      category: "Combo Meal",
      taxClass: "standard",
      rating: 4.5,
      prepTime: "15-20 mins", // Default prep time
    }));
  };

  // Helper function to format price
  const formatPrice = (price: string): string => {
    const numPrice = parseFloat(price);
    return isNaN(numPrice) ? "KES 0.00" : `KES ${numPrice.toFixed(2)}`;
  };

  // Helper function to get combo image based on combo ID
  const getComboImage = (comboId: number): string => {
    const comboImages = [
      "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1594212699903-ec8a3eca50f5?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1571091718767-18b5b1457add?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1512152272829-e3139592d56f?auto=format&fit=crop&w=400&q=80"
    ];
    return comboImages[comboId % comboImages.length] || comboImages[0];
  };

  const handleAddMenuItem = (newItem: MealItem) => {
    setMeals((prev) => [...prev, newItem]);
    // Refetch data after adding new combo
    refetch();
  };

  const handleMealCardClick = (meal: MealItem) => {
    console.log("Selected meal:", meal);
  };

  // Enhanced error handling
  const renderErrorState = () => {
    if (!error) return null;

    let errorMessage = "Failed to load combo menus";
    
    if (typeof error === 'object' && error !== null) {
      if ('status' in error) {
        errorMessage = `Error ${error.status}: Failed to fetch combo menus`;
      } else if ('message' in error) {
        errorMessage = error.message as string;
      }
    }

    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">⚠️</div>
        <h3 className="text-xl font-semibold text-red-600 mb-2">Error Loading Combos</h3>
        <p className="text-gray-600 mb-4">{errorMessage}</p>
        <PrimaryButton onClick={() => refetch()}>
          Try Again
        </PrimaryButton>
      </div>
    );
  };

  return (
    <Screen>
      <header className="mb-6">
        <div className="flex-1">
          <div className="relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-red-600 rounded-2xl shadow-2xl">
            <div className="px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <div className="w-8 h-8 bg-white/30 rounded flex items-center justify-center">
                    🍽️
                  </div>
                </div>
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold text-white drop-shadow">Combo Menu</h1>
                  <p className="text-white/80 text-sm">
                    Delicious combo meals at great prices
                  </p>
                  {/* Debug info - remove in production */}
                  {process.env.NODE_ENV === 'development' && comboMenus?.data && (
                    <p className="text-white/60 text-xs">
                      Total: {comboMenus.data.total_data} | Page: {comboMenus.data.current_page}/{comboMenus.data.last_page}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          {comboMenus?.data && (
            <p className="text-gray-600 text-sm">
              Showing {meals.length} of {comboMenus.data.total_data} combos
            </p>
          )}
        </div>
        <PrimaryButton 
          variant="primary" 
          onClick={() => setIsModalOpen(true)}
          disabled={isLoading}
        >
          Add Combo
        </PrimaryButton>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-3 text-gray-600">Loading combos...</span>
        </div>
      )}

     

      

      {/* Meals Grid */}
      {!isLoading && !error && meals.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {meals.map((meal, index) => (
            <MealCard
              key={`${meal.title}-${index}`}
              title={meal.title}
              description={meal.description}
              imageUrl={meal.imageUrl}
              price={meal.price}
              category={meal.category}
              rating={meal.rating}
              prepTime={meal.prepTime}
              onClick={() => handleMealCardClick(meal)}
              className="max-w-xs hover:scale-105 transition-transform duration-200"
            />
          ))}
        </div>
      )}

      {/* Pagination - if you need to load more data */}
      {comboMenus?.data && comboMenus.data.current_page < comboMenus.data.last_page && (
        <div className="flex justify-center mt-8">
          <PrimaryButton 
            onClick={() => {
              // You might need to implement pagination logic here
              console.log("Load more combos");
            }}
            disabled={isLoading}
          >
            Load More
          </PrimaryButton>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && meals.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🍽️</div>
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No combos available</h3>
          <p className="text-gray-500 mb-4">Start by adding your first combo meal!</p>
          <PrimaryButton onClick={() => setIsModalOpen(true)}>
            Add First Combo
          </PrimaryButton>
        </div>
      )}

      {/* Modal */}
      {isModalOpen && (
        <CreateComboMealModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddMenuItem}
        />
      )}
    </Screen>
  );
}

export default FoodCombo;