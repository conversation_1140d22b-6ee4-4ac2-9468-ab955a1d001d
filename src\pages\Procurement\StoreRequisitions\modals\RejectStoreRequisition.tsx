import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON><PERSON>gle, Loader2 } from "lucide-react";
import { useRejectStoreRequisitionMutation } from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface RejectStoreRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
  requisitionId: number | null;
  requisitionNumber?: string;
}

const RejectStoreRequisition = ({ 
  isOpen, 
  onClose, 
  requisitionId, 
  requisitionNumber 
}: RejectStoreRequisitionProps) => {
  const [rejectStoreRequisition, { isLoading: rejecting }] = useRejectStoreRequisitionMutation();
  const [reason, setReason] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }

    if (!requisitionId) {
      toast.error("No requisition selected");
      return;
    }

    try {
      await rejectStoreRequisition({ id: requisitionId, reason: reason.trim() }).unwrap();
      toast.success("Store requisition rejected successfully");
      setReason("");
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject requisition");
    }
  };

  const handleClose = () => {
    setReason("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Reject Store Requisition
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-800">
              You are about to reject{" "}
              <span className="font-semibold">
                {requisitionNumber || `Requisition #${requisitionId}`}
              </span>
              . This action cannot be undone.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="reason">Reason for Rejection *</Label>
              <Textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Please provide a detailed reason for rejecting this requisition..."
                rows={4}
                className="resize-none"
                required
              />
              <p className="text-xs text-gray-500">
                This reason will be visible to the requester and for audit purposes.
              </p>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="destructive" 
                disabled={rejecting || !reason.trim()}
              >
                {rejecting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Reject Requisition
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RejectStoreRequisition;
