import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { Pencil, Trash2, Package, ArrowUpDown, ArrowUp, ArrowDown, Plus } from 'lucide-react';
import type { FilterOptions } from './InventoryFilters';
import { cn } from '@/lib/utils';
import { InventoryItem } from '../types/inventory-item.type';

interface InventoryTableViewProps {
  items: InventoryItem[];
  filters: FilterOptions;
  onEdit: (item: InventoryItem) => void;
  onDelete: (id: number) => void;
  onSort: (column: FilterOptions['sortBy']) => void;
  onAdd: () => void;
  isDeleting: boolean;
  searchTerm: string;
}

export const InventoryTableView: React.FC<InventoryTableViewProps> = ({
  items,
  filters,
  onEdit,
  onDelete,
  onSort,
  onAdd,
  isDeleting,
  searchTerm,
}) => {
  const getStockStatus = (item: InventoryItem) => {
    if (item.quantity_available <= 0) {
      return { status: 'Out of Stock', variant: 'destructive' as const };
    } else if (item.quantity_available <= item.reorder_level) {
      return { status: 'Low Stock', variant: 'secondary' as const };
    }
    return { status: 'In Stock', variant: 'default' as const };
  };

  const isExpiringSoon = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  const isExpired = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  };

  const getSortIcon = (column: FilterOptions['sortBy']) => {
    if (filters.sortBy !== column) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return filters.sortOrder === 'asc' ?
      <ArrowUp className="h-4 w-4" /> :
      <ArrowDown className="h-4 w-4" />;
  };

  if (items.length === 0) {
    return (
      <div className="p-8 flex flex-col items-center justify-center text-center">
        <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No inventory items found</h3>
        <p className="text-muted-foreground mb-4">
          {searchTerm ? 'Try adjusting your search terms' : 'Get started by adding your first inventory item'}
        </p>
        {!searchTerm && (
          <Button onClick={onAdd} className='flex gap-2 !px-5 cursor-pointer'>
            <Plus className="h-4 w-4 " />
            Add First Item</Button>
        )}
      </div>
    );
  }

  return (
    <Card className='shadow-none'>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='py-5'>
                <Button
                  variant="ghost"
                  className="h-auto p-0 font-bold hover:bg-transparent !flex items-center !px-0"
                  onClick={() => onSort('name')}
                >
                  Product Name
                  {getSortIcon('name')}
                </Button>
              </TableHead>
              <TableHead className='font-bold'>Store</TableHead>
              <TableHead className='font-bold'>Branch</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="h-auto p-0 font-bold hover:bg-transparent flex items-center !px-0"
                  onClick={() => onSort('quantity')}
                >
                  Available
                  {getSortIcon('quantity')}
                </Button>
              </TableHead>
              <TableHead className='font-bold'>Reorder Level</TableHead>
              <TableHead className='font-bold'>Stock Status</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="h-auto p-0 font-bold hover:bg-transparent flex items-center !px-0"
                  onClick={() => onSort('expiry')}
                >
                  Expiry Date
                  {getSortIcon('expiry')}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="h-auto p-0 font-bold hover:bg-transparent flex items-center !px-0"
                  onClick={() => onSort('updated')}
                >
                  Last Updated
                  {getSortIcon('updated')}
                </Button>
              </TableHead>
              <TableHead className="text-center font-bold">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => {
              const stockStatus = getStockStatus(item);
              const expiringSoon = isExpiringSoon(item.expiry_date);
              const expired = isExpired(item.expiry_date);

              return (
                <TableRow key={item.id} className="hover:bg-muted/30">
                  <TableCell className="font-medium">
                    {item.product}
                  </TableCell>
                  <TableCell>{item?.store}</TableCell>
                  <TableCell>{item?.branch}</TableCell>
                  <TableCell>
                    <span className={cn(
                      "font-semibold",
                      item.quantity_available <= 0 && "text-destructive",
                      item.quantity_available <= item.reorder_level && item.quantity_available > 0 && "text-yellow-600"
                    )}>
                      {item.quantity_available}
                    </span>
                  </TableCell>
                  <TableCell>{item.reorder_level}</TableCell>
                  <TableCell>
                    <Badge variant={stockStatus.variant}>
                      {stockStatus.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {item.expiry_date ? (
                      <div className="flex flex-col items-center space-y-0.5 text-xs">
                        <span className={cn(
                          expired && "text-destructive",
                          expiringSoon && !expired && "text-yellow-600"
                        )}>
                          {new Date(item.expiry_date).toLocaleDateString()}
                        </span>
                        {expired && (
                          <Badge variant="destructive" className="text-[10px]">
                            Expired
                          </Badge>
                        )}
                        {expiringSoon && !expired && (
                          <Badge variant="secondary" className="text-[10px]">
                            Soon
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(item.last_updated).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(item)}
                        className="h-8 w-8 p-0"
                        title="Edit item"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(item.id)}
                        disabled={isDeleting}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                        title="Delete item"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};