import { Screen } from "@/app-components/layout/screen";
import AddStoreRequisition from "./modals/AddStoreRequisition";
import EditStoreRequisition from "./modals/EditStoreRequisition";
import ViewStoreRequisition from "./modals/ViewStoreRequisition";
import RejectStoreRequisition from "./modals/RejectStoreRequisition";
import { useState, useEffect } from "react";
import { useClientSideFiltering, buildApiParams, FilterStats } from "@/hooks/useClientSideFiltering";

// Inline FilterToggleComponent as fallback
const FilterToggleComponent = ({
  useClientSideFiltering,
  onToggle,
  filterStats,
  className = ""
}: {
  useClientSideFiltering: boolean;
  onToggle: () => void;
  filterStats: FilterStats;
  className?: string;
}) => {
  return (
    <div className={`flex items-center gap-2 text-xs ${className}`}>
      <button
        onClick={onToggle}
        className="px-2 py-1 rounded bg-gray-100 hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
        type="button"
      >
        {useClientSideFiltering ? "🔧 Client-side" : "🌐 Server-side"} filtering
      </button>

      {filterStats.hasActiveFilters && (
        <span className="text-blue-600 font-medium">
          {filterStats.filteredCount} of {filterStats.originalCount} results
          {filterStats.isFiltered && useClientSideFiltering && (
            <span className="text-orange-600 ml-1">
              (client-filtered)
            </span>
          )}
        </span>
      )}

      {filterStats.hasActiveFilters && filterStats.isFiltered && useClientSideFiltering && (
        <div className="text-amber-600 bg-amber-50 px-2 py-1 rounded border border-amber-200">
          ⚠️ Using client-side filtering
        </div>
      )}
    </div>
  );
};
import {
  useGetStoreRequisitionsQuery,
  useSubmitStoreRequisitionMutation,
  useApproveStoreRequisitionMutation,
  useRejectStoreRequisitionMutation,
  useDeleteStoreRequisitionMutation,
  useIssueStoreRequisitionMutation,
  useConvertToPurchaseRequisitionMutation,
  useMarkStoreRequisitionAsConvertedMutation
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { StoreRequisition } from "@/types/procurement";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, Trash2, Send, CheckCircle, XCircle } from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const StoreRequisitionsIndex = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [editingRequisition, setEditingRequisition] = useState<StoreRequisition | null>(null);
  const [viewingRequisition, setViewingRequisition] = useState<StoreRequisition | null>(null);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [rejectingRequisitionId, setRejectingRequisitionId] = useState<number | null>(null);
  const [searchInput, setSearchInput] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [statusFilter, setStatusFilter] = useState("");

  // Reset page when status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter]);

  // Client-side filtering setup
  const [clientSideFilteringEnabled, setClientSideFilteringEnabled] = useState(true);

  const filters = { status: statusFilter };

  const apiParams = buildApiParams(
    {
      page: currentPage,
      page_size: itemsPerPage,
      search: searchValue,
    },
    filters,
    clientSideFilteringEnabled
  );

  const {
    data: storeRequisitions,
    isLoading,
    error,
    isFetching,
  } = useGetStoreRequisitionsQuery(apiParams);

  // Client-side filtering
  const {
    filteredData: filteredResults,
    filterStats,
    useClientSideFiltering: isClientSideFiltering,
    setUseClientSideFiltering
  } = useClientSideFiltering(
    storeRequisitions?.results,
    filters,
    searchValue,
    {
      searchFields: ['code', 'purpose', 'requested_by_name'],
      enableClientSideFiltering: clientSideFilteringEnabled
    }
  );



  // Mutation hooks
  const [submitStoreRequisition, { isLoading: isSubmitting }] = useSubmitStoreRequisitionMutation();
  const [approveStoreRequisition, { isLoading: isApproving }] = useApproveStoreRequisitionMutation();
  const [rejectStoreRequisition, { isLoading: isRejecting }] = useRejectStoreRequisitionMutation();
  const [deleteStoreRequisition, { isLoading: isDeleting }] = useDeleteStoreRequisitionMutation();
  const [issueStoreRequisition, { isLoading: isIssuing }] = useIssueStoreRequisitionMutation();
  const [convertToPurchaseRequisition, { isLoading: isConverting }] = useConvertToPurchaseRequisitionMutation();
  const [markStoreRequisitionAsConverted] = useMarkStoreRequisitionAsConvertedMutation();

  // Handler functions
  const handleSubmit = async (id: number) => {
    try {
      await submitStoreRequisition(id).unwrap();
      toast.success("Store requisition submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await approveStoreRequisition(id).unwrap();
      toast.success("Store requisition approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = (id: number) => {
    setRejectingRequisitionId(id);
    setIsRejectModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to delete this requisition?")) return;

    try {
      await deleteStoreRequisition(id).unwrap();
      toast.success("Store requisition deleted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete requisition");
    }
  };

  const handleIssueItems = async (id: number) => {
    try {
      await issueStoreRequisition(id).unwrap();
      toast.success("Items issued successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to issue items");
    }
  };

  const handleConvertToPurchase = async (requisition: StoreRequisition) => {
    try {
      // Step 1: Create Purchase Requisition from Store Requisition
      const result = await convertToPurchaseRequisition({
        id: requisition.id,
        code: requisition.code,
        requested_by: requisition.requested_by,
        cost_center: requisition.cost_center,
        store: requisition.store,
        purpose: requisition.purpose,
        required_by: requisition.required_by
      }).unwrap();

      // Step 2: Mark Store Requisition as converted
      await markStoreRequisitionAsConverted(requisition.id).unwrap();

      toast.success(`Successfully converted to purchase requisition: ${result.code || 'PR-' + String(result.id).padStart(6, '0')}`);
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to convert to purchase requisition");
    }
  };

  const handleEdit = (requisition: StoreRequisition) => {
    setEditingRequisition(requisition);
    setIsEditModalOpen(true);
  };

  const handleView = (requisition: StoreRequisition) => {
    setViewingRequisition(requisition);
    setIsViewModalOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: {
        variant: "secondary" as const,
        color: "bg-gray-100 text-gray-800",
        icon: "📝",
        description: "In preparation"
      },
      Submitted: {
        variant: "default" as const,
        color: "bg-blue-100 text-blue-800",
        icon: "📤",
        description: "Awaiting approval"
      },
      Approved: {
        variant: "default" as const,
        color: "bg-green-100 text-green-800",
        icon: "✅",
        description: "Ready for processing"
      },
      Rejected: {
        variant: "destructive" as const,
        color: "bg-red-100 text-red-800",
        icon: "❌",
        description: "Needs revision"
      },
      Issued: {
        variant: "default" as const,
        color: "bg-purple-100 text-purple-800",
        icon: "📦",
        description: "Items issued"
      },
      Converted: {
        variant: "default" as const,
        color: "bg-orange-100 text-orange-800",
        icon: "🔄",
        description: "Converted to Purchase Req."
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <div className="flex flex-col">
        <Badge variant={config.variant} className={`${config.color} flex items-center gap-1`}>
          <span>{config.icon}</span>
          {status}
        </Badge>
        <span className="text-xs text-gray-500 mt-1">
          {config.description}
        </span>
      </div>
    );
  };

  const columns: ColumnDef<StoreRequisition>[] = [
    {
      accessorKey: "code",
      header: "Requisition Code",
      cell: (info) => (
        <div className="flex flex-col">
          <button
            onClick={() => handleView(info.row.original)}
            className="font-semibold text-blue-600 hover:text-blue-800 hover:underline text-left"
            title="Click to view details"
          >
            {info.getValue() as string || `STR-${String(info.row.original.id).padStart(6, '0')}`}
          </button>
          <span className="text-xs text-gray-500">
            ID: {info.row.original.id}
          </span>
        </div>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "purpose",
      header: "Purpose",
      cell: (info) => {
        const purpose = info.getValue() as string;
        return (
          <div className="max-w-sm">
            <div className="font-medium text-gray-900 truncate" title={purpose}>
              {purpose}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Click row to view full details
            </div>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "cost_center",
      header: "Cost Center",
      cell: (info) => {
        const value = info.getValue() as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {value || "Not Assigned"}
            </span>
            {value && (
              <span className="text-xs text-gray-500">
                Code: {value}
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "store",
      header: "Store",
      cell: (info) => {
        const value = info.getValue() as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {value || "Not Assigned"}
            </span>
            {value && (
              <span className="text-xs text-gray-500">
                Code: {value}
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "requested_by",
      header: "Requested By",
      cell: (info) => {
        const value = info.getValue() as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {value || "Not Assigned"}
            </span>
            {value && (
              <span className="text-xs text-gray-500">
                Employee: {value}
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "items",
      header: "Items",
      cell: (info) => {
        const items = info.getValue() as any[] || [];
        const itemCount = items.length;
        const totalQuantity = items.reduce((sum: number, item: any) =>
          sum + parseFloat(item.quantity || 0), 0
        );

        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {itemCount} {itemCount === 1 ? 'item' : 'items'}
            </span>
            <span className="text-xs text-gray-500">
              Total qty: {totalQuantity.toFixed(2)}
            </span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "required_by",
      header: "Required By",
      cell: (info) => {
        const date = info.getValue() as string;
        if (!date) return <span className="text-gray-400">Not set</span>;

        const requiredDate = new Date(date);
        const today = new Date();
        const diffTime = requiredDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        let urgencyColor = "text-gray-600";
        let urgencyText = "";
        let urgencyIcon = "📅";

        if (diffDays < 0) {
          urgencyColor = "text-red-600";
          urgencyText = "Overdue";
          urgencyIcon = "🚨";
        } else if (diffDays <= 3) {
          urgencyColor = "text-orange-600";
          urgencyText = "Urgent";
          urgencyIcon = "⚠️";
        } else if (diffDays <= 7) {
          urgencyColor = "text-yellow-600";
          urgencyText = "Soon";
          urgencyIcon = "⏰";
        }

        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {urgencyIcon} {requiredDate.toLocaleDateString()}
            </span>
            {urgencyText && (
              <span className={`text-xs font-medium ${urgencyColor}`}>
                {urgencyText} ({Math.abs(diffDays)} days)
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => getStatusBadge(info.getValue() as string),
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: (info) => {
        const date = info.getValue() as string;
        if (!date) return <span className="text-gray-400">N/A</span>;

        const createdDate = new Date(date);
        const today = new Date();
        const diffTime = today.getTime() - createdDate.getTime();
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor(diffTime / (1000 * 60 * 60));

        let timeAgo = "";
        if (diffDays === 0) {
          if (diffHours === 0) {
            timeAgo = "Just now";
          } else {
            timeAgo = `${diffHours}h ago`;
          }
        } else if (diffDays === 1) {
          timeAgo = "Yesterday";
        } else {
          timeAgo = `${diffDays} days ago`;
        }

        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {createdDate.toLocaleDateString()}
            </span>
            <span className="text-xs text-gray-500">
              {timeAgo}
            </span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const requisition = row.original;
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleView(requisition)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              {requisition.status === "Draft" && (
                <>
                  <DropdownMenuItem onClick={() => handleEdit(requisition)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSubmit(requisition.id!)}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(requisition.id!)}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Submitted" && (
                <>
                  <DropdownMenuItem onClick={() => handleApprove(requisition.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleReject(requisition.id!)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Approved" && (
                <>
                  <DropdownMenuItem onClick={() => handleIssueItems(requisition.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Issue Items
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleConvertToPurchase(requisition)}>
                    <Send className="mr-2 h-4 w-4" />
                    Convert to Purchase Req.
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Rejected" && (
                <>
                  <DropdownMenuItem onClick={() => handleEdit(requisition)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit & Resubmit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(requisition.id!)}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Converted" && (
                <DropdownMenuItem disabled>
                  <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                  Converted to Purchase Req.
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Store Requisitions</h1>
          <p className="text-gray-600 mt-1">Manage and track store requisition requests</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="default" onClick={() => setIsAddModalOpen(true)} className="bg-blue-600 hover:bg-blue-700">
            ➕ Create Requisition
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {storeRequisitions && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          {(() => {
            const stats = storeRequisitions.results?.reduce((acc: any, req: any) => {
              acc.total++;
              acc[req.status] = (acc[req.status] || 0) + 1;
              acc.totalItems += req.items?.length || 0;
              return acc;
            }, { total: 0, totalItems: 0, Draft: 0, Submitted: 0, Approved: 0, Rejected: 0, Issued: 0, Converted: 0 }) || {};

            return (
              <>
                <div className="bg-white p-4 rounded-lg shadow-sm border">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Requisitions</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                    </div>
                    <div className="text-2xl">📋</div>
                  </div>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm border">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pending Approval</p>
                      <p className="text-2xl font-bold text-blue-600">{stats.Submitted || 0}</p>
                    </div>
                    <div className="text-2xl">📤</div>
                  </div>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm border">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Approved</p>
                      <p className="text-2xl font-bold text-green-600">{stats.Approved || 0}</p>
                    </div>
                    <div className="text-2xl">✅</div>
                  </div>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm border">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Issued</p>
                      <p className="text-2xl font-bold text-purple-600">{stats.Issued || 0}</p>
                    </div>
                    <div className="text-2xl">📦</div>
                  </div>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm border">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Items</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.totalItems}</p>
                    </div>
                    <div className="text-2xl">📦</div>
                  </div>
                </div>
              </>
            );
          })()}
        </div>
      )}

      {/* Global Loading Indicator */}
      {(isFetching || isSubmitting || isApproving || isRejecting || isDeleting || isIssuing || isConverting) && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            <span className="text-blue-800 text-sm">
              {isFetching && "Loading data..."}
              {isSubmitting && "Submitting requisition..."}
              {isApproving && "Approving requisition..."}
              {isRejecting && "Rejecting requisition..."}
              {isDeleting && "Deleting requisition..."}
              {isIssuing && "Issuing items..."}
              {isConverting && "Converting to purchase requisition..."}
            </span>
          </div>
        </div>
      )}

      {/* Status Filter */}
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-sm font-medium text-gray-700">Filter by Status:</span>
          <span className="text-xs text-gray-500">
            (Current: {statusFilter || "All"})
          </span>
          <FilterToggleComponent
            useClientSideFiltering={isClientSideFiltering}
            onToggle={() => setUseClientSideFiltering(!isClientSideFiltering)}
            filterStats={filterStats}
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          <Button
            variant={statusFilter === "" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("")}
            className={statusFilter === "" ? "bg-blue-600 hover:bg-blue-700" : ""}
          >
            All
          </Button>
          <Button
            variant={statusFilter === "Draft" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("Draft")}
            className={statusFilter === "Draft" ? "bg-blue-600 hover:bg-blue-700" : ""}
          >
            Draft
          </Button>
          <Button
            variant={statusFilter === "Submitted" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("Submitted")}
            className={statusFilter === "Submitted" ? "bg-blue-600 hover:bg-blue-700" : ""}
          >
            Submitted
          </Button>
          <Button
            variant={statusFilter === "Approved" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("Approved")}
            className={statusFilter === "Approved" ? "bg-blue-600 hover:bg-blue-700" : ""}
          >
            Approved
          </Button>
          <Button
            variant={statusFilter === "Rejected" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("Rejected")}
            className={statusFilter === "Rejected" ? "bg-blue-600 hover:bg-blue-700" : ""}
          >
            Rejected
          </Button>
          <Button
            variant={statusFilter === "Converted" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("Converted")}
            className={statusFilter === "Converted" ? "bg-blue-600 hover:bg-blue-700" : ""}
          >
            Converted
          </Button>
          {statusFilter && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setStatusFilter("")}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              Clear Filter
            </Button>
          )}
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading store requisitions...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error loading store requisitions
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {(error as any)?.data?.message || "An unexpected error occurred"}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      {!isLoading && !error && (
        <DataTable<StoreRequisition>
        data={filteredResults as StoreRequisition[]}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-gray-50"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search requisitions..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={storeRequisitions?.total_data || 0}
      />
      )}

      {isAddModalOpen && (
        <AddStoreRequisition
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}

      {isEditModalOpen && (
        <EditStoreRequisition
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setEditingRequisition(null);
          }}
          requisition={editingRequisition}
        />
      )}

      {isRejectModalOpen && (
        <RejectStoreRequisition
          isOpen={isRejectModalOpen}
          onClose={() => {
            setIsRejectModalOpen(false);
            setRejectingRequisitionId(null);
          }}
          requisitionId={rejectingRequisitionId}
          requisitionNumber={
            rejectingRequisitionId
              ? `SR-${String(rejectingRequisitionId).padStart(4, '0')}`
              : undefined
          }
        />
      )}

      <ViewStoreRequisition
        isOpen={isViewModalOpen}
        onClose={() => {
          setIsViewModalOpen(false);
          setViewingRequisition(null);
        }}
        requisition={viewingRequisition}
        onEdit={handleEdit}
      />
    </Screen>
  );
};

export default StoreRequisitionsIndex;
