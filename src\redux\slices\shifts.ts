import { apiSlice } from "../apiSlice";

// Types for Shift API based on API specification
export interface Shift {
  code: string; // Auto-generated by backend
  start_time: string; // ISO datetime string
  end_time?: string | null; // ISO datetime string, nullable
  auto_ended?: boolean;
  employee: string; // Employee identifier
  branch: number; // Branch ID
  revenue_center?: number | null; // Revenue center ID, nullable
  workstation?: number | null; // Workstation ID, nullable
}

// Type for creating a new shift (with empty code for backend compatibility)
export interface CreateShiftRequest {
  code: string; // Empty string - backend will auto-generate
  start_time: string; // ISO datetime string
  end_time?: string | null; // ISO datetime string, nullable
  auto_ended?: boolean;
  employee: string; // Employee identifier
  branch: number; // Branch ID
  revenue_center?: number | null; // Revenue center ID, nullable
  workstation?: number | null; // Workstation ID, nullable
}

export interface ShiftEntry {
  code: string;
  shift_start_time: string; // ISO datetime string
  shift_end_time: string; // ISO datetime string
  start_time: string; // ISO datetime string
  end_time?: string | null; // ISO datetime string, nullable
  started_early?: boolean;
  started_late?: boolean;
  ended_early?: boolean;
  ended_late?: boolean;
  shift: string; // Shift code
  workstation?: number | null; // Workstation ID, nullable
}

export interface ShiftQueryParams {
  code?: string;
  employee?: string;
  branch?: string;
  revenue_center?: string;
  workstation?: string;
  start_time?: string;
  end_time?: string;
  search?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

export interface ShiftEntryQueryParams {
  code?: string;
  shift?: string;
  workstation?: string;
  start_time?: string;
  end_time?: string;
  search?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

export interface PaginatedShiftResponse {
  count: number;
  next?: string | null;
  previous?: string | null;
  results: Shift[];
}

export interface PaginatedShiftEntryResponse {
  count: number;
  next?: string | null;
  previous?: string | null;
  results: ShiftEntry[];
}

// API Response wrapper interfaces
export interface ApiShiftResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: Shift[];
  };
}

export interface ApiShiftEntryResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: ShiftEntry[];
  };
}

export const shiftApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all shifts with optional filtering
    getShifts: builder.query<PaginatedShiftResponse, ShiftQueryParams>({
      query: (params = {}) => ({
        url: "/shifts/",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any): PaginatedShiftResponse => {
        console.log("Raw shifts API response:", response);

        // Handle direct API response format (as per API documentation)
        if (response.count !== undefined && response.results !== undefined) {
          return {
            count: response.count,
            next: response.next,
            previous: response.previous,
            results: response.results,
          };
        }

        // Handle wrapped response format (if API returns nested data)
        if (response.data) {
          return {
            count: response.data.total_data,
            next: response.data.links?.next,
            previous: response.data.links?.previous,
            results: response.data.results,
          };
        }

        // Fallback
        return {
          count: 0,
          next: null,
          previous: null,
          results: [],
        };
      },
      providesTags: ["Shifts"],
    }),

    // Get single shift by code
    getShift: builder.query<Shift, string>({
      query: (code) => ({
        url: `/shifts/${code}`,
        method: "GET",
      }),
      providesTags: (result, error, code) => [{ type: "Shifts", id: code }],
    }),

    // Create a new shift
    createShift: builder.mutation<Shift, CreateShiftRequest>({
      query: (payload) => {
        console.log("RTK Query - Creating shift with payload:", JSON.stringify(payload, null, 2));
        return {
          url: "/shifts/",
          method: "POST",
          body: payload,
          headers: {
            'Content-Type': 'application/json',
          },
        };
      },
      invalidatesTags: ["Shifts"],
    }),

    // Update shift (partial update)
    updateShift: builder.mutation<Shift, { code: string; data: Partial<Shift> }>({
      query: ({ code, data }) => ({
        url: `/shifts/${code}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { code }) => [
        { type: "Shifts", id: code },
        "Shifts",
      ],
    }),

    // Delete shift
    deleteShift: builder.mutation<void, string>({
      query: (code) => ({
        url: `/shifts/${code}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Shifts"],
    }),

    // Get all shift entries
    getShiftEntries: builder.query<PaginatedShiftEntryResponse, ShiftEntryQueryParams>({
      query: (params = {}) => ({
        url: "/shifts/shift-entries/",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any): PaginatedShiftEntryResponse => {
        console.log("Raw shift entries API response:", response);

        // Handle direct API response format (as per API documentation)
        if (response.count !== undefined && response.results !== undefined) {
          return {
            count: response.count,
            next: response.next,
            previous: response.previous,
            results: response.results,
          };
        }

        // Handle wrapped response format (if API returns nested data)
        if (response.data) {
          return {
            count: response.data.total_data,
            next: response.data.links?.next,
            previous: response.data.links?.previous,
            results: response.data.results,
          };
        }

        // Fallback
        return {
          count: 0,
          next: null,
          previous: null,
          results: [],
        };
      },
      providesTags: ["ShiftEntries"],
    }),

    // Get single shift entry by code
    getShiftEntry: builder.query<ShiftEntry, string>({
      query: (code) => ({
        url: `/shifts/shift-entries/${code}/`,
        method: "GET",
      }),
      providesTags: (result, error, code) => [{ type: "ShiftEntries", id: code }],
    }),

    // Create a new shift entry (clock in)
    createShiftEntry: builder.mutation<ShiftEntry, Partial<ShiftEntry>>({
      query: (payload) => ({
        url: "/shifts/shift-entries/",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["ShiftEntries", "Shifts"],
    }),

    // Update shift entry (partial update)
    updateShiftEntry: builder.mutation<ShiftEntry, { code: string; data: Partial<ShiftEntry> }>({
      query: ({ code, data }) => ({
        url: `/shifts/shift-entries/${code}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { code }) => [
        { type: "ShiftEntries", id: code },
        "ShiftEntries",
        "Shifts",
      ],
    }),

    // Delete shift entry
    deleteShiftEntry: builder.mutation<void, string>({
      query: (code) => ({
        url: `/shifts/shift-entries/${code}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["ShiftEntries", "Shifts"],
    }),
  }),
});

export const {
  useGetShiftsQuery,
  useGetShiftQuery,
  useCreateShiftMutation,
  useUpdateShiftMutation,
  useDeleteShiftMutation,
  useGetShiftEntriesQuery,
  useGetShiftEntryQuery,
  useCreateShiftEntryMutation,
  useUpdateShiftEntryMutation,
  useDeleteShiftEntryMutation,

  // Lazy queries
  useLazyGetShiftsQuery,
  useLazyGetShiftQuery,
  useLazyGetShiftEntriesQuery,
  useLazyGetShiftEntryQuery,
} = shiftApiSlice;
