import React, { useState } from 'react';
import { ProductList } from './ProductList';
import { ProductForm } from './ProductForm';
import type { ProductItem } from '../types/product.type';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Screen } from '@/app-components/layout/screen';

export const ProductModule: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<ProductItem | undefined>();

  const handleAdd = () => {
    setEditingItem(undefined);
    setShowForm(true);
  };

  const handleEdit = (item: ProductItem) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingItem(undefined);
  };

  return (
    <Screen>
      <div className="container mx-auto py-6 space-y-5">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Products</h1>
            <p className="text-muted-foreground">
              Manage your product catalog
            </p>
          </div>
          <Button onClick={handleAdd} className="flex items-center gap-2 cursor-pointer">
            <Plus className="h-4 w-4" />
            Add Product
          </Button>
        </div>

        <ProductList onEdit={handleEdit} onAdd={handleAdd} />

        {showForm && (
          <ProductForm
            item={editingItem}
            onClose={handleCloseForm}
            onSuccess={handleFormSuccess}
          />
        )}
      </div>
    </Screen>
  );
};