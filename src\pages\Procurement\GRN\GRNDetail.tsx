import { useState } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useGetGRNQuery,
  useUpdateGRNMutation,
  useDeleteGRNMutation,
} from "@/redux/slices/procurement";
import {
  <PERSON>L<PERSON><PERSON>,
  Edit,
  Trash2,
  FileText,
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Download,
  Printer,
  RefreshCw,
  Receipt
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { inventoryIntegrationHooks } from "@/utils/inventoryIntegration";

const GRNDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [statusUpdateOpen, setStatusUpdateOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<"Partial" | "Full" | "Rejected">("Partial");
  const [notes, setNotes] = useState("");

  // API hooks
  const { 
    data: grn, 
    isLoading, 
    error,
    refetch 
  } = useGetGRNQuery(id!, { skip: !id });

  const [updateGRN, { isLoading: updating }] = useUpdateGRNMutation();
  const [deleteGRN, { isLoading: deleting }] = useDeleteGRNMutation();

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!grn) return;

    const originalStatus = grn.status;

    try {
      await updateGRN({
        id: grn.id!,
        status: newStatus,
        notes: notes.trim() || undefined,
      }).unwrap();

      // Handle inventory integration for status changes
      if (grn.items && grn.items.length > 0) {
        try {
          if (newStatus === "Rejected" && originalStatus !== "Rejected") {
            // Reverse inventory if GRN is being rejected
            await inventoryIntegrationHooks.onGRNCancelled(
              grn.items,
              grn.store,
              grn.id!
            );
          }
        } catch (inventoryError) {
          console.warn("Inventory update failed:", inventoryError);
          // Don't fail the status update if inventory fails
        }
      }

      toast.success("GRN status updated successfully");
      setStatusUpdateOpen(false);
      setNotes("");
      refetch();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to update GRN status");
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!grn) return;

    if (window.confirm(`Are you sure you want to delete GRN ${grn.grn_number}?`)) {
      try {
        // Reverse inventory before deleting if items were received
        if (grn.items && grn.items.length > 0 && grn.status !== "Rejected") {
          try {
            await inventoryIntegrationHooks.onGRNCancelled(
              grn.items,
              grn.store,
              grn.id!
            );
          } catch (inventoryError) {
            console.warn("Inventory reversal failed:", inventoryError);
            // Continue with deletion even if inventory reversal fails
          }
        }

        await deleteGRN(grn.id!).unwrap();
        toast.success("GRN deleted successfully");
        navigate("/procurement/grns");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete GRN");
      }
    }
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusConfig = (status: string) => {
      switch (status) {
        case "Full":
          return {
            color: "bg-green-100 text-green-800 border-green-200",
            icon: CheckCircle,
          };
        case "Partial":
          return {
            color: "bg-yellow-100 text-yellow-800 border-yellow-200",
            icon: AlertTriangle,
          };
        case "Rejected":
          return {
            color: "bg-red-100 text-red-800 border-red-200",
            icon: XCircle,
          };
        default:
          return {
            color: "bg-gray-100 text-gray-800 border-gray-200",
            icon: FileText,
          };
      }
    };

    const config = getStatusConfig(status);
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} border flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </Screen>
    );
  }

  if (error || !grn) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Failed to load GRN details</p>
            <Button onClick={() => refetch()}>Try Again</Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/procurement/grns")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to GRNs
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <FileText className="h-8 w-8 text-blue-600" />
                {grn.grn_number}
              </h1>
              <p className="text-gray-600 mt-1">
                Goods Received Note Details
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>

            {grn.status === "Full" && (
              <Button
                onClick={() => navigate(`/procurement/invoices?grn=${grn.id}`)}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                <Receipt className="h-4 w-4 mr-2" />
                Create Invoice
              </Button>
            )}
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                setNewStatus(grn.status);
                setStatusUpdateOpen(true);
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Update Status
            </Button>
            <Button 
              variant="destructive" 
              size="sm"
              onClick={handleDelete}
              disabled={deleting}
            >
              {deleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete
            </Button>
          </div>
        </div>

        {/* GRN Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                GRN Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">GRN Number</Label>
                  <p className="font-semibold">{grn.grn_number}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Status</Label>
                  <div className="mt-1">
                    <StatusBadge status={grn.status} />
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Received Date</Label>
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-600" />
                    {new Date(grn.received_date).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Received By</Label>
                  <p className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-600" />
                    {grn.received_by_name || "N/A"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Purchase Order Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">PO Number</Label>
                  <Link 
                    to={`/procurement/purchase-orders/${grn.purchase_order}`}
                    className="font-semibold text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-2"
                  >
                    <Package className="h-4 w-4" />
                    {grn.purchase_order_number || `PO-${grn.purchase_order}`}
                  </Link>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Supplier</Label>
                  <p className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-gray-600" />
                    {grn.supplier_name || "N/A"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Store</Label>
                  <p className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-gray-600" />
                    {grn.store_name || "N/A"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* GRN Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Received Items
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => refetch()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {grn.items && grn.items.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Quantity Received</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Total Price</TableHead>
                    <TableHead>Unit of Measure</TableHead>
                    <TableHead>Tax Rate</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {grn.items.map((item, index) => (
                    <TableRow key={item.id || index}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.product_name || `Product ${item.product}`}</p>
                          {item.product_code && (
                            <p className="text-sm text-gray-600">{item.product_code}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {parseFloat(item.quantity_received).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        ${parseFloat(item.unit_price).toFixed(2)}
                      </TableCell>
                      <TableCell className="font-medium">
                        ${item.total_price ? parseFloat(item.total_price).toFixed(2) : 
                          (parseFloat(item.quantity_received) * parseFloat(item.unit_price)).toFixed(2)}
                      </TableCell>
                      <TableCell>{item.unit_of_measure_name || "N/A"}</TableCell>
                      <TableCell>{item.tax_rate ? `${item.tax_rate}%` : "0%"}</TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800 border-green-200">
                          {item.status || "Received"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No items found for this GRN
              </div>
            )}
          </CardContent>
        </Card>

        {/* Notes Section */}
        {grn.notes && (
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 whitespace-pre-wrap">{grn.notes}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Status Update Dialog */}
      <Dialog open={statusUpdateOpen} onOpenChange={setStatusUpdateOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update GRN Status</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">New Status</Label>
              <Select value={newStatus} onValueChange={(value: any) => setNewStatus(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Partial">Partial</SelectItem>
                  <SelectItem value="Full">Full</SelectItem>
                  <SelectItem value="Rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any notes about the status change..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setStatusUpdateOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleStatusUpdate} disabled={updating}>
              {updating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Status"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Screen>
  );
};

export default GRNDetail;
