import React, { useState } from "react";
import { useNavigate, usePara<PERSON>, <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Edit,
  Store,
  Building2,
  Calculator,
  Monitor,
  MoreHorizontal,
  Settings,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { RevenueCenter } from "@/types/pos";
import { Screen } from "@/app-components/layout/screen";
import { useRetrieveRevenueCenterQuery } from "@/redux/slices/revenueCenters";
import { useRetrieveBranchQuery } from "@/redux/slices/branches";
import { useGetWorkstationsQuery } from "@/redux/slices/workstations";
import { cascadeRevenueCenterActivation } from "@/services/cascadingActivation";
import { handleApiError, handleApiSuccess } from "@/utils/errorHandling";

const RevenueCenterDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // API hooks to fetch revenue center and related data
  const {
    data: revenueCenter,
    isLoading,
    error,
    refetch,
  } = useRetrieveRevenueCenterQuery(id || "");

  // Fetch branch data - try both branch_code and id for compatibility
  const {
    data: branchData,
    isLoading: loadingBranch,
    refetch: refetchBranch,
  } = useRetrieveBranchQuery(revenueCenter?.branch || "", {
    skip: !revenueCenter?.branch,
  });

  // Fetch workstations for this revenue center using multiple filtering approaches
  const {
    data: allWorkstations = [],
    isLoading: loadingWorkstations,
    refetch: refetchWorkstations,
  } = useGetWorkstationsQuery(
    {
      revenue_center: revenueCenter?.revenue_center_code,
      revenue_center_id: revenueCenter?.id?.toString(),
    },
    {
      skip: !revenueCenter,
    }
  );

  // Filter workstations for this revenue center - support multiple relationship patterns
  const workstations = Array.isArray(allWorkstations)
    ? allWorkstations.filter(
        (ws) =>
          ws.revenue_center === id ||
          ws.revenue_center === revenueCenter?.id?.toString() ||
          ws.revenue_center === revenueCenter?.revenue_center_code
      )
    : [];

  const handleRevenueCenterActivation = async () => {
    if (!revenueCenter?.id) return;

    const newActiveState = !revenueCenter.is_active;
    setIsRefreshing(true);

    try {
      const result = await cascadeRevenueCenterActivation(
        revenueCenter.id.toString(),
        newActiveState
      );

      if (result.success) {
        handleApiSuccess(result.message);
        // Refresh all data
        await Promise.all([refetch(), refetchBranch(), refetchWorkstations()]);
      } else {
        handleApiError(result.errors?.join(", ") || result.message);
      }
    } catch (error) {
      handleApiError(
        `Failed to ${
          newActiveState ? "activate" : "deactivate"
        } revenue center: ${error}`
      );
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">
              Loading revenue center details...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !revenueCenter) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">⚠️</div>
            <p className="text-muted-foreground mb-4">
              {error
                ? "Error loading revenue center details"
                : "Revenue center not found"}
            </p>
            <Button onClick={() => navigate("/pos/revenue-centers")}>
              Back to Revenue Centers
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Screen>
      <Tabs className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => navigate("/pos/revenue-centers")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem>
                  <Monitor className="h-4 w-4 mr-2" />
                  Manage Workstations
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Calculator className="h-4 w-4 mr-2" />
                  Tax Configuration
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleRevenueCenterActivation}
                  disabled={isRefreshing}
                >
                  {revenueCenter.is_active ? "Deactivate" : "Activate"} Revenue
                  Center
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="workstations">Workstations</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Basic Information */}
            <Card className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Branch</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">
                        {branchData?.name || revenueCenter.branch}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {branchData?.branch_code || revenueCenter.branch}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">
                    Revenue Center Code
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    <Store className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {revenueCenter.revenue_center_code}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <Badge
                    variant={
                      revenueCenter.is_active !== false
                        ? "default"
                        : "secondary"
                    }
                  >
                    {revenueCenter.is_active !== false ? "Active" : "Inactive"}
                  </Badge>
                </CardContent>
                <p className="text-muted-foreground">
                  Revenue Center Code: {revenueCenter.revenue_center_code} •{" "}
                  {branchData?.name || revenueCenter.branch}
                </p>
              </Card>
            </Card>
            <div className="flex items-center space-x-2">
              <Link to={`/pos/revenue-centers/${revenueCenter.id}/edit`}>
                <Button>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Revenue Center
                </Button>
              </Link>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuItem>
                    <Monitor className="h-4 w-4 mr-2" />
                    Manage Workstations
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Calculator className="h-4 w-4 mr-2" />
                    Tax Configuration
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    {revenueCenter.is_active ? "Deactivate" : "Activate"}{" "}
                    Revenue Center
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </TabsContent>

          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="workstations">Workstations</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">
                      Branch
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">
                          {branchData?.name || revenueCenter.branch}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {branchData?.branch_code || revenueCenter.branch}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">
                      Revenue Center Code
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <Store className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {revenueCenter.revenue_center_code}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">
                      Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Badge
                      variant={
                        revenueCenter.is_active !== false
                          ? "default"
                          : "secondary"
                      }
                    >
                      {revenueCenter.is_active !== false
                        ? "Active"
                        : "Inactive"}
                    </Badge>
                  </CardContent>
                </Card>
              </div>

              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Center Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Name
                      </label>
                      <p className="text-lg font-semibold">
                        {revenueCenter.name}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Code
                      </label>
                      <p className="text-lg font-semibold">
                        {revenueCenter.revenue_center_code}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Branch
                      </label>
                      <p className="text-lg font-semibold">
                        {branchData?.name || revenueCenter.branch}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Status
                      </label>
                      <p className="text-lg font-semibold">
                        <Badge
                          variant={
                            revenueCenter.is_active !== false
                              ? "default"
                              : "secondary"
                          }
                        >
                          {revenueCenter.is_active !== false
                            ? "Active"
                            : "Inactive"}
                        </Badge>
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="workstations" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Assigned Workstations</CardTitle>
                  <CardDescription>
                    POS devices and terminals assigned to this revenue center
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">
                      No Workstations Assigned
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      This revenue center doesn't have any workstations assigned
                      yet.
                    </p>
                    <Link to="/pos/workstations/new">
                      <Button>
                        <Monitor className="h-4 w-4 mr-2" />
                        Add Workstation
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* </TabsContent> */}

            <TabsContent value="workstations" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>
                    Assigned Workstations ({workstations.length})
                  </CardTitle>
                  <CardDescription>
                    POS devices and terminals assigned to this revenue center
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loadingWorkstations ? (
                    <div className="flex justify-center items-center h-32">
                      <div className="text-muted-foreground">
                        Loading workstations...
                      </div>
                    </div>
                  ) : workstations.length > 0 ? (
                    <div className="space-y-4">
                      {workstations.map((ws) => (
                        <div
                          key={ws.id || ws.workstation_code}
                          className="flex items-center justify-between p-4 border rounded-lg"
                        >
                          <div>
                            <div className="font-medium">{ws.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {ws.role}
                            </div>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline">
                                {ws.workstation_code}
                              </Badge>
                              <Badge
                                variant={ws.is_active ? "default" : "secondary"}
                              >
                                {ws.is_active ? "Active" : "Inactive"}
                              </Badge>
                              {ws.ip_address && (
                                <Badge variant="outline">{ws.ip_address}</Badge>
                              )}
                            </div>
                          </div>
                          <Link to={`/pos/workstations/${ws.id}`}>
                            <Button variant="outline" size="sm">
                              Configure
                            </Button>
                          </Link>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">
                        No Workstations Assigned
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        This revenue center doesn't have any workstations
                        assigned yet.
                      </p>
                      <Link to="/pos/workstations/new">
                        <Button>
                          <Monitor className="h-4 w-4 mr-2" />
                          Add Workstation
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Center Settings</CardTitle>
                  <CardDescription>
                    Configuration and operational settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2">General Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Code:</span>
                          <span className="font-medium">
                            {revenueCenter.revenue_center_code}
                          </span>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Configuration</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Active Status:</span>
                            <Badge
                              variant={
                                revenueCenter.is_active !== false
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {revenueCenter.is_active !== false
                                ? "Active"
                                : "Inactive"}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </Tabs>
      </Tabs>
    </Screen>
  );
};

export default RevenueCenterDetail;
