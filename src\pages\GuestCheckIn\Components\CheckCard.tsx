import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, User, Clock, CheckCircle, Eye, Split } from "lucide-react";
import { GuestCheck } from "./types/types";

interface CheckCardProps {
  check: GuestCheck;
  onCloseCheck?: (checkId: string) => void;
  onVoidItem?: (checkId: string, itemIndex: number) => void;
  onSplitCheck?: () => void;
  onViewCheck?: () => void;
  showActions?: boolean;
}

export const CheckCard: React.FC<CheckCardProps> = ({
  check,
  onCloseCheck,
  onSplitCheck,
  onViewCheck,
  showActions = true,
}) => {
  const formatTime = (timeString: string) =>
    new Date(timeString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });

  const formatKES = (value: number): string => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
      case "open":
        return "bg-green-50 text-green-700 border-green-200";
      case "closed":
        return "bg-gray-50 text-gray-700 border-gray-200";
      case "paid":
        return "bg-blue-50 text-blue-700 border-blue-200";
      default:
        return "bg-orange-50 text-orange-700 border-orange-200";
    }
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 border border-border/50 bg-card hover:bg-card/95">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            <div className="p-2.5 bg-primary/10 rounded-xl border border-primary/20">
              <MapPin className="h-4 w-4 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-foreground">
                {check.tableNumber}
              </CardTitle>
              <p className="text-sm text-muted-foreground flex items-center mt-1">
                <User className="h-3 w-3 mr-1.5" />
                {check.guestCount} guests
              </p>
            </div>
          </div>
          <Badge
            variant="outline"
            className={`${getStatusColor(check.status)} font-medium capitalize`}
          >
            {check.status}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Server and Time Info */}
        <div className="flex justify-between items-center py-2.5 px-3 bg-muted/30 rounded-lg border border-border/40">
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-foreground">
              {check.waiterName}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {formatTime(check.orderTime)}
            </span>
          </div>
        </div>

        {/* Check Details */}
        <div className="space-y-3">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Subtotal:</span>
              <span className="font-medium text-foreground">
                {formatKES(check.subtotal)}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Tax:</span>
              <span className="font-medium text-foreground">
                {formatKES(check.tax)}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Service:</span>
              <span className="font-medium text-foreground">
                {formatKES(check.serviceCharge)}
              </span>
            </div>
          </div>

          <div className="border-t border-border pt-3">
            <div className="flex justify-between items-center">
              <span className="font-semibold text-foreground">Total:</span>
              <span className="font-bold text-xl text-primary">
                {formatKES(check.total)}
              </span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        {showActions && (
          <div className="flex space-x-2 pt-4 border-t border-border">
            <Button
              size="sm"
              variant="outline"
              className="flex-1 hover:bg-accent/10 hover:border-accent/40 hover:text-accent"
              onClick={onViewCheck}
            >
              <Eye className="h-4 w-4 mr-1.5" />
              View
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="flex-1 hover:bg-secondary/10 hover:border-secondary/40 hover:text-secondary"
              onClick={onSplitCheck}
            >
              <Split className="h-4 w-4 mr-1.5" />
              Link
            </Button>
            <Button
              size="sm"
              className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
              onClick={() => onCloseCheck?.(check.id)}
            >
              <CheckCircle className="h-4 w-4 mr-1.5" />
              Close
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};