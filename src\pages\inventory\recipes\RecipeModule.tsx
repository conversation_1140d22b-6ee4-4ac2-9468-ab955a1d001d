import React, { useState } from 'react';
import { RecipeList } from './RecipeList';
import { RecipeForm } from './RecipeForm';
import { RecipeView } from './RecipeView';
import { RecipeVersionHistory } from './RecipeVersionHistory';
import { RecipeVersionComparison } from './RecipeVersionComparison';
import { Plus } from 'lucide-react';
import { Recipe } from '../types/recipe-type';
import { Button } from '@/components/ui/button';
import { Screen } from '@/app-components/layout/screen';

type ViewMode = 'list' | 'form' | 'view' | 'history' | 'comparison';

export const RecipeModule: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [editingItem, setEditingItem] = useState<Recipe | undefined>();
  const [viewingItem, setViewingItem] = useState<Recipe | undefined>();

  const handleAdd = () => {
    setEditingItem(undefined);
    setViewMode('form');
  };

  const handleEdit = (item: Recipe) => {
    setEditingItem(item);
    setViewMode('form');
  };

  const handleView = (item: Recipe) => {
    setViewingItem(item);
    setViewMode('view');
  };

  const handleViewHistory = (item: Recipe) => {
    setViewingItem(item);
    setViewMode('history');
  };

  const handleCompareVersions = (item: Recipe) => {
    setViewingItem(item);
    setViewMode('comparison');
  };

  const handleBackToList = () => {
    setViewMode('list');
    setEditingItem(undefined);
    setViewingItem(undefined);
  };

  const handleFormSuccess = () => {
    setViewMode('list');
    setEditingItem(undefined);
  };

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-5">
        {viewMode === 'list' && (
          <>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-3xl font-bold">Recipes</h1>
                <p className="text-muted-foreground">
                  Manage your recipe catalog with ingredients, costing, and preparation details
                </p>
              </div>
              <Button onClick={handleAdd} className="flex items-center gap-2 cursor-pointer">
                <Plus className="h-4 w-4" />
                Add Recipe
              </Button>
            </div>

            <RecipeList
              onEdit={handleEdit}
              onAdd={handleAdd}
              onView={handleView}
              onViewHistory={handleViewHistory}
              onCompareVersions={handleCompareVersions}
            />
          </>
        )}

        {viewMode === 'form' && (
          <RecipeForm
            item={editingItem}
            onClose={handleBackToList}
            onSuccess={handleFormSuccess}
          />
        )}

        {viewMode === 'view' && viewingItem && (
          <RecipeView
            recipe={viewingItem}
            onClose={handleBackToList}
            onEdit={() => handleEdit(viewingItem)}
            onViewHistory={() => handleViewHistory(viewingItem)}
          />
        )}

        {viewMode === 'history' && viewingItem && (
          <RecipeVersionHistory
            recipe={viewingItem}
            onClose={handleBackToList}
            onCompareVersions={() => handleCompareVersions(viewingItem)}
          />
        )}

        {viewMode === 'comparison' && viewingItem && (
          <RecipeVersionComparison
            recipe={viewingItem}
            onClose={handleBackToList}
          />
        )}
      </div>
    </Screen>
  );
};