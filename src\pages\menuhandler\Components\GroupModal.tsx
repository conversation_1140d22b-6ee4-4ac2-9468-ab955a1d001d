import React, { useState, useEffect, useMemo } from "react";
import { X, Utensils, Loader2, Check } from "lucide-react";
import { useAddMenuGroupMutation } from "@/redux/slices/menuGroup";
import { useGetMainMenusQuery } from "@/redux/slices/mainMenu";
import { useGetProductCategoriesQuery } from "@/redux/slices/productCategories";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "@/hooks/use-toast";


class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught in CreateMenuGroupModal:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-500">
          Something went wrong. Please try again or contact support.
        </div>
      );
    }
    return this.props.children;
  }
}

interface CreateMenuGroupModalProps {
  onClose: () => void;
  onSave: (newGroup: {
    name: string;
    sales_category: string;
    position: number;
    menu: number;
  }) => void;
  selectedMainMenu?: number | null;
}

export function CreateMenuGroupModal({ onClose, onSave, selectedMainMenu }: CreateMenuGroupModalProps) {
 
  const { data: mainMenuData, isLoading: isLoadingMainMenu, error: mainMenuError } = useGetMainMenusQuery({});
  const { data: salesCategoryData, isLoading: isLoadingSalesCategory, error: salesCategoryError } = useGetProductCategoriesQuery({});
  const [addMenuGroup, { isLoading: isSubmitting }] = useAddMenuGroupMutation();

  // Initialize form data
  const [formData, setFormData] = useState({
    name: "",
    sales_category: "FOOD", // Default to FOOD from enum
    position: "1",
    menu: selectedMainMenu?.toString() || "",
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [backendError, setBackendError] = useState<string | null>(null); // Store backend errors

  // Log API responses for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("Main Menu Data:", mainMenuData);
      console.log("Sales Category Data:", salesCategoryData);
      if (mainMenuError) console.error("Main Menu Error:", mainMenuError);
      if (salesCategoryError) console.error("Sales Category Error:", salesCategoryError);
    }
  }, [mainMenuData, salesCategoryData, mainMenuError, salesCategoryError]);

  // Process main menus and sales categories
  const filteredMainMenus = useMemo(() => {
    if (!mainMenuData?.data?.results) return [];
    return mainMenuData.data.results
      .filter((menu: any) => menu.id && menu.is_active)
      .map((menu: any) => ({
        id: menu.id.toString(),
        name: menu.name || "Unknown",
      }));
  }, [mainMenuData]);

  const salesCategories = useMemo(() => [
    { id: "FOOD", name: "Food" },
    { id: "DRINKS", name: "Drinks" },
    { id: "DESSERTS", name: "Desserts" },
    { id: "COMBOS", name: "Combos" },
    { id: "PROMOTIONS", name: "Promotions" },
  ], []); // Hard-coded enum values

  // Log processed data for dropdowns
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("Filtered Main Menus:", filteredMainMenus);
      console.log("Sales Categories:", salesCategories);
    }
  }, [filteredMainMenus, salesCategories]);

  // Set default menu if selectedMainMenu changes
  useEffect(() => {
    if (!formData.menu && selectedMainMenu) {
      setFormData((prev) => ({
        ...prev,
        menu: selectedMainMenu.toString(),
      }));
    }
  }, [selectedMainMenu]);

  const handleInputChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (validationErrors[name]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
    setBackendError(null); // Clear backend error on input change

    if (name === "sales_category" && process.env.NODE_ENV === "development") {
      console.log("Selected Sales Category:", value);
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    let isValid = true;

    if (!formData.name.trim()) {
      errors.name = "Name is required";
      isValid = false;
    } else if (formData.name.length > 100) {
      errors.name = "Name cannot exceed 100 characters";
      isValid = false;
    }

    if (!formData.sales_category) {
      errors.sales_category = "Sales category is required";
      isValid = false;
    } else if (!salesCategories.map((cat) => cat.id).includes(formData.sales_category)) {
      errors.sales_category = "Invalid sales category";
      isValid = false;
    }

    if (!formData.position.trim()) {
      errors.position = "Position is required";
      isValid = false;
    } else if (
      isNaN(Number(formData.position)) ||
      Number(formData.position) < -2147483648 ||
      Number(formData.position) > 2147483647
    ) {
      errors.position = "Position must be a valid number between -2147483648 and 2147483647";
      isValid = false;
    }

    if (!formData.menu) {
      errors.menu = "Main menu is required";
      isValid = false;
    }

    setValidationErrors(errors);
    if (!isValid) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill all required fields correctly",
      });
    }
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      console.warn("Form validation failed:", validationErrors);
      return;
    }

    try {
      const payload = {
        name: formData.name,
        sales_category: formData.sales_category,
        position: parseInt(formData.position),
        menu: parseInt(formData.menu),
      };

      if (process.env.NODE_ENV === "development") {
        console.log("Submitting Menu Group Payload:", payload);
      }

      const result = await addMenuGroup(payload).unwrap();
      console.log("Menu group created successfully:", result);

      onSave({
        name: formData.name,
        sales_category: formData.sales_category,
        position: parseInt(formData.position),
        menu: parseInt(formData.menu),
      });
      toast({
        title: "Success",
        description: "Menu group created successfully!",
      });
      onClose();
    } catch (error: any) {
      console.error("Failed to add menu group:", error);
      const errorMessage =
        error?.data?.detail ||
        error?.data?.message ||
        "Failed to create menu group. Please try again.";
      setBackendError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    }
  };

  // Render loading state if critical data is missing
  if ((isLoadingMainMenu && !mainMenuData) || (isLoadingSalesCategory && !salesCategoryData)) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl h-[85vh] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
          onClick={onClose}
          aria-label="Close modal"
        />
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-lg h-[85vh] flex flex-col overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
          <div className="bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 p-4 text-white flex-shrink-0">
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                    <Utensils className="h-5 w-5" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Create Menu Group</h2>
                    <p className="text-white/90 text-xs">Add a new menu group to organize your items</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 rounded-full"
                  disabled={isSubmitting || isLoadingMainMenu || isLoadingSalesCategory}
                  aria-label="Close modal"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                {backendError && (
                  <div className="p-4 bg-red-50 text-red-500 rounded-md">{backendError}</div>
                )}

                {isSubmitting || isLoadingMainMenu || isLoadingSalesCategory ? (
                  <div className="flex items-center justify-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : mainMenuError || salesCategoryError ? (
                  <div className="text-red-500 text-center">
                    {mainMenuError
                      ? "Failed to load main menus. Please try again."
                      : "Failed to load sales categories. Please try again."}
                  </div>
                ) : (
                  <div className="space-y-8">
                    {/* General Information */}
                    <Card className="border-0 shadow-none">
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold">General Information</CardTitle>
                      </CardHeader>
                      <CardContent className="p-0 space-y-6">
                        {/* Name */}
                        <div className="space-y-2">
                          <Label htmlFor="name" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-orange-500" />
                            Menu Group Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="name"
                            placeholder="e.g., Main Course"
                            value={formData.name}
                            onChange={(e) => handleInputChange("name", e.target.value)}
                            className="h-11 text-base"
                            disabled={isSubmitting}
                            aria-required="true"
                          />
                          {validationErrors.name && (
                            <p className="text-sm text-red-500">{validationErrors.name}</p>
                          )}
                        </div>

                        {/* Sales Category */}
                        <div className="space-y-2">
                          <Label htmlFor="sales_category" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-orange-500" />
                            Sales Category <span className="text-red-500">*</span>
                          </Label>
                          <Select
                            value={formData.sales_category}
                            onValueChange={(value) => handleInputChange("sales_category", value)}
                            disabled={isSubmitting}
                          >
                            <SelectTrigger className="h-11">
                              <SelectValue placeholder="Select sales category" />
                            </SelectTrigger>
                            <SelectContent>
                              {salesCategories.map((category) => (
                                <SelectItem key={category.id} value={category.id}>
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {validationErrors.sales_category && (
                            <p className="text-sm text-red-500">{validationErrors.sales_category}</p>
                          )}
                        </div>

                        {/* Position */}
                        <div className="space-y-2">
                          <Label htmlFor="position" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-orange-500" />
                            Position <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="position"
                            placeholder="e.g., 1"
                            value={formData.position}
                            onChange={(e) => handleInputChange("position", e.target.value)}
                            className="h-11 text-base"
                            type="number"
                            step="1"
                            min="-2147483648"
                            max="2147483647"
                            disabled={isSubmitting}
                            aria-required="true"
                          />
                          {validationErrors.position && (
                            <p className="text-sm text-red-500">{validationErrors.position}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Menu Selection */}
                    <Card className="border-0 shadow-none">
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold">Menu Selection</CardTitle>
                      </CardHeader>
                      <CardContent className="p-0 space-y-6">
                        {/* Main Menu */}
                        <div className="space-y-2">
                          <Label htmlFor="menu" className="text-sm font-semibold flex items-center gap-2">
                            <Utensils className="h-4 w-4 text-orange-500" />
                            Main Menu <span className="text-red-500">*</span>
                          </Label>
                          {!filteredMainMenus.length ? (
                            <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                              No main menus available
                            </div>
                          ) : (
                            <Select
                              value={formData.menu}
                              onValueChange={(value) => handleInputChange("menu", value)}
                              disabled={isSubmitting || filteredMainMenus.length === 0}
                            >
                              <SelectTrigger className="h-11">
                                <SelectValue placeholder="Select main menu" />
                              </SelectTrigger>
                              <SelectContent>
                                {filteredMainMenus.map((menu) => (
                                  <SelectItem key={menu.id} value={menu.id}>
                                    {menu.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                          {validationErrors.menu && (
                            <p className="text-sm text-red-500">{validationErrors.menu}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </form>
            </ScrollArea>
          </div>

          <div className="border-t bg-muted/30 p-4 flex-shrink-0">
            <div className="flex justify-between">
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                disabled={isSubmitting}
                aria-label="Cancel and close modal"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting || isLoadingMainMenu || isLoadingSalesCategory || !filteredMainMenus.length}
                className="min-w-[100px] bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Create Menu Group
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}