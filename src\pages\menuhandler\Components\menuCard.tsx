import React, { useState } from "react";
import { X, <PERSON>H<PERSON>, Check, Loader2 } from "lucide-react";
import { useAddMainMenuMutation } from "@/redux/slices/mainMenu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useGetBranchesQuery } from "@/redux/slices/branches";
import { Switch } from "@/components/ui/switch";


// Error Boundary Component
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-500">
          Something went wrong. Please try again or contact support.
        </div>
      );
    }
    return this.props.children;
  }
}

interface CreateMainMenuModalProps {
  onClose: () => void;
  onSave: (newMenu: {
    name: string;
    description: string;
    sales_category: string;
    position: number;
    availability_type: string;
    available_days: object;
    available_times: object;
    ordering_channels: object;
    is_active: boolean;
    branch: string;
  }) => void;
}

export function CreateMainMenuModal({ onClose, onSave }: CreateMainMenuModalProps) {
  
  const [addMainMenu, { isLoading: isSubmitting }] = useAddMainMenuMutation();
  const { data: branchData, isLoading: branchLoading, error: branchError } = useGetBranchesQuery({});

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    sales_category: "FOOD",
    position: "1",
    availability_type: "ALWAYS",
    available_days: {},
    available_times: {},
    ordering_channels: {},
    is_active: true,
    branch: "",
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [backendError, setBackendError] = useState<string | null>(null);

  const handleInputChange = (name: string, value: string | boolean | object) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (validationErrors[name]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
    setBackendError(null);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    let isValid = true;

    if (!formData.name.trim()) {
      errors.name = "Name is required";
      isValid = false;
    }

    if (!formData.description.trim()) {
      errors.description = "Description is required";
      isValid = false;
    }

    if (!formData.position.trim()) {
      errors.position = "Position is required";
      isValid = false;
    } else if (isNaN(Number(formData.position)) || Number(formData.position) < 0) {
      errors.position = "Position must be a valid non-negative number";
      isValid = false;
    }

    if (!formData.branch) {
      errors.branch = "Branch is required";
      isValid = false;
    }

    const validAvailabilityTypes = ["ALWAYS", "SPECIFIC_DAYS", "SPECIFIC_TIMES"];
    if (!formData.availability_type.trim()) {
      errors.availability_type = "Availability type is required";
      isValid = false;
    } else if (!validAvailabilityTypes.includes(formData.availability_type.toUpperCase())) {
      errors.availability_type = "Availability type must be one of: ALWAYS, SPECIFIC_DAYS, SPECIFIC_TIMES";
      isValid = false;
    }

    setValidationErrors(errors);
    if (!isValid) {
      
    }
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const payload = {
        name: formData.name,
        description: formData.description,
        sales_category: formData.sales_category,
        position: parseInt(formData.position),
        availability_type: formData.availability_type.toUpperCase(),
        available_days: formData.available_days,
        available_times: formData.available_times,
        ordering_channels: formData.ordering_channels,
        is_active: formData.is_active,
        branch: formData.branch, // Sending branch_code as string
        menu: 0,
      };

      const result = await addMainMenu(payload).unwrap();

      onSave({
        name: formData.name,
        description: formData.description,
        sales_category: formData.sales_category,
        position: parseInt(formData.position),
        availability_type: formData.availability_type.toUpperCase(),
        available_days: formData.available_days,
        available_times: formData.available_times,
        ordering_channels: formData.ordering_channels,
        is_active: formData.is_active,
        branch: formData.branch,
      });
      
      onClose();
    } catch (error: any) {
      console.error("Failed to add main menu:", error);
      const errorMessage =
        error?.data?.branch?.[0] ||
        error?.data?.detail ||
        error?.data?.message ||
        "Failed to create main menu.";
      setBackendError(errorMessage);
      
    }
  };

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
          onClick={onClose}
          aria-label="Close modal"
        />
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-lg h-[90vh] flex flex-col animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
          <div className="bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 p-4 text-white flex-shrink-0">
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                    <ChefHat className="h-5 w-5" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Create Main Menu</h2>
                    <p className="text-white/90 text-xs">Add a new main menu to organize your items</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 rounded-full"
                  disabled={isSubmitting}
                  aria-label="Close modal"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {isSubmitting || branchLoading ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : branchError ? (
                <div className="text-red-500 text-center">Failed to load branches. Please try again.</div>
              ) : (
                <div className="space-y-8">
                  {/* Backend Error Display */}
                  {backendError && (
                    <div className="p-4 bg-red-50 text-red-500 rounded-md">
                      {backendError}
                    </div>
                  )}

                  {/* General Information */}
                  <Card className="border-0 shadow-none">
                    <CardHeader>
                      <CardTitle className="text-lg font-semibold">General Information</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0 space-y-6">
                      {/* Name */}
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-semibold flex items-center gap-2">
                          <ChefHat className="h-4 w-4 text-orange-500" />
                          Menu Name <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="name"
                          placeholder="e.g., Lunch Menu"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          className="h-11 text-base"
                          disabled={isSubmitting}
                          aria-required="true"
                        />
                        {validationErrors.name && (
                          <p className="text-sm text-red-500">{validationErrors.name}</p>
                        )}
                      </div>

                      {/* Description */}
                      <div className="space-y-2">
                        <Label htmlFor="description" className="text-sm font-semibold flex items-center gap-2">
                          <ChefHat className="h-4 w-4 text-orange-500" />
                          Description <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="description"
                          placeholder="e.g., Daily lunch specials"
                          value={formData.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                          className="h-11 text-base"
                          disabled={isSubmitting}
                          aria-required="true"
                        />
                        {validationErrors.description && (
                          <p className="text-sm text-red-500">{validationErrors.description}</p>
                        )}
                      </div>

                      {/* Sales Category */}
                      <div className="space-y-2">
                        <Label htmlFor="sales_category" className="text-sm font-semibold flex items-center gap-2">
                          <ChefHat className="h-4 w-4 text-orange-500" />
                          Sales Category
                        </Label>
                        <Select
                          value={formData.sales_category}
                          onValueChange={(value) => handleInputChange("sales_category", value)}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Select sales category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="FOOD">Food</SelectItem>
                            <SelectItem value="BEVERAGE">Beverage</SelectItem>
                            <SelectItem value="OTHER">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Position */}
                      <div className="space-y-2">
                        <Label htmlFor="position" className="text-sm font-semibold flex items-center gap-2">
                          <ChefHat className="h-4 w-4 text-orange-500" />
                          Position <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="position"
                          placeholder="e.g., 1"
                          value={formData.position}
                          onChange={(e) => handleInputChange("position", e.target.value)}
                          className="h-11 text-base"
                          type="number"
                          min="0"
                          disabled={isSubmitting}
                          aria-required="true"
                        />
                        {validationErrors.position && (
                          <p className="text-sm text-red-500">{validationErrors.position}</p>
                        )}
                      </div>

                      {/* Branch */}
                      <div className="space-y-2">
                        <Label htmlFor="branch" className="text-sm font-semibold flex items-center gap-2">
                          <ChefHat className="h-4 w-4 text-orange-500" />
                          Branch <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={formData.branch}
                          onValueChange={(value) => handleInputChange("branch", value)}
                          disabled={isSubmitting || !branchData?.length}
                        >
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Select branch" />
                          </SelectTrigger>
                          <SelectContent>
                            {branchData?.map((branch: any) => (
                              <SelectItem key={branch.id} value={branch.branch_code}>
                                {branch.name} ({branch.branch_code})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {validationErrors.branch && (
                          <p className="text-sm text-red-500">{validationErrors.branch}</p>
                        )}
                        {!branchData?.length && !branchLoading && (
                          <p className="text-sm text-red-500">No branches available. Please create a branch first.</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Availability Settings */}
                  <Card className="border-0 shadow-none">
                    <CardHeader>
                      <CardTitle className="text-lg font-semibold">Availability Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0 space-y-6">
                      {/* Availability Type */}
                      <div className="space-y-2">
                        <Label htmlFor="availability_type" className="text-sm font-semibold flex items-center gap-2">
                          <ChefHat className="h-4 w-4 text-orange-500" />
                          Availability Type <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="availability_type"
                          placeholder="e.g., ALWAYS, SPECIFIC_DAYS, SPECIFIC_TIMES"
                          value={formData.availability_type}
                          onChange={(e) => handleInputChange("availability_type", e.target.value)}
                          className="h-11 text-base"
                          disabled={isSubmitting}
                          aria-required="true"
                        />
                        {validationErrors.availability_type && (
                          <p className="text-sm text-red-500">{validationErrors.availability_type}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Status */}
                  <Card className="border-0 shadow-none">
                    <CardHeader>
                      <CardTitle className="text-lg font-semibold">Status</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0 space-y-6">
                      {/* Is Active */}
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold flex items-center gap-2">
                          <ChefHat className="h-4 w-4 text-orange-500" />
                          Active Status
                        </Label>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={formData.is_active}
                            onCheckedChange={(checked) => handleInputChange("is_active", checked)}
                            disabled={isSubmitting}
                          />
                          <span>{formData.is_active ? "Active" : "Inactive"}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </form>
          </div>

          <div className="border-t bg-muted/30 p-4 flex-shrink-0">
            <div className="flex justify-between">
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                disabled={isSubmitting}
                aria-label="Cancel and close modal"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="min-w-[120px] bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Create Menu
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}