import { CreateRecipeRequest, Recipe, RecipeApiResponse  } from "@/pages/inventory/types/recipe-type";
import { apiSlice } from "../apiSlice";


export const recipeApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getRecipe: builder.query<RecipeApiResponse, {params:{}}>({
            query: (params) => ({
                url: `/menu/recipes`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<RecipeApiResponse>) => response.data,
            providesTags: ["/menu/recipes"],
        }),
        updateRecipe: builder.mutation<Recipe, { id: number; body: Partial<CreateRecipeRequest> }>({
            query: ({ id, body }) => ({
                url: `/menu/recipes/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/menu/recipes"],
        }),
        createRecipe: builder.mutation<Recipe, CreateRecipeRequest>({
            query: (body) => ({
                url: `/menu/recipes`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/menu/recipes"],
        }),
        getOneRecipe: builder.query<Recipe, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/menu/recipes/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/menu/recipes"],
        }),
        deleteRecipe: builder.mutation<void, number>({
            query: (id) => ({
                url: `/menu/recipes/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/menu/recipes"],
        }),
    })
})

export const {
    useCreateRecipeMutation,
    useGetRecipeQuery,
    useUpdateRecipeMutation,
    useGetOneRecipeQuery,
    useDeleteRecipeMutation,
} = recipeApiSlice

interface ApiResponse<T> {
  data: T;
  message: string;
}