import { CreateProductSubCategoryItemRequest, ProductSubCategoryApiResponse, ProductSubCategoryItem  } from "@/pages/inventory/types/product-sub-category.type";
import { apiSlice } from "../apiSlice";

export const inventoryApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getProductSubCategory: builder.query<ProductSubCategoryApiResponse, {params:{}}>({
            query: (params) => ({
                url: `/inventory/product-sub-categories`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<ProductSubCategoryApiResponse>) => response.data,
            providesTags: ["/inventory/product-sub-categories"],
        }),
        updateProductSubCategory: builder.mutation<ProductSubCategoryItem, { id: number; body: Partial<CreateProductSubCategoryItemRequest> }>({
            query: ({ id, body }) => ({
                url: `/inventory/product-sub-categories/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/inventory/product-sub-categories"],
        }),
        createProductSubCategory: builder.mutation<ProductSubCategoryItem, CreateProductSubCategoryItemRequest>({
            query: (body) => ({
                url: `/inventory/product-sub-categories`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/inventory/product-sub-categories"],
        }),
        getOneProductSubCategory: builder.query<ProductSubCategoryItem, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/inventory/product-sub-categories/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/inventory/product-sub-categories"],
        }),
        deleteProductSubCategory: builder.mutation<void, number>({
            query: (id) => ({
                url: `/inventory/product-sub-categories/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/inventory/product-sub-categories"],
        }),
    })
})

export const {
    useCreateProductSubCategoryMutation,
    useGetProductSubCategoryQuery,
    useUpdateProductSubCategoryMutation,
    useGetOneProductSubCategoryQuery,
    useDeleteProductSubCategoryMutation,
} = inventoryApiSlice

interface ApiResponse<T> {
  data: T;
  message: string;
}