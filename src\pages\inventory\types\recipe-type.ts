export interface Recipe {
    id: number;
    name: string;
    recipe_type: 'Prep' | 'Serving';
    portion_size: string;
    image: string | null;
    preparation_time: number;   // in minutes
    cooking_time: number;       // in minutes
    instructions: string;
    tools_required: string | null;
    dietary_flags: string[] | null;
    is_active: boolean | null;
    recipe_group: number;
    location: string | null;
    menu_item: number | null;
    assigned_to?: string | null;  // User ID assigned to this recipe
    assigned_role?: string | null; // Role assigned to this recipe (chef, kitchen manager, etc.)
    last_modified?: string;       // ISO date string
    modified_by?: string;         // User ID who last modified
    modified_by_name?: string;    // Display name of who last modified
    is_recently_modified?: boolean; // Flag for recent modifications
    created_at?: string;          // ISO date string
    updated_at?: string;          // ISO date string
}

export interface RecipeApiResponse {
    current_page: number;
    per_page: number;
    total_data: number;
    results: Recipe[];
}

export interface CreateRecipeRequest {
    name: string;
    recipe_type: 'Prep' | 'Serving';
    portion_size: string;
    image: string | null;
    preparation_time: number;   // in minutes
    cooking_time: number;       // in minutes
    instructions: string;
    tools_required: string | null;
    dietary_flags: string[] | null;
    is_active: boolean | null;
    recipe_group: number;
    location: string | null;
    menu_item: number | null;
}

