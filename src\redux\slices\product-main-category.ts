import { CreateProductMainCategoryItemRequest, ProductMainCategoryApiResponse, ProductMainCategoryItem } from "@/pages/inventory/types/product-main-categories.type";
import { apiSlice } from "../apiSlice";

export const inventoryApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getProductMainCategory: builder.query<ProductMainCategoryApiResponse, {params:{}}>({
            query: (params) => ({
                url: `/inventory/product-main-categories`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<ProductMainCategoryApiResponse>) => response.data,
            providesTags: ["/inventory/product-main-categories"],
        }),
        updateProductMainCategory: builder.mutation<ProductMainCategoryItem, { id: number; body: Partial<CreateProductMainCategoryItemRequest> }>({
            query: ({ id, body }) => ({
                url: `/inventory/product-main-categories/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/inventory/product-main-categories"],
        }),
        createProductMainCategory: builder.mutation<ProductMainCategoryItem, CreateProductMainCategoryItemRequest>({
            query: (body) => ({
                url: `/inventory/product-main-categories`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/inventory/product-main-categories"],
        }),
        getOneProductMainCategory: builder.query<ProductMainCategoryItem, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/inventory/product-main-categories/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/inventory/product-main-categories"],
        }),
        deleteProductMainCategory: builder.mutation<void, number>({
            query: (id) => ({
                url: `/inventory/product-main-categories/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/inventory/product-main-categories"],
        }),
    })
})

export const {
    useCreateProductMainCategoryMutation,
    useGetProductMainCategoryQuery,
    useUpdateProductMainCategoryMutation,
    useGetOneProductMainCategoryQuery,
    useDeleteProductMainCategoryMutation,
} = inventoryApiSlice

interface ApiResponse<T> {
  data: T;
  message: string;
}