export interface RecipeVersion {
    id: number;
    data_snapshot: Record<string, any>;
    version_number: string;
    recipe: string;
    created_at: string;
    created_by: string;
    created_by_name?: string;
    created_by_role?: string;
    change_summary?: string;
    is_active: boolean;
    change_notes?: string;
    revert_reason?: string;
    approval_status?: 'pending' | 'approved' | 'rejected';
    approved_by?: string;
    approved_at?: string;
    changes_made?: string[];      // Array of field names that changed
    previous_version_id?: number; // Reference to previous version
    is_major_change?: boolean;    // Flag for significant changes
}

export interface RecipeVersionApiResponse {
    current_page: number;
    per_page: number;
    total_data: number;
    results: RecipeVersion[];
}

export interface CreateRecipeVersionRequest {
    data_snapshot: Record<string, any>;
    version_number: string;
    recipe: string;
}