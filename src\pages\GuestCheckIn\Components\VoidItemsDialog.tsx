import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertTriangle, Trash2 } from 'lucide-react';
import { GuestCheckItem } from './types/types';

interface VoidItemsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  items: GuestCheckItem[];
  onVoidItems: (itemIds: string[], reason: string) => void;
}

export const VoidItemsDialog: React.FC<VoidItemsDialogProps> = ({
  open,
  onOpenChange,
  items,
  onVoidItems,
}) => {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [voidReason, setVoidReason] = useState('');

  const activeItems = items.filter(item => !item.voided);

  const handleItemToggle = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleVoidItems = () => {
    if (selectedItems.length === 0 || !voidReason.trim()) return;
    
    onVoidItems(selectedItems, voidReason.trim());
    setSelectedItems([]);
    setVoidReason('');
    onOpenChange(false);
  };

  const formatKES = (value: number): string => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const getSelectedTotal = () => {
    return activeItems
      .filter(item => selectedItems.includes(item.id))
      .reduce((total, item) => {
        const modifiersTotal = item.modifiers?.reduce((sum, mod) => sum + mod.price, 0) || 0;
        return total + ((item.price + modifiersTotal) * item.qty);
      }, 0);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Void Items
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning Message */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-800">Warning</h4>
                <p className="text-sm text-red-700">
                  Voiding items will permanently remove them from the check. This action cannot be undone.
                </p>
              </div>
            </div>
          </div>

          {/* Items List */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Select items to void:</Label>
            <div className="max-h-60 overflow-y-auto border rounded-lg">
              {activeItems.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  No items available to void
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {activeItems.map(item => (
                    <label
                      key={item.id}
                      className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedItems.includes(item.id)
                          ? 'bg-red-50 border border-red-200'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(item.id)}
                        onChange={() => handleItemToggle(item.id)}
                        className="rounded border-gray-300"
                      />
                      
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium">{item.qty}x {item.name}</div>
                            {item.modifiers && item.modifiers.length > 0 && (
                              <div className="text-xs text-gray-600">
                                {item.modifiers.map(mod => `+ ${mod.name}`).join(', ')}
                              </div>
                            )}
                            {item.notes && (
                              <div className="text-xs text-gray-600 italic">Note: {item.notes}</div>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              {formatKES((item.price + (item.modifiers?.reduce((sum, mod) => sum + mod.price, 0) || 0)) * item.qty)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {formatKES(item.price)} each
                            </div>
                          </div>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Selected Items Summary */}
          {selectedItems.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex justify-between items-center">
                <span className="font-medium text-red-800">
                  {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected
                </span>
                <span className="font-bold text-red-800">
                  Total: {formatKES(getSelectedTotal())}
                </span>
              </div>
            </div>
          )}

          {/* Void Reason */}
          <div className="space-y-2">
            <Label htmlFor="voidReason" className="text-sm font-medium">
              Reason for voiding <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="voidReason"
              value={voidReason}
              onChange={(e) => setVoidReason(e.target.value)}
              placeholder="Enter reason for voiding these items..."
              rows={3}
              className="resize-none"
            />
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => {
              setSelectedItems([]);
              setVoidReason('');
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleVoidItems}
            disabled={selectedItems.length === 0 || !voidReason.trim()}
            className="bg-red-600 hover:bg-red-700"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Void {selectedItems.length} Item{selectedItems.length !== 1 ? 's' : ''}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};