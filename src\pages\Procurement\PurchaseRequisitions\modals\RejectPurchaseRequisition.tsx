import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useRejectPurchaseRequisitionMutation } from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface RejectPurchaseRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
  requisitionId: number;
  requisitionNumber?: string;
}

const RejectPurchaseRequisition = ({ 
  isOpen, 
  onClose, 
  requisitionId, 
  requisitionNumber 
}: RejectPurchaseRequisitionProps) => {
  const [reason, setReason] = useState("");
  const [rejectPurchaseRequisition, { isLoading: rejecting }] = useRejectPurchaseRequisitionMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }

    try {
      await rejectPurchaseRequisition({ 
        id: requisitionId, 
        reason: reason.trim() 
      }).unwrap();
      
      toast.success("Purchase requisition rejected successfully");
      onClose();
      setReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject purchase requisition");
    }
  };

  const handleClose = () => {
    if (!rejecting) {
      setReason("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Reject Purchase Requisition
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-800">
              You are about to reject Purchase Requisition{" "}
              <span className="font-semibold">
                {requisitionNumber || `PR-${String(requisitionId).padStart(4, '0')}`}
              </span>
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="reason">
                Reason for Rejection <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Please provide a clear reason for rejecting this purchase requisition..."
                rows={4}
                disabled={rejecting}
                className="resize-none"
              />
              <p className="text-xs text-gray-500">
                This reason will be visible to the requester and can help them improve future submissions.
              </p>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={rejecting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="destructive"
                disabled={rejecting || !reason.trim()}
              >
                {rejecting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Reject Purchase Requisition
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RejectPurchaseRequisition;
