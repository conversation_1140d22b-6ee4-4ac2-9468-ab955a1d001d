export interface RecipeIngredient {
    id: number;
    quantity: string;
    cost_per_unit: string | null;
    total_cost: string | null;
    recipe: number;
    unit_of_measure: number;
    store: string | null;
}

export interface RecipeIngredientApiResponse {
    current_page: number;
    per_page: number;
    total_data: number;
    results: RecipeIngredient[];
}

export interface CreateRecipeIngredientRequest {
    quantity: string;
    cost_per_unit: string | null;
    total_cost: string | null;
    recipe: number;
    unit_of_measure: number;
    store: string | null;
}