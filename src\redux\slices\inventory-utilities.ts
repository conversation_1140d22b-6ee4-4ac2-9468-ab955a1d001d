import { IU_Supplier, IU_TaxType, IU_StoreResponse, IU_TaxTypeResponse, IU_SupplierResponse  } from "@/pages/inventory/types/inventory-utilities.types";
import { apiSlice } from "../apiSlice";


export const inventoryUtilitiesApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getInventoryUtilityStores: builder.query<IU_StoreResponse, { params: {} }>({
            query: (params) => ({
                url: `/setup/stores`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<IU_StoreResponse>) => response.data,
        }),
        getInventoryUtilityBranch: builder.query<any, { params: {} }>({
            query: (params) => ({
                url: `/setup/branches`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<any>) => response.data,
        }),
        getInventoryUtilityTaxClass: builder.query<IU_TaxTypeResponse, { params: {} }>({
            query: (params) => ({
                url: `/finance/tax-types`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<IU_TaxTypeResponse>) => response.data,
        }),
        getInventoryUtilitySuppliers: builder.query<IU_SupplierResponse, { params: {} }>({
            query: (params) => ({
                url: `/setup/suppliers`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<IU_SupplierResponse>) => response.data,
        }),
        
    })
})

export const {
    useGetInventoryUtilityStoresQuery,
    useGetInventoryUtilityBranchQuery,
    useGetInventoryUtilityTaxClassQuery,
    useGetInventoryUtilitySuppliersQuery,
} = inventoryUtilitiesApiSlice

interface ApiResponse<T> {
    data: T;
    message: string;
}
