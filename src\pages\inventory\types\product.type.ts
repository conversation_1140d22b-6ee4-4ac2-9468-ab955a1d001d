export interface ProductItem {
    id: number;
    name: string;
    code: string;
    description: string | null;
    unit_price: string;
    perishable: boolean | null;
    shelf_life_days: number | null;
    is_active: boolean | null;
    sub_category: string;
    tax_class: number | null;
    default_supplier: string | null;
    unit_of_measure: number | null;
    default_store: string | null;
}

export interface ProductApiResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: ProductItem[];
}

export interface CreateProductItemRequest {
    name: string;
    code: string;
    description: string | null;
    unit_price: string;
    perishable: boolean | null;
    shelf_life_days: number | null;
    is_active: boolean | null;
    sub_category: string;
    tax_class: number | null;
    default_supplier: string | null;
    unit_of_measure: number | null;
    default_store: string | null;
}
