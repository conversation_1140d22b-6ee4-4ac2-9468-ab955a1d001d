export interface GuestCheck {
  id: string;
  checkNumber: string;
  tableNumber?: string;
  customerName?: string;
  items: CheckItem[];
  subtotal: number;
  taxes: number;
  serviceCharge: number;
  total: number;
  status: 'open' | 'paid' | 'partial' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  workstationId: string;
  revenueCenterId: string;
  serverId: string;
  payments: Payment[];
  balanceDue: number;
}

export interface CheckItem {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
  category: string;
  modifiers?: string[];
}

export interface Payment {
  id: string;
  checkId: string;
  amount: number;
  paymentType: PaymentType;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  reference?: string; // Mpesa code, card reference, etc.
  tip?: number;
  change?: number;
  note?: string;
  customerInfo?: CustomerInfo;
  invoiceNumber?: string;
  createdAt: Date;
  processedBy: string;
  glAccount: string;
}

export type PaymentType = 'cash' | 'mpesa' | 'card' | 'voucher' | 'credit_note' | 'gift_card';

export interface CustomerInfo {
  name?: string;
  email?: string;
  phone?: string;
  loyaltyNumber?: string;
  membershipId?: string;
}

export interface PaymentMethod {
  type: PaymentType;
  label: string;
  icon: string;
  glAccount: string;
  requiresReference: boolean;
  allowsPartial: boolean;
  allowsChange: boolean;
}

export interface Receipt {
  id: string;
  checkId: string;
  type: 'payment' | 'refund';
  format: 'print' | 'email' | 'qr';
  content: string;
  generatedAt: Date;
}

export interface WorkStation {
  id: string;
  name: string;
  location: string;
  isActive: boolean;
}

export interface RevenueCenter {
  id: string;
  name: string;
  code: string;
  glAccount: string;
}