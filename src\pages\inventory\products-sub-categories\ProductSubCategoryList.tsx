import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pencil, Trash2, Plus, Search, Package, ArrowUpDown, ArrowUp, ArrowDown, Filter, X } from 'lucide-react';
import {
  useGetProductSubCategoryQuery,
  useDeleteProductSubCategoryMutation
} from '@/redux/slices/product-sub-category';
import { useGetProductMainCategoryQuery } from '@/redux/slices/product-main-category';
import { ProductSubCategoryItem } from '../types/product-sub-category.type';

interface ProductSubCategoryListProps {
  onEdit: (item: ProductSubCategoryItem) => void;
  onAdd: () => void;
}

type SortField = 'name' | 'code' | 'main_category' | 'id';
type SortDirection = 'asc' | 'desc';

export const ProductSubCategoryList: React.FC<ProductSubCategoryListProps> = ({
  onEdit,
  onAdd
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [nameFilter, setNameFilter] = useState('');
  const [codeFilter, setCodeFilter] = useState('');
  const [mainCategoryFilter, setMainCategoryFilter] = useState('');
  const [showFilters, setShowFilters] = useState(true);
  const [deleteItem] = useDeleteProductSubCategoryMutation();

  const { data, isLoading, error } = useGetProductSubCategoryQuery({ params: {} });
  const { data: mainCategories } = useGetProductMainCategoryQuery({ params: {} });

  const getMainCategoryName = (mainCategoryId: string) => {
    const category = mainCategories?.results?.find(cat => cat.id.toString() === mainCategoryId);
    return category ? category.name : mainCategoryId;
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this sub category?')) {
      try {
        await deleteItem(id).unwrap();
      } catch (err) {
        console.error('Failed to delete sub category:', err);
      }
    }
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  const clearFilters = () => {
    setSearchTerm('');
    setNameFilter('');
    setCodeFilter('');
    setMainCategoryFilter('');
    setSortField('name');
    setSortDirection('asc');
  };

  const hasActiveFilters = searchTerm || nameFilter || codeFilter || mainCategoryFilter || sortField !== 'name' || sortDirection !== 'asc';

  const filteredAndSortedItems = useMemo(() => {
    let filtered = data?.results?.filter(item => {
      const matchesSearch = !searchTerm ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        getMainCategoryName(item.main_category).toLowerCase().includes(searchTerm.toLowerCase());

      const matchesName = !nameFilter || item.name.toLowerCase().includes(nameFilter.toLowerCase());
      const matchesCode = !codeFilter || item.code.toLowerCase().includes(codeFilter.toLowerCase());
      const matchesMainCategory = !mainCategoryFilter || item.main_category === mainCategoryFilter;

      return matchesSearch && matchesName && matchesCode && matchesMainCategory;
    }) || [];

    // Sort the filtered results
    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'code':
          aValue = a.code.toLowerCase();
          bValue = b.code.toLowerCase();
          break;
        case 'main_category':
          aValue = getMainCategoryName(a.main_category).toLowerCase();
          bValue = getMainCategoryName(b.main_category).toLowerCase();
          break;
        case 'id':
          aValue = a.id;
          bValue = b.id;
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [data?.results, searchTerm, nameFilter, codeFilter, mainCategoryFilter, sortField, sortDirection, mainCategories]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2 text-gray-600">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
            Loading sub categories...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Failed to load sub categories</p>
            <p className="text-sm text-gray-500 mt-1">Please try again later</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-5'>
      <div >
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search sub categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 focus-visible:ring-0"
          />
        </div>

        <div className="mt-4 p-4 rounded-lg border">
          <div className="flex flex-col md:flex-row gap-4 w-full">
            <div className="flex-1 ">
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Filter by Name
              </label>
              <Input
                placeholder="Filter by name..."
                value={nameFilter}
                onChange={(e) => setNameFilter(e.target.value)}
              />
            </div>
            <div className="flex-1 min-w-48">
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Filter by Code
              </label>
              <Input
                placeholder="Filter by code..."
                value={codeFilter}
                onChange={(e) => setCodeFilter(e.target.value)}
              />
            </div>
            <div className="flex-1 min-w-48">
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Filter by Main Category
              </label>
              <Select value={mainCategoryFilter} onValueChange={setMainCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All main categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All main categories</SelectItem>
                  {mainCategories?.results?.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-48">
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Sort by
              </label>
              <Select value={sortField} onValueChange={(value: SortField) => setSortField(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="code">Code</SelectItem>
                  <SelectItem value="main_category">Main Category</SelectItem>
                  <SelectItem value="id">ID</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-32">
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Direction
              </label>
              <Select value={sortDirection} onValueChange={(value: SortDirection) => setSortDirection(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="">
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Action
              </label>
              <Button
                variant="outline"
                onClick={clearFilters}
                className={`flex items-center w-full gap-2 py-[19px] ${!hasActiveFilters as boolean ? 'cursor-not-allowed' : 'cursor-pointer'}`}
              >
                <X className="h-4 w-4" />
                Clear
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div>
        {filteredAndSortedItems.length === 0 ? (
          <div className="flex flex-col items-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {hasActiveFilters ? 'No sub categories found' : 'No sub categories yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {hasActiveFilters
                ? 'Try adjusting your search terms or filters'
                : 'Get started by creating your first product sub category'
              }
            </p>
            {!hasActiveFilters && (
              <Button onClick={onAdd} className="flex items-center gap-2 w-fit">
                <Plus className="h-4 w-4" />
                Add First Sub Category
              </Button>
            )}
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('name')}
                      className="h-auto p-0 font-semibold hover:bg-transparent !px-0"
                    >
                      Name {getSortIcon('name')}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('code')}
                      className="h-auto p-0 font-semibold hover:bg-transparent !px-0"
                    >
                      Code {getSortIcon('code')}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('main_category')}
                      className="h-auto p-0 font-semibold hover:bg-transparent !px-0"
                    >
                      Main Category {getSortIcon('main_category')}
                    </Button>
                  </TableHead>
                  <TableHead className='font-bold'>Description</TableHead>
                  <TableHead className="text-right font-bold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">
                      {item.name}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="font-mono pt-1.5 pb-0 px-3">
                        {item.code}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {getMainCategoryName(item.main_category)}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <div className="truncate">
                        {item.description || (
                          <span className="text-gray-400 italic">No description</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit(item)}
                          className="h-8 w-8 p-0"
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item.id)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
};