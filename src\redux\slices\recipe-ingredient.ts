
import { CreateRecipeIngredientRequest, RecipeIngredient, RecipeIngredientApiResponse } from "@/pages/inventory/types/recipe-ingredient.type";
import { apiSlice } from "../apiSlice";

export const recipeIngredientApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getRecipeIngredient: builder.query<RecipeIngredientApiResponse, {params:{}}>({
            query: (params) => ({
                url: `/menu/recipe-ingredients`,
                method: "GET",
                params: params
            }),
            transformResponse: (response: ApiResponse<RecipeIngredientApiResponse>) => response.data,
            providesTags: ["/menu/recipe-ingredients"],
        }),
        updateRecipeIngredient: builder.mutation<RecipeIngredient, { id: number; body: Partial<CreateRecipeIngredientRequest> }>({
            query: ({ id, body }) => ({
                url: `/menu/recipe-ingredients/${id}`,
                method: "PATCH",
                body: body,
            }),
            invalidatesTags: ["/menu/recipe-ingredients"],
        }),
        createRecipeIngredient: builder.mutation<RecipeIngredient, CreateRecipeIngredientRequest>({
            query: (body) => ({
                url: `/menu/recipe-ingredients`,
                method: "POST",
                body: body,
            }),
            invalidatesTags: ["/menu/recipe-ingredients"],
        }),
        getOneRecipeIngredient: builder.query<RecipeIngredient, { id: number, params: {} }>({
            query: ({ id, params }) => ({
                url: `/menu/recipe-ingredients/${id}`,
                method: "GET",
                params: params
            }),
            providesTags: ["/menu/recipe-ingredients"],
        }),
        deleteRecipeIngredient: builder.mutation<void, number>({
            query: (id) => ({
                url: `/menu/recipe-ingredients/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["/menu/recipe-ingredients"],
        }),
    })
})

export const {
    useCreateRecipeIngredientMutation,
    useGetRecipeIngredientQuery,
    useUpdateRecipeIngredientMutation,
    useGetOneRecipeIngredientQuery,
    useDeleteRecipeIngredientMutation,
} = recipeIngredientApiSlice

interface ApiResponse<T> {
  data: T;
  message: string;
}